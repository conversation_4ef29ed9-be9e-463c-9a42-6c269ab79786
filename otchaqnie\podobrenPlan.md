# Детайлен архитектурен план за RAG система

## Фаза 1: Подготовка и структура на проекта

### Задача 1.1: Създаване на структура на проекта за RAG система

#### Описание
Създаване на основната структура на проекта за RAG система, която ще включва всички необходими директории, файлове и конфигурации.

#### Стъпки
1. **Създаване на директории за различните компоненти:**
   - Създайте главна директория `rag-system/`
   - В нея създайте следните поддиректории:
     - `crawlers/` - за скриптове за краулинг
     - `processors/` - за ETL процеси и обработка на данни
     - `database/` - за SQL скриптове и схеми на базата данни
     - `embeddings/` - за генериране и управление на ембединги
     - `mcp_server/` - за MCP сървър имплементация
     - `tests/` - за тестови скриптове
     - `config/` - за конфигурационни файлове
     - `docs/` - за документация
     - `security/` - за управление на криптиране и сигурност
     - `monitoring/` - за наблюдение и алерти

2. **Инициализиране на основните файлове:**
   - Създайте `README.md` във всяка директория с описание на предназначението ѝ
   - Създайте `main.py` в главната директория като входна точка на приложението
   - Създайте `requirements.txt` за Python зависимости
   - Създайте `.gitignore` файл за игнориране на временни файлове и директории
   - Създайте `setup.py` за инсталация на проекта като пакет
   - Създайте `__init__.py` във всяка директория за правилно импортиране
   - Създайте `docker-compose.yml` за контейнеризация на приложението

3. **Настройка на конфигурационни файлове:**
   - Създайте `config/settings.py` за общи настройки на приложението
   - Създайте `config/database.py` за настройки на връзката с Supabase
   - Създайте `config/crawlers.py` за настройки на краулърите (URLs, честота, правила)
   - Създайте `config/embeddings.py` за настройки на OpenAI API и параметри на ембедингите
   - Създайте `config/mcp_server.py` за настройки на MCP сървъра
   - Създайте `.env.example` файл с примерни стойности на средата
   - Създайте `config/security.py` за управление на криптирането на чувствителни данни

4. **Дефиниране на зависимости:**
   - В `requirements.txt` добавете следните зависимости с конкретни версии:
     - `scrapy==2.8.0` - за краулинг
     - `supabase==1.0.3` - за връзка със Supabase
     - `openai==0.27.8` - за генериране на ембединги
     - `fastapi==0.95.2` - за MCP сървър
     - `uvicorn==0.22.0` - за стартиране на FastAPI сървър
     - `python-dotenv==1.0.0` - за зареждане на .env файлове
     - `beautifulsoup4==4.12.2` - за парсване на HTML
     - `pymupdf==1.22.3` - за извличане на текст от PDF
     - `langdetect==1.0.9` - за детекция на език
     - `fasttext==0.9.2` - за бърза детекция на език
     - `pytest==7.3.1` - за тестване
     - `pgvector==0.1.8` - за работа с векторни ембединги в PostgreSQL
     - `selenium==4.9.1` - за краулинг на JavaScript-базирани страници
     - `playwright==1.33.0` - алтернатива за динамични страници
     - `cryptography==40.0.2` - за криптиране на чувствителни данни
     - `prefect==2.10.8` - за оркестрация на работни потоци
     - `prometheus-client==0.16.0` - за метрики и наблюдение

5. **Създаване на базови класове и интерфейси:**
   - Създайте `crawlers/base_crawler.py` с абстрактен клас `BaseCrawler`
   - Създайте `processors/base_processor.py` с абстрактен клас `BaseProcessor`
   - Създайте `database/db_manager.py` с клас `DatabaseManager` за връзка с Supabase
   - Създайте `embeddings/embedder.py` с клас `Embedder` за генериране на ембединги
   - Създайте `mcp_server/server.py` с основен клас `MCPServer`
   - Създайте `security/crypto_manager.py` с клас `CryptoManager` за управление на криптирането

6. **Инициализиране на Git репозитори:**
   - Инициализирайте Git репозитори с `git init`
   - Направете първоначален commit с основната структура
   - Създайте `CONTRIBUTING.md` с инструкции за допринасяне към проекта
   - Създайте `LICENSE` файл с избран лиценз (например MIT)

#### Валидация
1. **Проверка на структурата на директориите:**
   - Изпълнете `find rag-system -type d | sort` и проверете дали всички директории са създадени
   - Уверете се, че всяка директория има правилните поддиректории

2. **Валидация на основните файлове:**
   - Проверете дали всички основни файлове са създадени с `find rag-system -type f | sort`
   - Уверете се, че README.md файловете съдържат подходящо описание
   - Проверете дали .gitignore съдържа всички необходими шаблони

3. **Тестване на конфигурационните файлове:**
   - Опитайте се да заредите настройките с `python -c "from config import settings; print(settings.__dict__)"`
   - Проверете дали .env файлът се зарежда правилно

4. **Проверка на зависимостите:**
   - Създайте виртуална среда с `python -m venv venv`
   - Активирайте я с `source venv/bin/activate` (Linux/Mac) или `venv\Scripts\activate` (Windows)
   - Инсталирайте зависимостите с `pip install -r requirements.txt`
   - Уверете се, че всички пакети се инсталират без грешки

### Задача 1.2: Настройка на Supabase проект и инициализация на базата данни

#### Описание
Създаване и настройка на Supabase проект, активиране на необходимите разширения и инициализация на базата данни.

#### Стъпки
1. **Създаване на Supabase проект:**
   - Регистрирайте се в Supabase (https://supabase.com/)
   - Създайте нов проект с име "rag-system"
   - Запазете URL и API ключове в `.env` файла
   - Използвайте `security/crypto_manager.py` за криптиране на API ключовете

2. **Активиране на необходимите разширения:**
   - Активирайте pgvector разширение чрез SQL редактора:
     ```sql
     CREATE EXTENSION IF NOT EXISTS vector;
     ```
   - Активирайте pg_cron разширение за планирани задачи:
     ```sql
     CREATE EXTENSION IF NOT EXISTS pg_cron;
     ```
   - Активирайте pg_stat_statements за наблюдение на производителността:
     ```sql
     CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
     ```

3. **Създаване на схема на базата данни:**
   - Създайте файл `database/schema.sql` със следното съдържание:
     ```sql
     -- Бронзов слой (сурови данни)
     CREATE TABLE raw_pages (
       id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
       url TEXT NOT NULL,
       html_content TEXT,
       fetch_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
       metadata JSONB,
       is_processed BOOLEAN DEFAULT FALSE
     );

     CREATE TABLE raw_documents (
       id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
       url TEXT NOT NULL,
       file_data BYTEA,
       file_type TEXT,
       fetch_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
       metadata JSONB,
       is_processed BOOLEAN DEFAULT FALSE
     );

     -- Сребърен слой (обработени данни)
     CREATE TABLE clean_pages (
       id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
       raw_page_id UUID REFERENCES raw_pages(id),
       content TEXT NOT NULL,
       language TEXT,
       content_hash TEXT UNIQUE,
       processed_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
       metadata JSONB
     );

     CREATE TABLE documents_staging (
       id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
       raw_document_id UUID REFERENCES raw_documents(id),
       content TEXT NOT NULL,
       language TEXT,
       content_hash TEXT UNIQUE,
       processed_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
       metadata JSONB
     );

     -- Златен слой (крайни данни)
     CREATE TABLE documents (
       id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
       name TEXT,
       source TEXT,
       created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
       metadata JSONB
     );

     CREATE TABLE document_sections (
       id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
       document_id UUID REFERENCES documents(id),
       content TEXT NOT NULL,
       section_index INTEGER,
       section_type VARCHAR(20), -- heading/paragraph/list/table
       embedding VECTOR(1536),
       embedding_model TEXT,
       similarity_score FLOAT,
       created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
     );

     -- Индекси
     CREATE INDEX idx_raw_pages_url ON raw_pages(url);
     CREATE INDEX idx_raw_pages_processed ON raw_pages(is_processed);
     CREATE INDEX idx_raw_documents_url ON raw_documents(url);
     CREATE INDEX idx_raw_documents_processed ON raw_documents(is_processed);
     CREATE INDEX idx_clean_pages_content_hash ON clean_pages(content_hash);
     CREATE INDEX idx_documents_staging_content_hash ON documents_staging(content_hash);
     CREATE INDEX idx_document_sections_document_id ON document_sections(document_id);
     
     -- Векторен индекс за бързо търсене
     CREATE INDEX idx_document_sections_embedding ON document_sections USING ivfflat (embedding vector_cosine_ops)
     WITH (lists = 100);
     ```

4. **Създаване на функции и тригери:**
   - Създайте файл `database/functions.sql` със следното съдържание:
     ```sql
     -- Функция за дедупликация
     CREATE OR REPLACE FUNCTION check_duplicate_content()
     RETURNS TRIGGER AS $$
     BEGIN
       IF EXISTS (
         SELECT 1 FROM clean_pages WHERE content_hash = NEW.content_hash AND id != NEW.id
       ) THEN
         RETURN NULL;
       END IF;
       RETURN NEW;
     END;
     $$ LANGUAGE plpgsql;

     -- Тригер за дедупликация
     CREATE TRIGGER prevent_duplicate_content
     BEFORE INSERT ON clean_pages
     FOR EACH ROW
     EXECUTE FUNCTION check_duplicate_content();
     
     -- Функция за маркиране на обработени сурови страници
     CREATE OR REPLACE FUNCTION mark_raw_page_processed()
     RETURNS TRIGGER AS $$
     BEGIN
       UPDATE raw_pages SET is_processed = TRUE WHERE id = NEW.raw_page_id;
       RETURN NEW;
     END;
     $$ LANGUAGE plpgsql;
     
     -- Тригер за маркиране на обработени сурови страници
     CREATE TRIGGER mark_page_processed
     AFTER INSERT ON clean_pages
     FOR EACH ROW
     EXECUTE FUNCTION mark_raw_page_processed();
     
     -- Планирана задача за ETL процеси
     SELECT cron.schedule('nightly-etl', '0 3 * * *', $$
       -- Извикване на ETL процеси
       UPDATE raw_pages SET is_processed = FALSE WHERE id IN (
         SELECT id FROM raw_pages 
         ORDER BY fetch_time DESC 
         LIMIT 100
       );
     $$);
     
     -- Функция за изчисляване на косинусно подобие
     CREATE OR REPLACE FUNCTION cosine_similarity(a vector, b vector)
     RETURNS float AS $$
     BEGIN
       RETURN 1 - (a <=> b);
     END;
     $$ LANGUAGE plpgsql IMMUTABLE;
     ```

5. **Създаване на Python скрипт за инициализация на базата данни:**
   - Създайте файл `database/init_db.py` със следното съдържание:
     ```python
     import os
     from dotenv import load_dotenv
     from supabase import create_client
     from security.crypto_manager import CryptoManager

     load_dotenv()

     # Декриптиране на API ключове
     crypto_manager = CryptoManager()
     supabase_url = os.getenv("SUPABASE_URL")
     encrypted_key = os.getenv("SUPABASE_SERVICE_KEY_ENCRYPTED")
     supabase_key = crypto_manager.decrypt(encrypted_key) if encrypted_key else os.getenv("SUPABASE_SERVICE_KEY")

     supabase = create_client(supabase_url, supabase_key)

     def init_database():
         # Четене на SQL файловете
         with open("database/schema.sql", "r") as f:
             schema_sql = f.read()
         
         with open("database/functions.sql", "r") as f:
             functions_sql = f.read()
         
         # Изпълнение на SQL заявките
         supabase.table("raw_pages").select("*").limit(1).execute()
         
         # Изпълнение на SQL скриптовете чрез REST API
         # Забележка: В реална ситуация бихте използвали SQL редактора на Supabase
         # или директна връзка с PostgreSQL за изпълнение на сложни скриптове
         print("Database initialized successfully")
         
         # Проверка на индексите
         print("Verifying database indexes...")
         # Тук бихте добавили код за проверка на индексите
         
         return True

     if __name__ == "__main__":
         init_database()
     ```

6. **Създаване на клас за управление на криптирането:**
   - Създайте файл `security/crypto_manager.py` със следното съдържание:
     ```python
     import os
     from cryptography.fernet import Fernet
     from dotenv import load_dotenv

     load_dotenv()

     class CryptoManager:
         def __init__(self):
             # Генериране или зареждане на ключ за криптиране
             key = os.getenv("ENCRYPTION_KEY")
             if not key:
                 key = Fernet.generate_key().decode()
                 print(f"Generated new encryption key: {key}")
                 print("Add this to your .env file as ENCRYPTION_KEY")
             
             self.cipher = Fernet(key.encode() if isinstance(key, str) else key)
         
         def encrypt(self, text):
             """Криптиране на текст"""
             if not text:
                 return None
             return self.cipher.encrypt(text.encode()).decode()
         
         def decrypt(self, encrypted_text):
             """Декриптиране на текст"""
             if not encrypted_text:
                 return None
             return self.cipher.decrypt(encrypted_text.encode()).decode()
     ```

#### Валидация
1. **Проверка на връзката със Supabase:**
   - Изпълнете `python database/init_db.py` и проверете дали скриптът се изпълнява без грешки
   - Проверете дали таблиците са създадени успешно в Supabase конзолата

2. **Тестване на схемата на базата данни:**
   - Опитайте се да вмъкнете тестови данни в таблицата `raw_pages`
   - Проверете дали индексите работят правилно

3. **Валидация на функциите и тригерите:**
   - Опитайте се да вмъкнете дублиращо се съдържание в таблицата `clean_pages`
   - Проверете дали тригерът за дедупликация работи правилно
   - Проверете дали тригерът за маркиране на обработени страници работи правилно

4. **Тестване на криптирането:**
   - Създайте тестов скрипт `tests/test_crypto.py` за проверка на криптирането
   - Уверете се, че криптирането и декриптирането работят правилно

## Фаза 2: Имплементация на краулинг и ETL процеси

### Задача 2.1: Разработка на краулинг компоненти

#### Описание
Разработка на компоненти за краулинг на уеб страници и изтегляне на документи от целеви сайтове, включително поддръжка на JavaScript-базирани страници.

#### Стъпки
1. **Създаване на базов клас за краулинг:**
   - Създайте файл `crawlers/base_crawler.py` със следното съдържание:
     ```python
     from abc import ABC, abstractmethod
     import logging
     import time
     import random
     from prometheus_client import Counter, Gauge

     # Метрики за наблюдение
     PAGES_CRAWLED = Counter('pages_crawled_total', 'Total number of pages crawled', ['crawler_type', 'status'])
     CRAWL_TIME = Gauge('crawl_time_seconds', 'Time taken to crawl a page', ['crawler_type'])

     class BaseCrawler(ABC):
         def __init__(self, config):
             self.config = config
             self.logger = logging.getLogger(self.__class__.__name__)
             self.user_agents = [
                 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
                 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36'
             ]
             self.crawler_type = self.__class__.__name__
         
         def get_random_user_agent(self):
             """Получаване на случаен User-Agent за избягване на блокиране"""
             return random.choice(self.user_agents)
         
         def add_delay(self):
             """Добавяне на случайно забавяне между заявките"""
             min_delay = self.config.get('min_delay', 1)
             max_delay = self.config.get('max_delay', 5)
             delay = random.uniform(min_delay, max_delay)
             time.sleep(delay)
         
         @abstractmethod
         def crawl(self):
             """Метод за краулинг на целеви сайт"""
             pass
         
         @abstractmethod
         def save_data(self, data):
             """Метод за запазване на данните в базата данни"""
             pass
         
         def run(self):
             """Изпълнение на краулинг процеса"""
             self.logger.info(f"Starting crawling with {self.__class__.__name__}")
             start_time = time.time()
             
             try:
                 data = self.crawl()
                 self.save_data(data)
                 PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status='success').inc(len(data) if isinstance(data, list) else 1)
             except Exception as e:
                 self.logger.error(f"Error during crawling: {str(e)}")
                 PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status='error').inc()
             
             end_time = time.time()
             CRAWL_TIME.labels(crawler_type=self.crawler_type).set(end_time - start_time)
             self.logger.info(f"Finished crawling with {self.__class__.__name__} in {end_time - start_time:.2f} seconds")
     ```

2. **Имплементация на Scrapy spider за HTML страници:**
   - Създайте директория `crawlers/spiders/`
   - Създайте файл `crawlers/spiders/__init__.py`
   - Създайте файл `crawlers/spiders/html_spider.py` със следното съдържание:
     ```python
     import scrapy
     from scrapy.linkextractors import LinkExtractor
     from scrapy.spiders import CrawlSpider, Rule
     import os
     import logging
     from dotenv import load_dotenv
     from supabase import create_client
     from security.crypto_manager import CryptoManager
     import random
     import time

     load_dotenv()

     # Декриптиране на API ключове
     crypto_manager = CryptoManager()
     supabase_url = os.getenv("SUPABASE_URL")
     encrypted_key = os.getenv("SUPABASE_SERVICE_KEY_ENCRYPTED")
     supabase_key = crypto_manager.decrypt(encrypted_key) if encrypted_key else os.getenv("SUPABASE_SERVICE_KEY")

     class HtmlSpider(CrawlSpider):
         name = 'html_spider'
         
         def __init__(self, *args, **kwargs):
             super(HtmlSpider, self).__init__(*args, **kwargs)
             self.start_urls = kwargs.get('start_urls', [])
             self.allowed_domains = kwargs.get('allowed_domains', [])
             self.supabase = create_client(supabase_url, supabase_key)
             self.logger = logging.getLogger(self.__class__.__name__)
             
             # Дефиниране на правила за следване на линкове
             self.rules = (
                 Rule(LinkExtractor(), callback='parse_item', follow=True),
             )
             
             # Добавяне на случайно забавяне
             self.download_delay = random.uniform(1, 3)
         
         def parse_item(self, response):
             # Запазване на HTML съдържанието в базата данни
             data = {
                 'url': response.url,
                 'html_content': response.text,
                 'metadata': {
                     'title': response.css('title::text').get(),
                     'headers': response.headers.to_unicode_dict(),
                     'status': response.status
                 },
                 'is_processed': False
             }
             
             # Проверка за дубликати преди запазване
             try:
                 response = self.supabase.table('raw_pages').select('id').eq('url', data['url']).execute()
                 
                 if not response.data:
                     # Запазване в Supabase
                     self.supabase.table('raw_pages').insert(data).execute()
                     self.logger.info(f"Saved page: {response.url}")
                 else:
                     self.logger.info(f"Duplicate URL found: {response.url}")
             except Exception as e:
                 self.logger.error(f"Error saving page {response.url}: {str(e)}")
     ```

3. **Имплементация на Playwright краулър за JavaScript-базирани страници:**
   - Създайте файл `crawlers/js_crawler.py` със следното съдържание:
     ```python
     import os
     import asyncio
     from playwright.async_api import async_playwright
     from dotenv import load_dotenv
     from supabase import create_client
     import logging
     from security.crypto_manager import CryptoManager
     from .base_crawler import BaseCrawler
     import time

     load_dotenv()

     # Декриптиране на API ключове
     crypto_manager = CryptoManager()
     supabase_url = os.getenv("SUPABASE_URL")
     encrypted_key = os.getenv("SUPABASE_SERVICE_KEY_ENCRYPTED")
     supabase_key = crypto_manager.decrypt(encrypted_key) if encrypted_key else os.getenv("SUPABASE_SERVICE_KEY")

     class JSCrawler(BaseCrawler):
         def __init__(self, config):
             super().__init__(config)
             self.supabase = create_client(supabase_url, supabase_key)
             self.urls = config.get('urls', [])
             self.wait_time = config.get('wait_time', 5)  # Време за изчакване на JavaScript зареждане
         
         async def crawl_page(self, url):
             """Краулинг на една страница с Playwright"""
             async with async_playwright() as p:
                 browser = await p.chromium.launch(headless=True)
                 page = await browser.new_page(user_agent=self.get_random_user_agent())
                 
                 try:
                     self.logger.info(f"Crawling JS page: {url}")
                     await page.goto(url, wait_until='networkidle')
                     await page.wait_for_timeout(self.wait_time * 1000)  # Изчакване в милисекунди
                     
                     # Извличане на HTML след изпълнение на JavaScript
                     html_content = await page.content()
                     title = await page.title()
                     
                     data = {
                         'url': url,
                         'html_content': html_content,
                         'metadata': {
                             'title': title,
                             'js_rendered': True
                         },
                         'is_processed': False
                     }
                     
                     return data
                 except Exception as e:
                     self.logger.error(f"Error crawling JS page {url}: {str(e)}")
                     return None
                 finally:
                     await browser.close()
         
         def crawl(self):
             """Краулинг на всички URL-и"""
             results = []
             
             for url in self.urls:
                 try:
                     # Изпълнение на асинхронния метод в синхронен контекст
                     data = asyncio.run(self.crawl_page(url))
                     if data:
                         results.append(data)
                     
                     # Добавяне на забавяне между заявките
                     self.add_delay()
                 except Exception as e:
                     self.logger.error(f"Error in JS crawling for {url}: {str(e)}")
             
             return results
         
         def save_data(self, data):
             """Запазване на данните в базата данни"""
             for item in data:
                 try:
                     # Проверка за дубликати
                     response = self.supabase.table('raw_pages').select('id').eq('url', item['url']).execute()
                     
                     if not response.data:
                         self.supabase.table('raw_pages').insert(item).execute()
                         self.logger.info(f"Saved JS page: {item['url']}")
                     else:
                         self.logger.info(f"Duplicate JS URL found: {item['url']}")
                 except Exception as e:
                     self.logger.error(f"Error saving JS page {item['url']}: {str(e)}")
     ```

4. **Имплементация на компонент за изтегляне на PDF файлове:**
   - Създайте файл `crawlers/pdf_downloader.py` със следното съдържание:
     ```python
     import requests
     import os
     from dotenv import load_dotenv
     from supabase import create_client
     import logging
     from urllib.parse import urlparse
     from .base_crawler import BaseCrawler
     from security.crypto_manager import CryptoManager

     load_dotenv()

     # Декриптиране на API ключове
     crypto_manager = CryptoManager()
     supabase_url = os.getenv("SUPABASE_URL")
     encrypted_key = os.getenv("SUPABASE_SERVICE_KEY_ENCRYPTED")
     supabase_key = crypto_manager.decrypt(encrypted_key) if encrypted_key else os.getenv("SUPABASE_SERVICE_KEY")

     class PDFDownloader(BaseCrawler):
         def __init__(self, config):
             super().__init__(config)
             self.supabase = create_client(supabase_url, supabase_key)
             self.pdf_urls = config.get('pdf_urls', [])
             self.headers = {
                 'User-Agent': self.get_random_user_agent()
             }
         
         def crawl(self):
             results = []
             for url in self.pdf_urls:
                 try:
                     self.logger.info(f"Downloading PDF from {url}")
                     response = requests.get(url, headers=self.headers, timeout=30)
                     
                     if response.status_code == 200 and 'application/pdf' in response.headers.get('Content-Type', ''):
                         results.append({
                             'url': url,
                             'file_data': response.content,
                             'file_type': 'pdf',
                             'metadata': {
                                 'filename': os.path.basename(urlparse(url
"""
Configurable Text Chunking with Quality Metrics
Supports experimental chunking strategies for optimal RAG performance
"""
import hashlib
import logging
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
from config.settings import settings


@dataclass
class ChunkingStrategy:
    name: str
    chunk_size: int
    chunk_overlap: int
    separators: List[str]
    min_chunk_size: int = 50
    max_chunk_size: int = 2000


class ConfigurableChunker:
    """
    Configurable text chunker with quality metrics
    Supports A/B testing of different chunking strategies
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Load configuration
        if config:
            self.chunk_size = config.get('chunk_size', settings.chunk_size)
            self.chunk_overlap = config.get('chunk_overlap', settings.chunk_overlap)
            self.separators = config.get('separators', ["\n\n", "\n", ".", "!", "?", ";", ",", " ", ""])
        else:
            self.chunk_size = settings.chunk_size
            self.chunk_overlap = settings.chunk_overlap
            self.separators = ["\n\n", "\n", ".", "!", "?", ";", ",", " ", ""]
        
        # Initialize text splitter
        try:
            from langchain.text_splitter import RecursiveCharacterTextSplitter
            
            self.text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=self.chunk_size,
                chunk_overlap=self.chunk_overlap,
                separators=self.separators,
                length_function=len
            )
        except ImportError:
            self.logger.error("langchain not installed. Install with: pip install langchain")
            raise
        
        # Metrics tracking
        self.chunk_metrics = {
            'avg_chunk_size': 0,
            'chunks_too_small': 0,
            'chunks_too_large': 0,
            'total_chunks': 0
        }
        
        # Predefined strategies for A/B testing
        self.strategies = {
            'conservative': ChunkingStrategy(
                name='conservative',
                chunk_size=800,
                chunk_overlap=100,
                separators=["\n\n", "\n", ".", "!", "?", ";", ",", " "]
            ),
            'aggressive': ChunkingStrategy(
                name='aggressive', 
                chunk_size=1200,
                chunk_overlap=200,
                separators=["\n\n", "\n", ".", " "]
            ),
            'semantic': ChunkingStrategy(
                name='semantic',
                chunk_size=1000,
                chunk_overlap=150,
                separators=["\n\n", "\n", ". ", "! ", "? "]  # Preserves sentences
            ),
            'technical': ChunkingStrategy(
                name='technical',
                chunk_size=1500,
                chunk_overlap=300,
                separators=["\n\n", "\n", "```", ".", "!", "?"]  # Good for code/docs
            )
        }
    
    def chunk_document(self, content: str, strategy_name: str = None) -> List[Dict[str, Any]]:
        """
        Chunk document with specified strategy
        Returns list of chunk dictionaries with metadata
        """
        
        if strategy_name and strategy_name in self.strategies:
            return self.chunk_with_strategy(content, strategy_name)
        
        # Use default configuration
        chunks = self.text_splitter.split_text(content)
        
        # Filter chunks by size
        filtered_chunks = [
            chunk for chunk in chunks 
            if 50 <= len(chunk) <= 2000
        ]
        
        # Update metrics
        self._update_metrics(filtered_chunks)
        
        return [
            {
                'content': chunk,
                'chunk_index': i,
                'chunk_size': len(chunk),
                'strategy': 'default',
                'content_hash': hashlib.sha256(chunk.encode()).hexdigest(),
                'quality_score': self._calculate_chunk_quality(chunk),
                'overlap_start': i > 0,
                'overlap_end': i < len(filtered_chunks) - 1
            }
            for i, chunk in enumerate(filtered_chunks)
        ]
    
    def chunk_with_strategy(self, content: str, strategy_name: str) -> List[Dict[str, Any]]:
        """Chunk with specific strategy"""
        
        if strategy_name not in self.strategies:
            raise ValueError(f"Unknown strategy: {strategy_name}")
        
        strategy = self.strategies[strategy_name]
        
        # Create splitter for this strategy
        from langchain.text_splitter import RecursiveCharacterTextSplitter
        
        splitter = RecursiveCharacterTextSplitter(
            chunk_size=strategy.chunk_size,
            chunk_overlap=strategy.chunk_overlap,
            separators=strategy.separators,
            length_function=len
        )
        
        chunks = splitter.split_text(content)
        
        # Filter by strategy limits
        filtered_chunks = [
            chunk for chunk in chunks 
            if strategy.min_chunk_size <= len(chunk) <= strategy.max_chunk_size
        ]
        
        # Update metrics for this strategy
        self._update_metrics(filtered_chunks, strategy_name)
        
        return [
            {
                'content': chunk,
                'chunk_index': i,
                'chunk_size': len(chunk),
                'strategy': strategy_name,
                'content_hash': hashlib.sha256(chunk.encode()).hexdigest(),
                'quality_score': self._calculate_chunk_quality(chunk),
                'overlap_start': i > 0,
                'overlap_end': i < len(filtered_chunks) - 1,
                'strategy_config': {
                    'chunk_size': strategy.chunk_size,
                    'chunk_overlap': strategy.chunk_overlap,
                    'separators': strategy.separators
                }
            }
            for i, chunk in enumerate(filtered_chunks)
        ]
    
    def _calculate_chunk_quality(self, chunk: str) -> float:
        """Calculate quality score for individual chunk"""
        quality_score = 0.0
        
        # Sentence completeness
        if chunk.strip().endswith(('.', '!', '?', ':')):
            quality_score += 0.3
        
        # Information density
        words = chunk.split()
        unique_words = set(word.lower() for word in words)
        if words:
            uniqueness = len(unique_words) / len(words)
            quality_score += min(uniqueness * 0.5, 0.4)
        
        # Structure presence
        if '\n' in chunk:  # Has paragraphs
            quality_score += 0.2
        
        # Factual content (numbers/data)
        import re
        if re.search(r'\d', chunk):
            quality_score += 0.1
        
        return min(quality_score, 1.0)
    
    def _update_metrics(self, chunks: List[str], strategy_name: str = 'default'):
        """Update chunking metrics"""
        if not chunks:
            return
        
        chunk_sizes = [len(chunk) for chunk in chunks]
        
        metrics = {
            'avg_chunk_size': sum(chunk_sizes) / len(chunk_sizes),
            'total_chunks': len(chunks),
            'chunks_too_small': sum(1 for size in chunk_sizes if size < 100),
            'chunks_too_large': sum(1 for size in chunk_sizes if size > self.chunk_size * 1.5),
            'min_size': min(chunk_sizes),
            'max_size': max(chunk_sizes),
            'size_variance': sum((size - sum(chunk_sizes)/len(chunk_sizes))**2 for size in chunk_sizes) / len(chunk_sizes)
        }
        
        self.chunk_metrics[strategy_name] = metrics
        self.logger.info(f"Chunking metrics for {strategy_name}: {metrics}")
    
    def get_metrics(self, strategy_name: str = 'default') -> Dict[str, Any]:
        """Get metrics for specific strategy"""
        return self.chunk_metrics.get(strategy_name, {})
    
    def get_all_metrics(self) -> Dict[str, Dict[str, Any]]:
        """Get all chunking metrics"""
        return self.chunk_metrics
    
    def compare_strategies(self, content: str, strategies: List[str] = None) -> Dict[str, Any]:
        """
        Compare different chunking strategies on the same content
        Returns comparison metrics
        """
        
        if strategies is None:
            strategies = list(self.strategies.keys())
        
        results = {}
        
        for strategy_name in strategies:
            if strategy_name in self.strategies:
                chunks = self.chunk_with_strategy(content, strategy_name)
                
                results[strategy_name] = {
                    'chunk_count': len(chunks),
                    'avg_quality': sum(chunk['quality_score'] for chunk in chunks) / len(chunks) if chunks else 0,
                    'metrics': self.get_metrics(strategy_name),
                    'sample_chunk': chunks[0]['content'][:200] + "..." if chunks else ""
                }
        
        # Determine best strategy based on quality and chunk count balance
        best_strategy = None
        best_score = 0
        
        for strategy_name, result in results.items():
            # Combined score: quality * efficiency
            efficiency = 1.0 / (result['chunk_count'] / 10) if result['chunk_count'] > 0 else 0
            combined_score = result['avg_quality'] * 0.7 + efficiency * 0.3
            
            if combined_score > best_score:
                best_score = combined_score
                best_strategy = strategy_name
        
        return {
            'results': results,
            'best_strategy': best_strategy,
            'best_score': best_score,
            'recommendation': f"Use '{best_strategy}' strategy for optimal balance of quality and efficiency"
        }


class SemanticChunker:
    """
    Advanced semantic chunker that preserves meaning boundaries
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def chunk_by_sentences(self, content: str, target_size: int = 1000, overlap: int = 100) -> List[str]:
        """Chunk by sentence boundaries to preserve semantic meaning"""
        
        import re
        
        # Split into sentences
        sentences = re.split(r'(?<=[.!?])\s+', content)
        
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            # Check if adding this sentence would exceed target size
            if len(current_chunk) + len(sentence) > target_size and current_chunk:
                chunks.append(current_chunk.strip())
                
                # Start new chunk with overlap
                if overlap > 0:
                    words = current_chunk.split()
                    overlap_words = words[-overlap//10:] if len(words) > overlap//10 else words
                    current_chunk = " ".join(overlap_words) + " " + sentence
                else:
                    current_chunk = sentence
            else:
                current_chunk += " " + sentence if current_chunk else sentence
        
        # Add final chunk
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def chunk_by_paragraphs(self, content: str, target_size: int = 1000) -> List[str]:
        """Chunk by paragraph boundaries"""
        
        paragraphs = content.split('\n\n')
        chunks = []
        current_chunk = ""
        
        for paragraph in paragraphs:
            if len(current_chunk) + len(paragraph) > target_size and current_chunk:
                chunks.append(current_chunk.strip())
                current_chunk = paragraph
            else:
                current_chunk += "\n\n" + paragraph if current_chunk else paragraph
        
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return chunks

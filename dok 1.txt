За намаляване на халюцинациите на големи езикови модели (LLM) използваме Retrieval-Augmented Generation (RAG), при който LLM динамично извличат релевантна информация от външна база знания преди генериране на отговори. Предложената архитектура автоматично събира (краулинг) данни от целеви сайтове (текст, PDF, таблици), почиства и структурира информацията, и я записва в Supabase (Postgres с векторни колони). По-късно LLM правят заявки към тази база през MCP (Model Context Protocol) сървър, който предоставя необходимия контекст и инструменти. В следващите задачи ще проектираме и реализираме този pipeline подробно.

Задача 1: Определяне на изисквания и избор на технологии. 
Формулирайте обем на данните (например хиляди или милиони страници), езици (предимно български и английски) и честота на обновяване (например ежедневна или по-рядка). Оценете ограниченията на безплатния план на Supabase (например лимитирано пространство) и планирайте скалируемост (възможно добавяне на S3 или платен план). Изберете инструмент за краулинг: препоръчително е да използвате aiohttp за статични страници и Playwright за динамични страници вместо Scrapy/Twisted поради по-лесната интеграция с asyncio. Предвидете прокси/IP ротация за избягване на блокиране, както и спазване на robots.txt. Изберете метод за извличане на съдържание от PDF (препоръчително Unstructured с fallback към pymupdf4llm). Подгответе Supabase: включете pgvector разширението за вектори и pg_cron/pgmq за задачите. В рамките на тази задача дефинирайте и логическа структура (трислойна медалистична архитектура: сурови данни – „бронз", почистени данни – „сребро", крайните документи – „злато"). Валидиране: Проверете, че избрания стек (aiohttp, Playwright, Supabase, pgvector и т.н.) може да бъде инсталиран и инициализиран успешно.

Задача 2: Създаване на краулинг скриптове. 
Реализирайте механизъм за автоматично обхождане на целевите сайтове (напр. eufunds.bg и други) и сваляне на съдържание. Използвайте aiohttp за статични страници и Playwright за динамични страници с JavaScript, като внедрите delay между заявките и ротация на User-Agent, за да оприличите трафика на човешки. Имплементирайте асинхронно програмиране с asyncio за по-добра производителност и управление на конкурентни заявки. За всяка страница записвайте URL, суров HTML код и евентуални свалени PDF/файлове. Пример: таблица raw_pages(id, url, html_content, fetch_time), таблица raw_pdfs(id, url, file_data, fetch_time). Валидиране: Уверете се, че от примерен сайт (напр. eufunds.bg) се връща правилно текстът или PDF-данните (напр. прочетете съдържанието от таблицата и вижте смислен текст).

Задача 3: Съхранение на суровите данни („бронзов слой"). 
Създайте таблици в Supabase/Postgres за суровите данни: например raw_pages за HTML и raw_documents за PDF/документи. Можете да използвате JSONB поле за допълнителни метаданни (източник, заглавие, език). Добавете колони за времеви клейм (when fetched). По желание използвайте Supabase Storage (S3-стил) за по-големи файлове (PDF). Имплементирайте система за мониторинг с Prometheus метрики за следене на процеса на събиране на данни. Валидиране: Вкарайте тестов запис (напр. HTML от малка страница) и прочетете го през SQL, за да потвърдите, че данните са цели и индексите работят.

Задача 4: Парсинг и почистване на данните („сребърен слой"). 
Обработете суровия HTML/документи: с BeautifulSoup или аналози екстрахирайте основния текст, премахнете навигация и ненужни скриптове. За PDF използвайте комбинация от Unstructured и pymupdf4llm, които могат да извлекат текст, таблици и изображения с fallback механизъм при грешки. Разделете големи документи на логични части (напр. параграфи или страници) с интелигентен chunker, който поддържа overlap между секциите. Детектирайте езика с langdetect вместо FastText и филтрирайте нерелевантни езици; може да превеждате автоматично англоезични данни, ако е нужно. Създайте алгоритъм за дедупликация: например генерирайте отпечатък (hash) на всяка статия/документ, за да избегнете повторно съхранение. Ако са открити конфликти (променено съдържание на вече събран документ), логвайте ги за ръчна проверка. Запишете почистения текст в нови таблици като clean_pages или documents_staging. Валидиране: Направете тестово влизане на HTML от една и съща страница два пъти и проверете, че дедупликацията (напр. по отпечатък) не допуска дубликат. Използвайте ръчен преглед или автоматизирани скриптове за QC, за да гарантирате, че филтрираните данни са смислени.

Задача 5: Дефиниране на окончателни таблици („златен слой") с партициониране. 
Спроектрайте крайните таблици за RAG-извличане с партициониране по дати за по-добра производителност. Примерна структура:
documents (id, name, source, created_at) – записва оригиналните документи.
document_sections (id, document_id, content, embedding) – съдържа разделени секции (параграфи/страници) с текст и векторни ембединг колони (pgvector).
Използвайте PARTITION BY RANGE (created_at) за document_sections и създайте функция за автоматично генериране на нови партиции с pg_cron. Към всяка таблица добавете HNSW индекс върху embedding вместо IVFFLAT за по-бързо и точно търсене. Ако е приложимо, включете RLS политики за достъп (от Supabase: всеки „собственик" вижда само своите записи). Валидиране: Вкарайте примерна секция и проверете, че при SELECT заявка с векторно търсене (WHERE embedding <#> query_embedding < threshold) се връща очакван текст. Уверете се, че схемата поддържа лесни JOIN-ове и филтриране (напр. по източник или тема).

Задача 6: Генериране на ембединг и индексиране. 
Настройте автоматично създаване на векторен ембединг при запис на текст. Supabase предлага automatic embeddings: добавете pgvector колона към document_sections и използвайте SQL тригери (pgmq, pg_net, pg_cron) за асинхронно извикване на модел (например OpenAI, HuggingFace) при всяка промяна. Така се гарантира, че ембедингът винаги съответства на текущото съдържание. Индексирайте колоната с HNSW индекс за бързо сходствено търсене. Имплементирайте batch обработка на ембединги за по-добра ефективност и намаляване на API разходите. Валидиране: Вмъкнете нов ред с текст и вижте дали автоматично се запълва ембединг колоната. Изпълнете сходствено търсене с този ембединг и проверете дали селектира този ред.

Задача 7: Реализация на Retrieval (RAG) логика с кеширане. 
Напишете скрипт или микросървис, който приема входна заявка на езика на потребителя, изчислява ембединг на заявката (със същия модел като за документите) и прави векторно търсене в document_sections, връщайки top-K близки секции. Добавете Redis кеширане на резултатите от търсенето за подобряване на производителността и намаляване на натоварването. Опционално добавете и ключово (full-text) търсене по таблицата за допълнително прецизиране. Резултатите се подават към LLM като контекст. Имплементирайте rate limiting за предотвратяване на претоварване на системата. Валидиране: Тествате системата с няколко контролни въпроса за съдържание от базата (например „Какви програми за финансова подкрепа има за земеделците?") и проверете дали върнатите отговори включват релевантни текстове от таблиците. Уверете се, че LLM не измисля несъществуваща информация (проверява се срещу реалните документи).

Задача 8: Разработка на MCP сървър с подобрена производителност. 
Изгответе MCP сървър по спецификацията на Model Context Protocol. Сървърът може да е на Python (FastAPI) или Node.js и да комуникира с LLM чрез STDIO или SSE (според избора). Дефинирайте инструменти (tools) и ресурси (resources): например инструмент search_documents(query) – който връща релевантни секции чрез горния RAG-пайплайн, и ресурс list_programs() – който изброява теми/програми в базата. Добавете Redis кеширане на резултатите, rate limiting и мониторинг на производителността. Уникалните имена на инструментите трябва да предотвратяват конфликти. MCP сървърът, при получен RPC/заявка, ще извършва посочените функции (напр. търсене в базата) и връща резултат към клиента. Валидиране: Симулирайте LLM-заявка през MCP клиента: например изпратете JSON с команда за search_documents("еврофонд за енергийна ефективност") и проверете дали отговорът съдържа точни данни от базата. Убедете се, че сървърът обработва коректно параметри и връща нужните полета (информация и цитати от таблиците).

Задача 9: Оркестрация и планиране с мониторинг. 
Организирайте изпълнението на горните стъпки чрез планиран график (cron jobs или външни инструменти като Prefect). Например задайте ежедневен краул за нови данни и последователна обработка (ETL). Можете да използвате pg_cron за автоматизация в Supabase. Осигурете и механизъм за наблюдение и известяване при грешки (логове и алерти) с Prometheus метрики и Grafana дашборд. Имплементирайте health checks за всички компоненти на системата. Валидиране: Уверете се, че по разписание се стартира обновяване на данните и че новопостъпила информация се появява в базата. Имплементирайте тестова поредица (например малък сайт с фиктивни данни), за да докажете, че системата успешно улавя промени.

Задача 10: Тестове и непрекъснато наблюдение с автоматизация. 
За всяка компонент проверете функционалността чрез тестови сценарии. Например, напишете скриптове, които следят дали нови документи се индексират, търсенията връщат очаквани резултати, а LLM отговаря вярно на въпроси по тях. Интегрирайте проверки за дублирани или липсващи данни. Дайте приоритет на реална валидация с конкретни примери: не разчитайте само на LLM-генерирани тестове. QA примери: изваждане на конкретни цитати от eufunds.bg, проверка дали са налични в document_sections. Автоматизирайте тестовете с CI/CD pipeline. Валидиране: Организирайте регулярен процес (напр. ежедневен) за проверка на данните и резултатите. Отбележете всяка точка като „завършена", след като сте потвърдили, че работи коректно и стабилно.

Така създаденият MCP сървър и RAG pipeline ще позволят на LLM да черпи знание от надеждни актуални източници, значително намалявайки халюцинациите. Основата е ясна стъпка по стъпка реализация, като всеки етап включва задължително валидиране на резултатите.

# Контролен списък за имплементация

## Незабавни действия:
- [ ] Сменете IVFFLAT с HNSW индекс
- [ ] Добавете proper error handling във всички async функции
- [ ] Имплементирайте Redis кеширане
- [ ] Добавете rate limiting
- [ ] Тествайте с малко количество данни първо

## Средносрочни подобрения:
- [ ] Добавете партициониране на таблици
- [ ] Имплементирайте health checks
- [ ] Добавете comprehensive logging
- [ ] Създайте Docker compose файл за development

## Дългосрочни цели:
- [ ] Добавете A/B тестване на различни embedding модели
- [ ] Имплементирайте auto-scaling за краулъри
- [ ] Добавете data lineage tracking
- [ ] Създайте dashboard за мониторинг

# OpenAI Configuration
OPENAI_API_KEY=********************************************************************************************************************************************************************
MODEL_CHOICE=gpt-4o-mini

# Supabase Configuration
SUPABASE_URL=https://jbdpiowmhaxghnzvhxse.supabase.co
SUPABASE_SERVICE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.azR_6VXSMS-oVO9LElqtBKEbQZqKXfFrm3-CsW_sS-o

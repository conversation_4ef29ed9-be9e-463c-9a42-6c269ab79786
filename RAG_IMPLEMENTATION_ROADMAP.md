# RAG Система - Пошагов план за имплементация

## 🎯 Правила за изпълнение
- ✅ **ЗАДЪЛЖИТЕЛНО**: След всяка задача попълнете валидационното квадратче
- ⚠️ **ЗАБРАНЕНО**: Преминаване към следваща задача без 100% валидация
- 🔍 **ИЗИСКВАНЕ**: Всяка задача трябва да бъде тествана и потвърдена че работи
- 📝 **ДОКУМЕНТАЦИЯ**: Запишете точно какво сте тествали и как

---

## 📋 ФАЗА 1: Подготовка и основна структура (Седмица 1)

### Задача 1.1: Създаване на проектна структура
**Цел**: Създаване на всички директории и основни файлове

**Стъпки**:
1. Създайте главна директория `rag-system/`
2. Създайте всички поддиректории според плана
3. Създайте основните файлове (`README.md`, `requirements.txt`, `.gitignore`, etc.)
4. Инициализирайте Git репозитори

**Файлове за създаване**:
```
rag-system/
├── crawlers/
├── processors/
├── database/
├── embeddings/
├── mcp_server/
├── security/
├── monitoring/
├── config/
├── tests/
├── docs/
├── workflows/
├── main.py
├── requirements.txt
├── .env.example
├── .gitignore
└── README.md
```

**Валидация**:
- [ ] Всички директории са създадени
- [ ] Всички основни файлове съществуват
- [ ] Git репозитори е инициализиран
- [ ] `find rag-system -type d | wc -l` връща поне 10 директории
- [ ] `find rag-system -type f | wc -l` връща поне 15 файла

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че структурата е създадена правилно**
- [ ] **Тествал съм с командите по-горе и всичко работи**
- [ ] **НЕ лъжа и НЕ си измислям - всичко е реално направено**

---

### Задача 1.2: Настройка на зависимости
**Цел**: Инсталиране и тестване на всички Python библиотеки

**Стъпки**:
1. Създайте виртуална среда
2. Попълнете `requirements.txt` с актуализираните версии
3. Инсталирайте всички зависимости
4. Тествайте импортирането на ключови библиотеки

**requirements.txt съдържание**:
```
# Основни
scrapy==2.11.0
supabase==2.3.0
fastapi==0.104.1
uvicorn==0.24.0
python-dotenv==1.0.0

# Обработка на данни
beautifulsoup4==4.12.2
pymupdf==1.23.8
langdetect==1.0.9
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Асинхронни операции
aiohttp==3.9.1
playwright==1.40.0
scrapy-asyncer==0.1.0

# AI и ембединги
openai>=1.6.0
pgvector==0.2.4

# Мониторинг и оркестрация
prometheus-client==0.16.0
prefect==2.10.8
tenacity==8.2.2

# Сигурност
cryptography==40.0.2

# Тестване
pytest==7.3.1
pytest-asyncio==0.21.0
```

**Валидация**:
- [ ] Виртуалната среда е създадена и активирана
- [ ] Всички пакети се инсталират без грешки
- [ ] `python -c "import openai; print(openai.__version__)"` работи
- [ ] `python -c "import pydantic; print(pydantic.__version__)"` показва >=2.5.0
- [ ] `python -c "import supabase; print('OK')"` работи
- [ ] `python -c "import fastapi; print('OK')"` работи
- [ ] `playwright install` се изпълнява успешно

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че всички зависимости работят**
- [ ] **Тествал съм всички import команди и няма грешки**
- [ ] **НЕ лъжа и НЕ си измислям - всичко е реално тествано**

---

### Задача 1.3: Настройка на Supabase проект
**Цел**: Създаване и конфигуриране на Supabase база данни

**Стъпки**:
1. Създайте Supabase проект на https://supabase.com/
2. Активирайте pgvector разширение
3. Създайте `.env` файл с credentials
4. Тествайте връзката

**SQL команди за изпълнение**:
```sql
-- Активиране на разширения
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS pg_cron;
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Тестова таблица
CREATE TABLE test_connection (
  id SERIAL PRIMARY KEY,
  message TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

INSERT INTO test_connection (message) VALUES ('Connection test successful');
```

**.env файл**:
```
SUPABASE_URL=your_supabase_url_here
SUPABASE_SERVICE_KEY=your_service_key_here
SUPABASE_ANON_KEY=your_anon_key_here
OPENAI_API_KEY=your_openai_key_here
ENCRYPTION_KEY=your_encryption_key_here
```

**Валидация**:
- [ ] Supabase проект е създаден
- [ ] pgvector разширение е активирано
- [ ] `.env` файл съществува с всички ключове
- [ ] `python -c "from supabase import create_client; import os; from dotenv import load_dotenv; load_dotenv(); client = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_SERVICE_KEY')); print(client.table('test_connection').select('*').execute())"` работи
- [ ] Заявката връща данни от test_connection таблицата

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че Supabase връзката работи**
- [ ] **Тествал съм заявката и получавам данни**
- [ ] **НЕ лъжа и НЕ си измислям - връзката е реална и функционална**

---

## 📋 ФАЗА 2: Базова схема на базата данни (Седмица 1-2)

### Задача 2.1: Създаване на основни таблици
**Цел**: Създаване на всички таблици за Medallion архитектурата

**Стъпки**:
1. Създайте `database/schema.sql`
2. Изпълнете SQL скрипта в Supabase
3. Проверете че всички таблици са създадени
4. Тествайте основни CRUD операции

**database/schema.sql**:
```sql
-- Бронзов слой (сурови данни)
CREATE TABLE raw_pages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  url TEXT NOT NULL,
  html_content TEXT,
  fetch_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB,
  is_processed BOOLEAN DEFAULT FALSE
);

CREATE TABLE raw_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  url TEXT NOT NULL,
  file_path TEXT,
  file_type TEXT,
  storage_bucket TEXT DEFAULT 'documents',
  fetch_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB,
  is_processed BOOLEAN DEFAULT FALSE
);

-- Сребърен слой (обработени данни)
CREATE TABLE clean_pages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  raw_page_id UUID REFERENCES raw_pages(id),
  content TEXT NOT NULL,
  language TEXT,
  content_hash TEXT UNIQUE,
  processed_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);

-- Златен слой (крайни данни)
CREATE TABLE documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT,
  source TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);

CREATE TABLE document_sections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID REFERENCES documents(id),
  content TEXT NOT NULL,
  content_tsv TSVECTOR,
  content_hash TEXT UNIQUE,
  section_index INTEGER,
  section_type VARCHAR(20),
  embedding VECTOR(1536),
  embedding_model TEXT DEFAULT 'text-embedding-3-small',
  language TEXT DEFAULT 'en',
  is_embedded BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Валидация**:
- [ ] Всички таблици са създадени без грешки
- [ ] `SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'` показва всички таблици
- [ ] `INSERT INTO raw_pages (url) VALUES ('https://test.com')` работи
- [ ] `SELECT * FROM raw_pages` връща тестовия запис
- [ ] `DELETE FROM raw_pages WHERE url = 'https://test.com'` работи

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че всички таблици са създадени правилно**
- [ ] **Тествал съм CRUD операции и всичко работи**
- [ ] **НЕ лъжа и НЕ си измислям - таблиците са реални и функционални**

---

### Задача 2.2: Създаване на индекси и функции
**Цел**: Оптимизиране на базата данни с индекси и stored procedures

**Стъпки**:
1. Създайте `database/indexes.sql`
2. Създайте `database/functions.sql`
3. Изпълнете скриптовете
4. Тествайте производителността

**database/indexes.sql**:
```sql
-- Основни индекси
CREATE INDEX idx_raw_pages_url ON raw_pages(url);
CREATE INDEX idx_raw_pages_processed ON raw_pages(is_processed);
CREATE INDEX idx_raw_documents_url ON raw_documents(url);
CREATE INDEX idx_clean_pages_content_hash ON clean_pages(content_hash);
CREATE INDEX idx_document_sections_content_hash ON document_sections(content_hash);
CREATE INDEX idx_document_sections_embedded ON document_sections(is_embedded);

-- HNSW индекс за векторно търсене
CREATE INDEX idx_document_sections_embedding ON document_sections
USING hnsw (embedding vector_cosine_ops)
WITH (m = 32, ef_construction = 128);

-- GIN индекс за текстово търсене
CREATE INDEX idx_document_sections_tsv ON document_sections USING GIN(content_tsv);
```

**Валидация**:
- [ ] Всички индекси са създадени без грешки
- [ ] `SELECT indexname FROM pg_indexes WHERE tablename = 'document_sections'` показва всички индекси
- [ ] `EXPLAIN SELECT * FROM raw_pages WHERE url = 'test'` използва индекс
- [ ] Няма грешки при създаване на HNSW индекс

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че всички индекси работят правилно**
- [ ] **Тествал съм EXPLAIN заявки и индексите се използват**
- [ ] **НЕ лъжа и НЕ си измислям - индексите са реални и оптимизират заявките**

---

## 📋 ФАЗА 3: Базови конфигурационни класове (Седмица 2)

### Задача 3.1: Pydantic конфигурационни модели
**Цел**: Създаване на type-safe конфигурация с Pydantic V2

**Стъпки**:
1. Създайте `config/models.py`
2. Имплементирайте всички конфигурационни класове
3. Тествайте валидацията
4. Тествайте зареждането от .env

**config/models.py**:
```python
from pydantic import BaseModel, Field, field_validator, ConfigDict
from pydantic_settings import BaseSettings
from typing import List, Optional, Literal

class CrawlerConfig(BaseSettings):
    model_config = ConfigDict(env_file='.env', env_prefix='CRAWLER_')

    start_urls: List[str] = Field(default=[])
    allowed_domains: List[str] = Field(default=[])
    download_delay: float = 2.0
    concurrent_requests: int = 8
    max_pages: Optional[int] = None

    @field_validator('concurrent_requests')
    @classmethod
    def validate_concurrent_requests(cls, v):
        if v < 1 or v > 32:
            raise ValueError('concurrent_requests must be between 1 and 32')
        return v

class DatabaseConfig(BaseSettings):
    model_config = ConfigDict(env_file='.env', env_prefix='DB_')

    supabase_url: str
    supabase_service_key: str
    supabase_anon_key: str

class EmbeddingConfig(BaseSettings):
    model_config = ConfigDict(env_file='.env', env_prefix='EMBEDDING_')

    model: str = "text-embedding-3-small"
    batch_size: int = Field(default=20, ge=1, le=100)
    dimensions: int = 1536
    api_key: str
    max_tokens_per_minute: int = Field(default=1000000, ge=10000)

class DocumentSection(BaseModel):
    content: str
    section_index: int
    section_type: Literal["heading", "paragraph", "list", "table", "image", "code"]
    embedding: Optional[List[float]] = None
    content_hash: Optional[str] = None

    @field_validator("content")
    @classmethod
    def validate_content_length(cls, v):
        if len(v) < 10:
            raise ValueError("Section content too short")
        return v
```

**Тестов файл `tests/test_config.py`**:
```python
import pytest
from config.models import CrawlerConfig, DatabaseConfig, EmbeddingConfig, DocumentSection
from pydantic import ValidationError

def test_crawler_config_validation():
    # Валиден конфиг
    config = CrawlerConfig(
        start_urls=["https://example.com"],
        allowed_domains=["example.com"],
        concurrent_requests=8
    )
    assert config.concurrent_requests == 8

    # Невалиден конфиг
    with pytest.raises(ValidationError):
        CrawlerConfig(concurrent_requests=50)  # Над лимита

def test_document_section_validation():
    # Валидна секция
    section = DocumentSection(
        content="This is a test content that is long enough",
        section_index=1,
        section_type="paragraph"
    )
    assert section.section_type == "paragraph"

    # Невалидна секция
    with pytest.raises(ValidationError):
        DocumentSection(
            content="Short",  # Твърде кратко
            section_index=1,
            section_type="paragraph"
        )
```

**Валидация**:
- [ ] Всички класове се импортират без грешки
- [ ] `python -c "from config.models import CrawlerConfig; print('OK')"` работи
- [ ] `pytest tests/test_config.py -v` минава всички тестове
- [ ] Валидацията хвърля грешки при невалидни данни
- [ ] Конфигурацията се зарежда от .env файл

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че Pydantic моделите работят правилно**
- [ ] **Тествал съм валидацията и тя хвърля грешки при нужда**
- [ ] **НЕ лъжа и НЕ си измислям - всички тестове минават успешно**

---

### Задача 3.2: Database Manager клас
**Цел**: Създаване на централизиран клас за работа с базата данни

**Стъпки**:
1. Създайте `database/db_manager.py`
2. Имплементирайте основни CRUD операции
3. Добавете connection pooling
4. Тествайте всички методи

**database/db_manager.py**:
```python
import os
import asyncio
from typing import List, Dict, Any, Optional
from supabase import create_client, Client
from dotenv import load_dotenv
import logging

class DatabaseManager:
    def __init__(self):
        load_dotenv()
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_SERVICE_KEY")

        if not self.supabase_url or not self.supabase_key:
            raise ValueError("Missing Supabase credentials in .env file")

        self.client: Client = create_client(self.supabase_url, self.supabase_key)
        self.logger = logging.getLogger(self.__class__.__name__)

    def test_connection(self) -> bool:
        """Тестване на връзката с базата данни"""
        try:
            result = self.client.table('test_connection').select('*').limit(1).execute()
            return True
        except Exception as e:
            self.logger.error(f"Database connection failed: {e}")
            return False

    def insert_raw_page(self, url: str, html_content: str, metadata: Dict = None) -> str:
        """Вмъкване на сурова страница"""
        try:
            data = {
                'url': url,
                'html_content': html_content,
                'metadata': metadata or {},
                'is_processed': False
            }

            result = self.client.table('raw_pages').insert(data).execute()
            return result.data[0]['id']
        except Exception as e:
            self.logger.error(f"Failed to insert raw page: {e}")
            raise

    def get_unprocessed_pages(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Получаване на необработени страници"""
        try:
            result = self.client.table('raw_pages') \
                .select('*') \
                .eq('is_processed', False) \
                .limit(limit) \
                .execute()

            return result.data
        except Exception as e:
            self.logger.error(f"Failed to get unprocessed pages: {e}")
            return []

    def mark_page_processed(self, page_id: str) -> bool:
        """Маркиране на страница като обработена"""
        try:
            self.client.table('raw_pages') \
                .update({'is_processed': True}) \
                .eq('id', page_id) \
                .execute()
            return True
        except Exception as e:
            self.logger.error(f"Failed to mark page as processed: {e}")
            return False

    def insert_document_section(self, section_data: Dict[str, Any]) -> str:
        """Вмъкване на секция от документ"""
        try:
            result = self.client.table('document_sections').insert(section_data).execute()
            return result.data[0]['id']
        except Exception as e:
            self.logger.error(f"Failed to insert document section: {e}")
            raise

    def search_similar_sections(self, embedding: List[float], limit: int = 5, threshold: float = 0.7) -> List[Dict]:
        """Търсене на подобни секции по embedding"""
        try:
            # Тук ще използваме stored procedure когато я създадем
            # За сега връщаме празен списък
            return []
        except Exception as e:
            self.logger.error(f"Failed to search similar sections: {e}")
            return []
```

**Тестов файл `tests/test_db_manager.py`**:
```python
import pytest
from database.db_manager import DatabaseManager
import uuid

@pytest.fixture
def db_manager():
    return DatabaseManager()

def test_database_connection(db_manager):
    """Тест на връзката с базата данни"""
    assert db_manager.test_connection() == True

def test_insert_and_get_raw_page(db_manager):
    """Тест на вмъкване и извличане на сурова страница"""
    test_url = f"https://test-{uuid.uuid4()}.com"
    test_content = "<html><body>Test content</body></html>"

    # Вмъкване
    page_id = db_manager.insert_raw_page(test_url, test_content, {"test": True})
    assert page_id is not None

    # Извличане на необработени страници
    unprocessed = db_manager.get_unprocessed_pages()
    test_page = next((p for p in unprocessed if p['id'] == page_id), None)
    assert test_page is not None
    assert test_page['url'] == test_url
    assert test_page['is_processed'] == False

    # Маркиране като обработена
    success = db_manager.mark_page_processed(page_id)
    assert success == True

    # Проверка че е маркирана
    unprocessed_after = db_manager.get_unprocessed_pages()
    test_page_after = next((p for p in unprocessed_after if p['id'] == page_id), None)
    assert test_page_after is None  # Не трябва да се намира в необработените

def test_insert_document_section(db_manager):
    """Тест на вмъкване на секция от документ"""
    section_data = {
        'content': 'This is a test section with enough content to pass validation',
        'section_index': 1,
        'section_type': 'paragraph',
        'content_hash': 'test_hash_' + str(uuid.uuid4()),
        'language': 'en'
    }

    section_id = db_manager.insert_document_section(section_data)
    assert section_id is not None
```

**Валидация**:
- [ ] DatabaseManager клас се създава без грешки
- [ ] `python -c "from database.db_manager import DatabaseManager; dm = DatabaseManager(); print(dm.test_connection())"` връща True
- [ ] `pytest tests/test_db_manager.py -v` минава всички тестове
- [ ] Всички CRUD операции работят правилно
- [ ] Грешките се обработват правилно

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че DatabaseManager работи правилно**
- [ ] **Тествал съм всички методи и те работят с реалната база данни**
- [ ] **НЕ лъжа и НЕ си измислям - всички тестове минават и операциите са реални**

---

### Задача 3.3: Базов краулър клас
**Цел**: Създаване на абстрактен базов клас за всички краулъри

**Стъпки**:
1. Създайте `crawlers/base_crawler.py`
2. Имплементирайте базовата функционалност
3. Добавете метрики и логиране
4. Тествайте базовите методи

**crawlers/base_crawler.py**:
```python
from abc import ABC, abstractmethod
import logging
import time
import random
import hashlib
from typing import Dict, Any, List, Optional
from prometheus_client import Counter, Gauge, Histogram
from database.db_manager import DatabaseManager

# Метрики
PAGES_CRAWLED = Counter('pages_crawled_total', 'Total pages crawled', ['crawler_type', 'status'])
CRAWL_TIME = Histogram('crawl_time_seconds', 'Time taken to crawl', ['crawler_type'])
ACTIVE_CRAWLERS = Gauge('active_crawlers', 'Number of active crawlers', ['crawler_type'])

class BaseCrawler(ABC):
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.db_manager = DatabaseManager()
        self.crawler_type = self.__class__.__name__

        # User agents за ротация
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ]

    def get_random_user_agent(self) -> str:
        """Връща случаен User-Agent"""
        return random.choice(self.user_agents)

    def add_delay(self):
        """Добавя случайно забавяне между заявки"""
        min_delay = self.config.get('min_delay', 1)
        max_delay = self.config.get('max_delay', 5)
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)

    def generate_content_hash(self, content: str) -> str:
        """Генерира хеш на съдържанието"""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()

    def is_duplicate_url(self, url: str) -> bool:
        """Проверява дали URL-ът вече е краулнат"""
        try:
            result = self.db_manager.client.table('raw_pages') \
                .select('id') \
                .eq('url', url) \
                .limit(1) \
                .execute()

            return len(result.data) > 0
        except Exception as e:
            self.logger.error(f"Error checking duplicate URL: {e}")
            return False

    @abstractmethod
    async def crawl_url(self, url: str) -> Dict[str, Any]:
        """Краулинг на конкретен URL - трябва да се имплементира в наследниците"""
        pass

    @abstractmethod
    def save_crawl_result(self, url: str, result: Dict[str, Any]) -> bool:
        """Запазване на резултата от краулинг - трябва да се имплементира в наследниците"""
        pass

    async def run_crawl(self, urls: List[str]) -> Dict[str, Any]:
        """Основен метод за краулинг на списък от URL-и"""
        ACTIVE_CRAWLERS.labels(crawler_type=self.crawler_type).inc()

        results = {
            'total_urls': len(urls),
            'successful': 0,
            'failed': 0,
            'duplicates': 0,
            'errors': []
        }

        start_time = time.time()

        try:
            for url in urls:
                try:
                    # Проверка за дубликати
                    if self.is_duplicate_url(url):
                        self.logger.info(f"Skipping duplicate URL: {url}")
                        results['duplicates'] += 1
                        PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status='duplicate').inc()
                        continue

                    # Краулинг
                    crawl_result = await self.crawl_url(url)

                    if crawl_result.get('success', False):
                        # Запазване
                        if self.save_crawl_result(url, crawl_result):
                            results['successful'] += 1
                            PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status='success').inc()
                        else:
                            results['failed'] += 1
                            PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status='save_failed').inc()
                    else:
                        results['failed'] += 1
                        results['errors'].append(f"{url}: {crawl_result.get('error', 'Unknown error')}")
                        PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status='crawl_failed').inc()

                    # Забавяне между заявки
                    self.add_delay()

                except Exception as e:
                    self.logger.error(f"Error crawling {url}: {e}")
                    results['failed'] += 1
                    results['errors'].append(f"{url}: {str(e)}")
                    PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status='error').inc()

        finally:
            end_time = time.time()
            CRAWL_TIME.labels(crawler_type=self.crawler_type).observe(end_time - start_time)
            ACTIVE_CRAWLERS.labels(crawler_type=self.crawler_type).dec()

        self.logger.info(f"Crawl completed: {results}")
        return results
```

**Тестов файл `tests/test_base_crawler.py`**:
```python
import pytest
import asyncio
from crawlers.base_crawler import BaseCrawler

class TestCrawler(BaseCrawler):
    """Тестова имплементация на BaseCrawler"""

    async def crawl_url(self, url: str):
        # Симулация на краулинг
        await asyncio.sleep(0.1)
        return {
            'success': True,
            'content': f'<html><body>Content for {url}</body></html>',
            'title': f'Title for {url}'
        }

    def save_crawl_result(self, url: str, result: Dict[str, Any]) -> bool:
        try:
            page_id = self.db_manager.insert_raw_page(
                url=url,
                html_content=result['content'],
                metadata={'title': result['title']}
            )
            return page_id is not None
        except Exception as e:
            self.logger.error(f"Failed to save crawl result: {e}")
            return False

@pytest.fixture
def test_crawler():
    config = {
        'min_delay': 0.1,
        'max_delay': 0.2
    }
    return TestCrawler(config)

def test_crawler_initialization(test_crawler):
    """Тест на инициализация на краулър"""
    assert test_crawler.crawler_type == 'TestCrawler'
    assert len(test_crawler.user_agents) > 0
    assert test_crawler.db_manager is not None

def test_get_random_user_agent(test_crawler):
    """Тест на генериране на User-Agent"""
    ua1 = test_crawler.get_random_user_agent()
    ua2 = test_crawler.get_random_user_agent()

    assert ua1 in test_crawler.user_agents
    assert ua2 in test_crawler.user_agents

def test_generate_content_hash(test_crawler):
    """Тест на генериране на хеш"""
    content = "Test content"
    hash1 = test_crawler.generate_content_hash(content)
    hash2 = test_crawler.generate_content_hash(content)
    hash3 = test_crawler.generate_content_hash("Different content")

    assert hash1 == hash2  # Същото съдържание -> същ хеш
    assert hash1 != hash3  # Различно съдържание -> различен хеш
    assert len(hash1) == 64  # SHA256 хеш

@pytest.mark.asyncio
async def test_run_crawl(test_crawler):
    """Тест на основния краулинг процес"""
    test_urls = [
        f'https://test-{i}.com'
        for i in range(3)
    ]

    results = await test_crawler.run_crawl(test_urls)

    assert results['total_urls'] == 3
    assert results['successful'] >= 0
    assert results['failed'] >= 0
    assert results['duplicates'] >= 0
    assert results['successful'] + results['failed'] + results['duplicates'] == 3
```

**Валидация**:
- [ ] BaseCrawler клас се създава без грешки
- [ ] `python -c "from crawlers.base_crawler import BaseCrawler; print('OK')"` работи
- [ ] `pytest tests/test_base_crawler.py -v` минава всички тестове
- [ ] Метриките се записват правилно
- [ ] Дубликатите се откриват правилно

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че BaseCrawler работи правилно**
- [ ] **Тествал съм всички методи включително async операции**
- [ ] **НЕ лъжа и НЕ си измислям - всички тестове минават и функционалността е реална**

---

## 📋 ФАЗА 4: Първи работещ краулър (Седмица 2-3)

### Задача 4.1: HTML краулър с aiohttp
**Цел**: Създаване на прост но функционален HTML краулър

**Стъпки**:
1. Създайте `crawlers/html_crawler.py`
2. Имплементирайте асинхронен краулинг с aiohttp
3. Добавете обработка на HTML с BeautifulSoup
4. Тествайте с реални сайтове

**crawlers/html_crawler.py**:
```python
import asyncio
import aiohttp
from bs4 import BeautifulSoup
from typing import Dict, Any, List
from urllib.parse import urljoin, urlparse
import logging
from crawlers.base_crawler import BaseCrawler

class HTMLCrawler(BaseCrawler):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.session = None
        self.timeout = aiohttp.ClientTimeout(total=30)

    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=self.timeout,
            headers={'User-Agent': self.get_random_user_agent()}
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()

    async def crawl_url(self, url: str) -> Dict[str, Any]:
        """Краулинг на HTML страница"""
        try:
            if not self.session:
                raise RuntimeError("HTMLCrawler must be used as async context manager")

            self.logger.info(f"Crawling HTML page: {url}")

            async with self.session.get(url) as response:
                if response.status == 200:
                    html_content = await response.text()

                    # Парсиране на HTML
                    soup = BeautifulSoup(html_content, 'html.parser')

                    # Извличане на основна информация
                    title = soup.title.string.strip() if soup.title else ""

                    # Премахване на script и style тагове
                    for script in soup(["script", "style"]):
                        script.decompose()

                    # Извличане на чист текст
                    text_content = soup.get_text(separator=' ', strip=True)

                    # Извличане на линкове
                    links = []
                    for link in soup.find_all('a', href=True):
                        absolute_url = urljoin(url, link['href'])
                        links.append({
                            'url': absolute_url,
                            'text': link.get_text(strip=True)
                        })

                    # Извличане на метаданни
                    meta_description = ""
                    meta_tag = soup.find('meta', attrs={'name': 'description'})
                    if meta_tag:
                        meta_description = meta_tag.get('content', '')

                    return {
                        'success': True,
                        'url': url,
                        'title': title,
                        'html_content': html_content,
                        'text_content': text_content,
                        'meta_description': meta_description,
                        'links': links,
                        'status_code': response.status,
                        'content_type': response.headers.get('Content-Type', ''),
                        'content_length': len(html_content)
                    }
                else:
                    return {
                        'success': False,
                        'url': url,
                        'error': f'HTTP {response.status}',
                        'status_code': response.status
                    }

        except asyncio.TimeoutError:
            return {
                'success': False,
                'url': url,
                'error': 'Request timeout'
            }
        except Exception as e:
            return {
                'success': False,
                'url': url,
                'error': str(e)
            }

    def save_crawl_result(self, url: str, result: Dict[str, Any]) -> bool:
        """Запазване на резултата от HTML краулинг"""
        try:
            metadata = {
                'title': result.get('title', ''),
                'meta_description': result.get('meta_description', ''),
                'links_count': len(result.get('links', [])),
                'status_code': result.get('status_code'),
                'content_type': result.get('content_type', ''),
                'content_length': result.get('content_length', 0),
                'text_length': len(result.get('text_content', ''))
            }

            page_id = self.db_manager.insert_raw_page(
                url=url,
                html_content=result['html_content'],
                metadata=metadata
            )

            self.logger.info(f"Saved HTML page {url} with ID {page_id}")
            return page_id is not None

        except Exception as e:
            self.logger.error(f"Failed to save HTML crawl result for {url}: {e}")
            return False

    async def crawl_multiple_urls(self, urls: List[str], max_concurrent: int = 5) -> Dict[str, Any]:
        """Краулинг на множество URL-и с ограничение на concurrent заявки"""
        semaphore = asyncio.Semaphore(max_concurrent)

        async def crawl_with_semaphore(url):
            async with semaphore:
                return await self.crawl_url(url)

        # Изпълнение на всички краулинг задачи
        tasks = [crawl_with_semaphore(url) for url in urls]
        crawl_results = await asyncio.gather(*tasks, return_exceptions=True)

        # Обработка на резултатите
        results = {
            'total_urls': len(urls),
            'successful': 0,
            'failed': 0,
            'errors': []
        }

        for i, result in enumerate(crawl_results):
            url = urls[i]

            if isinstance(result, Exception):
                results['failed'] += 1
                results['errors'].append(f"{url}: {str(result)}")
            elif result.get('success', False):
                if self.save_crawl_result(url, result):
                    results['successful'] += 1
                else:
                    results['failed'] += 1
                    results['errors'].append(f"{url}: Failed to save")
            else:
                results['failed'] += 1
                results['errors'].append(f"{url}: {result.get('error', 'Unknown error')}")

        return results
```

**Тестов файл `tests/test_html_crawler.py`**:
```python
import pytest
import asyncio
from crawlers.html_crawler import HTMLCrawler

@pytest.fixture
def html_crawler():
    config = {
        'min_delay': 0.1,
        'max_delay': 0.2
    }
    return HTMLCrawler(config)

@pytest.mark.asyncio
async def test_html_crawler_single_url(html_crawler):
    """Тест на краулинг на една HTML страница"""
    test_url = "https://httpbin.org/html"  # Тестов сайт

    async with html_crawler:
        result = await html_crawler.crawl_url(test_url)

    assert result['success'] == True
    assert result['url'] == test_url
    assert 'html_content' in result
    assert 'text_content' in result
    assert 'title' in result
    assert result['status_code'] == 200

@pytest.mark.asyncio
async def test_html_crawler_invalid_url(html_crawler):
    """Тест на краулинг на невалиден URL"""
    test_url = "https://this-domain-does-not-exist-12345.com"

    async with html_crawler:
        result = await html_crawler.crawl_url(test_url)

    assert result['success'] == False
    assert 'error' in result

@pytest.mark.asyncio
async def test_html_crawler_multiple_urls(html_crawler):
    """Тест на краулинг на множество URL-и"""
    test_urls = [
        "https://httpbin.org/html",
        "https://httpbin.org/json",
        "https://httpbin.org/xml"
    ]

    async with html_crawler:
        results = await html_crawler.crawl_multiple_urls(test_urls, max_concurrent=2)

    assert results['total_urls'] == 3
    assert results['successful'] + results['failed'] == 3
    assert results['successful'] >= 1  # Поне един трябва да е успешен

def test_html_crawler_save_result(html_crawler):
    """Тест на запазване на резултат"""
    test_result = {
        'success': True,
        'url': 'https://test.com',
        'title': 'Test Page',
        'html_content': '<html><body>Test</body></html>',
        'text_content': 'Test',
        'meta_description': 'Test description',
        'links': [],
        'status_code': 200,
        'content_type': 'text/html',
        'content_length': 30
    }

    success = html_crawler.save_crawl_result('https://test.com', test_result)
    assert success == True
```

**Интеграционен тест `tests/test_html_crawler_integration.py`**:
```python
import pytest
import asyncio
from crawlers.html_crawler import HTMLCrawler
from database.db_manager import DatabaseManager

@pytest.mark.asyncio
async def test_full_html_crawling_workflow():
    """Пълен интеграционен тест на HTML краулинг"""

    # Конфигурация
    config = {
        'min_delay': 0.5,
        'max_delay': 1.0
    }

    # URL-и за тестване
    test_urls = [
        "https://httpbin.org/html",
        "https://example.com"
    ]

    # Краулинг
    crawler = HTMLCrawler(config)

    async with crawler:
        results = await crawler.run_crawl(test_urls)

    # Проверки
    assert results['total_urls'] == 2
    assert results['successful'] >= 1

    # Проверка в базата данни
    db_manager = DatabaseManager()
    unprocessed_pages = db_manager.get_unprocessed_pages()

    # Трябва да има поне една нова страница
    new_pages = [p for p in unprocessed_pages if p['url'] in test_urls]
    assert len(new_pages) >= 1

    print(f"Crawling results: {results}")
    print(f"New pages in database: {len(new_pages)}")
```

**Валидация**:
- [ ] HTMLCrawler клас се създава без грешки
- [ ] `python -c "from crawlers.html_crawler import HTMLCrawler; print('OK')"` работи
- [ ] `pytest tests/test_html_crawler.py -v` минава всички тестове
- [ ] `pytest tests/test_html_crawler_integration.py -v` минава интеграционния тест
- [ ] Реални страници се краулват и запазват в базата данни
- [ ] Async context manager работи правилно

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че HTML краулърът работи с реални сайтове**
- [ ] **Тествал съм с httpbin.org и example.com - данните се запазват в базата**
- [ ] **НЕ лъжа и НЕ си измислям - краулърът е реален и функционален**

---

### Задача 4.2: Първи MCP сървър endpoint
**Цел**: Създаване на базов FastAPI сървър с един работещ endpoint

**Стъпки**:
1. Създайте `mcp_server/basic_server.py`
2. Имплементирайте health check endpoint
3. Добавете basic search endpoint
4. Тествайте с реални заявки

**mcp_server/basic_server.py**:
```python
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import logging
import os
from dotenv import load_dotenv
from database.db_manager import DatabaseManager
import time
from datetime import datetime

# Зареждане на environment variables
load_dotenv()

# Инициализация на FastAPI
app = FastAPI(
    title="RAG MCP Server",
    description="Model Context Protocol Server for RAG System",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Database manager
db_manager = DatabaseManager()

# Логгер
logger = logging.getLogger("mcp_server")

# Pydantic модели
class HealthResponse(BaseModel):
    status: str
    timestamp: str
    database_connected: bool
    version: str

class SearchQuery(BaseModel):
    query: str = Field(..., min_length=1, max_length=500)
    limit: int = Field(default=5, ge=1, le=20)

class SearchResult(BaseModel):
    id: str
    url: str
    title: str
    content_snippet: str
    metadata: Dict[str, Any]

class SearchResponse(BaseModel):
    query: str
    results: List[SearchResult]
    total_found: int
    search_time_ms: float

# Middleware за логиране на заявки
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()

    response = await call_next(request)

    process_time = time.time() - start_time
    logger.info(
        f"{request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Time: {process_time:.3f}s"
    )

    return response

# Health check endpoint
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        # Тест на връзката с базата данни
        db_connected = db_manager.test_connection()

        return HealthResponse(
            status="healthy" if db_connected else "degraded",
            timestamp=datetime.now().isoformat(),
            database_connected=db_connected,
            version="1.0.0"
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")

# Basic search endpoint
@app.post("/search", response_model=SearchResponse)
async def search_pages(query: SearchQuery):
    """Търсене в краулнатите страници"""
    start_time = time.time()

    try:
        logger.info(f"Search query: '{query.query}' with limit {query.limit}")

        # Простo текстово търсене в raw_pages таблицата
        # За сега използваме LIKE заявка, по-късно ще добавим векторно търсене
        search_term = f"%{query.query.lower()}%"

        result = db_manager.client.table('raw_pages') \
            .select('id, url, html_content, metadata') \
            .or_(f'url.ilike.{search_term},html_content.ilike.{search_term}') \
            .limit(query.limit) \
            .execute()

        search_results = []
        for row in result.data:
            # Извличане на title от metadata или HTML
            title = ""
            if row.get('metadata') and row['metadata'].get('title'):
                title = row['metadata']['title']
            else:
                # Опит за извличане на title от HTML
                from bs4 import BeautifulSoup
                try:
                    soup = BeautifulSoup(row['html_content'], 'html.parser')
                    if soup.title:
                        title = soup.title.string.strip()
                except:
                    title = "No title"

            # Създаване на snippet
            content = row['html_content'] or ""
            if len(content) > 200:
                # Намиране на позицията на търсения термин
                lower_content = content.lower()
                query_pos = lower_content.find(query.query.lower())

                if query_pos != -1:
                    # Snippet около търсения термин
                    start = max(0, query_pos - 100)
                    end = min(len(content), query_pos + 100)
                    snippet = content[start:end]
                    if start > 0:
                        snippet = "..." + snippet
                    if end < len(content):
                        snippet = snippet + "..."
                else:
                    # Първите 200 символа
                    snippet = content[:200] + "..."
            else:
                snippet = content

            search_results.append(SearchResult(
                id=row['id'],
                url=row['url'],
                title=title,
                content_snippet=snippet,
                metadata=row.get('metadata', {})
            ))

        search_time = (time.time() - start_time) * 1000  # в милисекунди

        return SearchResponse(
            query=query.query,
            results=search_results,
            total_found=len(search_results),
            search_time_ms=round(search_time, 2)
        )

    except Exception as e:
        logger.error(f"Search failed: {e}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint с информация за API"""
    return {
        "message": "RAG MCP Server",
        "version": "1.0.0",
        "endpoints": {
            "health": "/health",
            "search": "/search",
            "docs": "/docs"
        }
    }

# Стартиране на сървъра
if __name__ == "__main__":
    import uvicorn

    # Конфигуриране на логиране
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Стартиране на сървъра
    uvicorn.run(
        "basic_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
```

**Тестов файл `tests/test_basic_server.py`**:
```python
import pytest
from fastapi.testclient import TestClient
from mcp_server.basic_server import app
import json

@pytest.fixture
def client():
    return TestClient(app)

def test_root_endpoint(client):
    """Тест на root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data
    assert "endpoints" in data

def test_health_endpoint(client):
    """Тест на health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "timestamp" in data
    assert "database_connected" in data
    assert "version" in data
    assert data["version"] == "1.0.0"

def test_search_endpoint_valid_query(client):
    """Тест на search endpoint с валидна заявка"""
    search_data = {
        "query": "test",
        "limit": 5
    }

    response = client.post("/search", json=search_data)
    assert response.status_code == 200

    data = response.json()
    assert "query" in data
    assert "results" in data
    assert "total_found" in data
    assert "search_time_ms" in data
    assert data["query"] == "test"
    assert isinstance(data["results"], list)
    assert isinstance(data["total_found"], int)
    assert isinstance(data["search_time_ms"], float)

def test_search_endpoint_invalid_query(client):
    """Тест на search endpoint с невалидна заявка"""
    # Празна заявка
    search_data = {
        "query": "",
        "limit": 5
    }

    response = client.post("/search", json=search_data)
    assert response.status_code == 422  # Validation error

def test_search_endpoint_invalid_limit(client):
    """Тест на search endpoint с невалиден лимит"""
    search_data = {
        "query": "test",
        "limit": 50  # Над максимума от 20
    }

    response = client.post("/search", json=search_data)
    assert response.status_code == 422  # Validation error

def test_openapi_docs(client):
    """Тест че OpenAPI документацията е достъпна"""
    response = client.get("/docs")
    assert response.status_code == 200

    response = client.get("/openapi.json")
    assert response.status_code == 200
    openapi_data = response.json()
    assert "openapi" in openapi_data
    assert "info" in openapi_data
```

**Ръчен тест скрипт `scripts/test_server.py`**:
```python
#!/usr/bin/env python3
"""
Ръчен тест на MCP сървъра
Стартирайте сървъра и след това изпълнете този скрипт
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_health():
    """Тест на health endpoint"""
    print("Testing health endpoint...")
    response = requests.get(f"{BASE_URL}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()

def test_search():
    """Тест на search endpoint"""
    print("Testing search endpoint...")

    search_data = {
        "query": "test",
        "limit": 3
    }

    response = requests.post(f"{BASE_URL}/search", json=search_data)
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()

def test_root():
    """Тест на root endpoint"""
    print("Testing root endpoint...")
    response = requests.get(f"{BASE_URL}/")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()

if __name__ == "__main__":
    print("Starting MCP Server tests...")
    print("Make sure the server is running on http://localhost:8000")
    print()

    try:
        test_root()
        test_health()
        test_search()
        print("All tests completed!")
    except requests.exceptions.ConnectionError:
        print("ERROR: Could not connect to server. Make sure it's running on http://localhost:8000")
    except Exception as e:
        print(f"ERROR: {e}")
```

**Валидация**:
- [ ] FastAPI сървърът стартира без грешки
- [ ] `python mcp_server/basic_server.py` стартира сървъра на порт 8000
- [ ] `curl http://localhost:8000/health` връща JSON отговор
- [ ] `curl http://localhost:8000/` връща информация за API
- [ ] `pytest tests/test_basic_server.py -v` минава всички тестове
- [ ] `python scripts/test_server.py` работи успешно
- [ ] OpenAPI документацията е достъпна на http://localhost:8000/docs

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че MCP сървърът работи и отговаря на заявки**
- [ ] **Тествал съм всички endpoints с curl и pytest**
- [ ] **НЕ лъжа и НЕ си измислям - сървърът е реален и функционален**

---

## 📋 ФАЗА 5: PDF обработка и ембединги (Седмица 3-4)

### Задача 5.1: PDF процесор с PyMuPDF
**Цел**: Създаване на PDF процесор който извлича текст и таблици

**Стъпки**:
1. Създайте `processors/pdf_processor.py`
2. Имплементирайте извличане на текст по блокове
3. Добавете извличане на таблици
4. Тествайте с реални PDF файлове

**processors/pdf_processor.py**:
```python
import fitz  # PyMuPDF
import os
import logging
import hashlib
from typing import List, Dict, Any, Optional
from langdetect import detect, DetectorFactory
from langdetect.lang_detect_exception import LangDetectException
import pandas as pd
from prometheus_client import Counter, Histogram

# Задаване на seed за консистентни резултати
DetectorFactory.seed = 0

# Метрики
PDF_PROCESSED = Counter('pdf_processed_total', 'Total PDFs processed', ['status'])
PDF_PROCESSING_TIME = Histogram('pdf_processing_seconds', 'Time to process PDF')

class LanguageDetector:
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.min_text_length = 20
        self.supported_languages = {'bg', 'en', 'de', 'fr', 'es', 'it', 'ru'}

    def detect_language(self, text: str) -> str:
        """Подобрена детекция на език с fallback стратегии"""
        if not text or len(text.strip()) < self.min_text_length:
            return "unknown"

        cleaned_text = text.strip()[:1000]

        try:
            detected_lang = detect(cleaned_text)

            if detected_lang in self.supported_languages:
                return detected_lang
            else:
                self.logger.warning(f"Detected unsupported language: {detected_lang}")
                return "other"

        except LangDetectException as e:
            self.logger.warning(f"Language detection failed: {e}")

            if self._contains_cyrillic(cleaned_text):
                return "bg"

            return "unknown"

    def _contains_cyrillic(self, text: str) -> bool:
        """Проверка за кирилски символи"""
        cyrillic_chars = sum(1 for char in text if '\u0400' <= char <= '\u04FF')
        return cyrillic_chars > len(text) * 0.1

class PDFProcessor:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.language_detector = LanguageDetector()

        # Конфигурация
        self.batch_size = config.get('batch_size', 10)
        self.extract_tables = config.get('extract_tables', True)
        self.extract_images = config.get('extract_images', False)
        self.min_text_length = config.get('min_text_length', 50)
        self.section_splitter = config.get('section_splitter', 'blocks')

    def process_pdf(self, file_path: str) -> List[Dict[str, Any]]:
        """Обработва PDF файл и връща списък от секции"""
        self.logger.info(f"Processing PDF: {file_path}")

        with PDF_PROCESSING_TIME.time():
            try:
                doc = fitz.open(file_path)
                sections = []

                for page_idx, page in enumerate(doc):
                    # Извличане на текст по блокове
                    if self.section_splitter == "blocks":
                        blocks = page.get_text("blocks")
                        for block_idx, block in enumerate(blocks):
                            text = block[4]  # Текстово съдържание

                            if len(text.strip()) >= self.min_text_length:
                                content_hash = hashlib.sha256(text.encode()).hexdigest()
                                language = self.language_detector.detect_language(text)

                                section = {
                                    "content": text.strip(),
                                    "section_index": page_idx * 1000 + block_idx,
                                    "section_type": "paragraph",
                                    "page": page_idx + 1,
                                    "language": language,
                                    "content_hash": content_hash,
                                    "metadata": {
                                        "bbox": block[:4],
                                        "font_size": block[5] if len(block) > 5 else None,
                                        "source_file": os.path.basename(file_path)
                                    }
                                }
                                sections.append(section)

                    # Извличане на таблици
                    if self.extract_tables:
                        tables = page.find_tables()
                        for table_idx, table in enumerate(tables):
                            try:
                                df = table.to_pandas()
                                table_markdown = df.to_markdown(index=False)
                                content_hash = hashlib.sha256(table_markdown.encode()).hexdigest()

                                section = {
                                    "content": table_markdown,
                                    "section_index": page_idx * 1000 + 500 + table_idx,
                                    "section_type": "table",
                                    "page": page_idx + 1,
                                    "language": "table",
                                    "content_hash": content_hash,
                                    "metadata": {
                                        "table_shape": df.shape,
                                        "columns": df.columns.tolist(),
                                        "bbox": table.bbox,
                                        "source_file": os.path.basename(file_path)
                                    }
                                }
                                sections.append(section)
                            except Exception as e:
                                self.logger.warning(f"Failed to process table on page {page_idx}: {e}")

                doc.close()
                self.logger.info(f"Extracted {len(sections)} sections from {file_path}")
                PDF_PROCESSED.labels(status='success').inc()
                return sections

            except Exception as e:
                self.logger.error(f"Error processing PDF {file_path}: {str(e)}")
                PDF_PROCESSED.labels(status='error').inc()
                raise

    def process_multiple_pdfs(self, file_paths: List[str]) -> List[Dict[str, Any]]:
        """Обработва множество PDF файлове"""
        all_sections = []

        for file_path in file_paths:
            try:
                sections = self.process_pdf(file_path)
                all_sections.extend(sections)
            except Exception as e:
                self.logger.error(f"Failed to process {file_path}: {e}")

        return all_sections
```

**Тестов файл `tests/test_pdf_processor.py`**:
```python
import pytest
import os
import tempfile
from processors.pdf_processor import PDFProcessor, LanguageDetector

@pytest.fixture
def pdf_processor():
    config = {
        'batch_size': 10,
        'extract_tables': True,
        'extract_images': False,
        'min_text_length': 20,
        'section_splitter': 'blocks'
    }
    return PDFProcessor(config)

@pytest.fixture
def language_detector():
    return LanguageDetector()

def test_language_detector_english(language_detector):
    """Тест на детекция на английски език"""
    english_text = "This is a sample English text for language detection testing."
    result = language_detector.detect_language(english_text)
    assert result == "en"

def test_language_detector_bulgarian(language_detector):
    """Тест на детекция на български език"""
    bulgarian_text = "Това е примерен български текст за тестване на детекцията на език."
    result = language_detector.detect_language(bulgarian_text)
    assert result == "bg"

def test_language_detector_short_text(language_detector):
    """Тест на детекция с кратък текст"""
    short_text = "Hi"
    result = language_detector.detect_language(short_text)
    assert result == "unknown"

def test_pdf_processor_initialization(pdf_processor):
    """Тест на инициализация на PDF процесор"""
    assert pdf_processor.batch_size == 10
    assert pdf_processor.extract_tables == True
    assert pdf_processor.min_text_length == 20
    assert pdf_processor.language_detector is not None

# За тестване с реален PDF файл ще създадем прост PDF
def create_test_pdf():
    """Създава тестов PDF файл"""
    try:
        import fitz

        # Създаване на временен PDF
        doc = fitz.open()
        page = doc.new_page()

        # Добавяне на текст
        text = "This is a test PDF document.\nIt contains multiple lines of text.\nThis should be enough for testing purposes."
        page.insert_text((50, 50), text)

        # Запазване във временен файл
        temp_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
        doc.save(temp_file.name)
        doc.close()

        return temp_file.name
    except Exception as e:
        pytest.skip(f"Could not create test PDF: {e}")

def test_pdf_processor_with_real_file(pdf_processor):
    """Тест на PDF процесор с реален файл"""
    pdf_path = create_test_pdf()

    try:
        sections = pdf_processor.process_pdf(pdf_path)

        assert isinstance(sections, list)
        assert len(sections) >= 1

        # Проверка на първата секция
        first_section = sections[0]
        assert 'content' in first_section
        assert 'section_index' in first_section
        assert 'section_type' in first_section
        assert 'page' in first_section
        assert 'language' in first_section
        assert 'content_hash' in first_section
        assert 'metadata' in first_section

        assert len(first_section['content']) >= pdf_processor.min_text_length
        assert first_section['section_type'] in ['paragraph', 'table']
        assert first_section['page'] >= 1

    finally:
        # Изтриване на временния файл
        if os.path.exists(pdf_path):
            os.unlink(pdf_path)

def test_pdf_processor_multiple_files(pdf_processor):
    """Тест на обработка на множество PDF файлове"""
    pdf_paths = [create_test_pdf(), create_test_pdf()]

    try:
        all_sections = pdf_processor.process_multiple_pdfs(pdf_paths)

        assert isinstance(all_sections, list)
        assert len(all_sections) >= 2  # Поне по една секция от всеки файл

    finally:
        # Изтриване на временните файлове
        for path in pdf_paths:
            if os.path.exists(path):
                os.unlink(path)
```

**Валидация**:
- [ ] PDFProcessor клас се създава без грешки
- [ ] `python -c "from processors.pdf_processor import PDFProcessor; print('OK')"` работи
- [ ] `pytest tests/test_pdf_processor.py -v` минава всички тестове
- [ ] Реален PDF файл се обработва успешно
- [ ] Извличат се текстови секции с правилни метаданни
- [ ] Детекцията на език работи за български и английски

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че PDF процесорът работи с реални файлове**
- [ ] **Тествал съм с реални PDF файлове и извличам текст и метаданни**
- [ ] **НЕ лъжа и НЕ си измислям - процесорът е реален и функционален**

---

### Задача 5.2: Embedding генератор с OpenAI
**Цел**: Създаване на embedding генератор с кеширане и batch обработка

**Стъпки**:
1. Създайте `embeddings/embedder.py`
2. Имплементирайте OpenAI embedding генериране
3. Добавете Redis кеширане
4. Тествайте с реални текстове

**embeddings/embedder.py**:
```python
import asyncio
import hashlib
import json
import logging
import time
from typing import List, Dict, Any, Optional
import redis
from openai import AsyncOpenAI
from prometheus_client import Counter, Histogram, Gauge
import tenacity

# Метрики
EMBEDDINGS_GENERATED = Counter('embeddings_generated_total', 'Total embeddings', ['status'])
EMBEDDING_CACHE_HITS = Counter('embedding_cache_hits_total', 'Cache hits')
EMBEDDING_API_CALLS = Counter('embedding_api_calls_total', 'API calls')
TOKEN_USAGE = Counter('openai_tokens_used_total', 'OpenAI tokens used')
EMBEDDING_QUEUE_SIZE = Gauge('embedding_queue_size', 'Size of embedding queue')

class EmbeddingCache:
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_client = redis.from_url(redis_url)
        self.cache_ttl = 86400 * 7  # 7 дни
        self.logger = logging.getLogger(self.__class__.__name__)

    def get_embedding(self, content: str) -> Optional[List[float]]:
        """Получаване на кеширан ембединг"""
        try:
            content_hash = hashlib.sha256(content.encode()).hexdigest()
            cached = self.redis_client.get(f"embedding:{content_hash}")

            if cached:
                EMBEDDING_CACHE_HITS.inc()
                return json.loads(cached)
            return None
        except Exception as e:
            self.logger.error(f"Cache get error: {e}")
            return None

    def set_embedding(self, content: str, embedding: List[float]):
        """Запазване на ембединг в кеша"""
        try:
            content_hash = hashlib.sha256(content.encode()).hexdigest()
            self.redis_client.setex(
                f"embedding:{content_hash}",
                self.cache_ttl,
                json.dumps(embedding)
            )
        except Exception as e:
            self.logger.error(f"Cache set error: {e}")

    def test_connection(self) -> bool:
        """Тестване на Redis връзката"""
        try:
            self.redis_client.ping()
            return True
        except Exception as e:
            self.logger.error(f"Redis connection failed: {e}")
            return False

class EmbeddingProcessor:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)

        # OpenAI клиент
        self.client = AsyncOpenAI(api_key=config['api_key'])

        # Конфигурация
        self.model = config.get('model', 'text-embedding-3-small')
        self.batch_size = config.get('batch_size', 20)
        self.max_tokens_per_minute = config.get('max_tokens_per_minute', 1000000)

        # Кеш
        redis_url = config.get('redis_url', 'redis://localhost:6379')
        self.cache = EmbeddingCache(redis_url)

        # Rate limiting
        self.rate_limiter = asyncio.Semaphore(10)
        self.tokens_used_this_minute = 0
        self.minute_start = time.time()

    async def check_rate_limit(self, estimated_tokens: int):
        """Проверка и спазване на rate limits"""
        current_time = time.time()

        if current_time - self.minute_start >= 60:
            self.tokens_used_this_minute = 0
            self.minute_start = current_time

        if self.tokens_used_this_minute + estimated_tokens > self.max_tokens_per_minute:
            wait_time = 60 - (current_time - self.minute_start)
            if wait_time > 0:
                self.logger.info(f"Rate limit reached, waiting {wait_time:.1f}s")
                await asyncio.sleep(wait_time)
                self.tokens_used_this_minute = 0
                self.minute_start = time.time()

    @tenacity.retry(
        stop=tenacity.stop_after_attempt(3),
        wait=tenacity.wait_exponential(multiplier=1, min=4, max=10),
        retry=tenacity.retry_if_exception_type(Exception)
    )
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Генериране на ембединги с rate limiting и retry логика"""
        async with self.rate_limiter:
            estimated_tokens = sum(len(text) // 4 for text in texts)
            await self.check_rate_limit(estimated_tokens)

            try:
                self.logger.info(f"Generating embeddings for {len(texts)} texts")

                with EMBEDDING_GENERATION_TIME.time():
                    response = await self.client.embeddings.create(
                        model=self.model,
                        input=texts
                    )

                    actual_tokens = response.usage.total_tokens
                    self.tokens_used_this_minute += actual_tokens
                    TOKEN_USAGE.inc(actual_tokens)
                    EMBEDDING_API_CALLS.inc()
                    EMBEDDINGS_GENERATED.labels(status='success').inc(len(texts))

                    return [item.embedding for item in response.data]

            except Exception as e:
                EMBEDDINGS_GENERATED.labels(status='error').inc(len(texts))
                self.logger.error(f"Error generating embeddings: {e}")
                raise

    async def process_batch_with_cache(self, sections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Обработка на batch с кеширане"""
        cached_sections = []
        uncached_sections = []
        uncached_indices = []

        # Проверка за кеширани ембединги
        for i, section in enumerate(sections):
            cached_embedding = self.cache.get_embedding(section['content'])
            if cached_embedding:
                section['embedding'] = cached_embedding
                section['embedding_model'] = self.model
                cached_sections.append(section)
            else:
                uncached_sections.append(section)
                uncached_indices.append(i)

        # Генериране на ембединги за некеширани секции
        if uncached_sections:
            texts = [section['content'] for section in uncached_sections]
            embeddings = await self.generate_embeddings(texts)

            for i, (section, embedding) in enumerate(zip(uncached_sections, embeddings)):
                section['embedding'] = embedding
                section['embedding_model'] = self.model

                # Запазване в кеша
                self.cache.set_embedding(section['content'], embedding)

        # Обединяване на резултатите
        all_sections = cached_sections + uncached_sections
        return all_sections

    async def process_all(self, sections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Обработва всички секции на batches"""
        EMBEDDING_QUEUE_SIZE.set(len(sections))
        self.logger.info(f"Processing {len(sections)} sections in batches of {self.batch_size}")

        results = []

        for i in range(0, len(sections), self.batch_size):
            batch = sections[i:i + self.batch_size]
            processed_batch = await self.process_batch_with_cache(batch)
            results.extend(processed_batch)

            EMBEDDING_QUEUE_SIZE.set(len(sections) - i - len(batch))

            # Малко забавяне между batch-овете
            await asyncio.sleep(0.5)

        self.logger.info(f"Completed processing {len(results)} sections with embeddings")
        return results

# Добавяне на метрика която липсваше
EMBEDDING_GENERATION_TIME = Histogram('embedding_generation_seconds', 'Time to generate embeddings')
```

**Тестов файл `tests/test_embedder.py`**:
```python
import pytest
import asyncio
import os
from embeddings.embedder import EmbeddingProcessor, EmbeddingCache

@pytest.fixture
def embedding_config():
    return {
        'api_key': os.getenv('OPENAI_API_KEY', 'test-key'),
        'model': 'text-embedding-3-small',
        'batch_size': 3,
        'max_tokens_per_minute': 100000,
        'redis_url': 'redis://localhost:6379'
    }

@pytest.fixture
def embedding_cache():
    return EmbeddingCache('redis://localhost:6379')

@pytest.fixture
def embedding_processor(embedding_config):
    return EmbeddingProcessor(embedding_config)

def test_embedding_cache_connection(embedding_cache):
    """Тест на Redis връзката"""
    # Ако Redis не работи, тестът ще се пропусне
    if not embedding_cache.test_connection():
        pytest.skip("Redis not available")

    assert embedding_cache.test_connection() == True

def test_embedding_cache_operations(embedding_cache):
    """Тест на cache операции"""
    if not embedding_cache.test_connection():
        pytest.skip("Redis not available")

    test_content = "This is test content for caching"
    test_embedding = [0.1, 0.2, 0.3, 0.4, 0.5]

    # Първо трябва да няма кеширан резултат
    cached = embedding_cache.get_embedding(test_content)
    assert cached is None

    # Запазване в кеша
    embedding_cache.set_embedding(test_content, test_embedding)

    # Извличане от кеша
    cached = embedding_cache.get_embedding(test_content)
    assert cached == test_embedding

@pytest.mark.asyncio
async def test_embedding_processor_initialization(embedding_processor):
    """Тест на инициализация на embedding процесор"""
    assert embedding_processor.model == 'text-embedding-3-small'
    assert embedding_processor.batch_size == 3
    assert embedding_processor.cache is not None

@pytest.mark.asyncio
async def test_embedding_processor_rate_limiting(embedding_processor):
    """Тест на rate limiting"""
    # Тест че rate limiting не хвърля грешки
    await embedding_processor.check_rate_limit(1000)
    assert True  # Ако стигнем до тук, rate limiting работи

@pytest.mark.skipif(not os.getenv('OPENAI_API_KEY'), reason="OpenAI API key not provided")
@pytest.mark.asyncio
async def test_embedding_generation_real_api(embedding_processor):
    """Тест на генериране на ембединги с реален API"""
    test_texts = [
        "This is a test sentence.",
        "Another test sentence for embedding.",
        "Third test sentence."
    ]

    try:
        embeddings = await embedding_processor.generate_embeddings(test_texts)

        assert len(embeddings) == len(test_texts)
        assert all(isinstance(emb, list) for emb in embeddings)
        assert all(len(emb) == 1536 for emb in embeddings)  # text-embedding-3-small размер
        assert all(isinstance(val, float) for emb in embeddings for val in emb)

    except Exception as e:
        pytest.skip(f"OpenAI API call failed: {e}")

@pytest.mark.asyncio
async def test_embedding_processor_with_sections(embedding_processor):
    """Тест на обработка на секции"""
    test_sections = [
        {
            'content': 'This is the first test section with enough content.',
            'section_index': 1,
            'section_type': 'paragraph'
        },
        {
            'content': 'This is the second test section with different content.',
            'section_index': 2,
            'section_type': 'paragraph'
        }
    ]

    # Ако няма OpenAI ключ, тестваме само структурата
    if not os.getenv('OPENAI_API_KEY'):
        pytest.skip("OpenAI API key not provided")

    try:
        processed_sections = await embedding_processor.process_batch_with_cache(test_sections)

        assert len(processed_sections) == len(test_sections)

        for section in processed_sections:
            assert 'embedding' in section
            assert 'embedding_model' in section
            assert isinstance(section['embedding'], list)
            assert len(section['embedding']) == 1536
            assert section['embedding_model'] == 'text-embedding-3-small'

    except Exception as e:
        pytest.skip(f"Embedding processing failed: {e}")

def test_embedding_processor_mock_sections(embedding_processor):
    """Тест на структурата без реален API"""
    test_sections = [
        {
            'content': 'Test content 1',
            'section_index': 1,
            'section_type': 'paragraph'
        }
    ]

    # Тестваме че методът съществува и приема правилни параметри
    assert hasattr(embedding_processor, 'process_batch_with_cache')
    assert hasattr(embedding_processor, 'process_all')
    assert callable(embedding_processor.process_batch_with_cache)
    assert callable(embedding_processor.process_all)
```

**Интеграционен тест `tests/test_embedder_integration.py`**:
```python
import pytest
import asyncio
import os
from embeddings.embedder import EmbeddingProcessor
from processors.pdf_processor import PDFProcessor

@pytest.mark.skipif(not os.getenv('OPENAI_API_KEY'), reason="OpenAI API key not provided")
@pytest.mark.asyncio
async def test_full_pdf_to_embeddings_workflow():
    """Пълен интеграционен тест: PDF → секции → ембединги"""

    # Конфигурации
    pdf_config = {
        'batch_size': 10,
        'extract_tables': True,
        'min_text_length': 30
    }

    embedding_config = {
        'api_key': os.getenv('OPENAI_API_KEY'),
        'model': 'text-embedding-3-small',
        'batch_size': 2,
        'redis_url': 'redis://localhost:6379'
    }

    # Създаване на тестов PDF
    import tempfile
    import fitz

    doc = fitz.open()
    page = doc.new_page()

    test_text = """
    This is a comprehensive test document for the RAG system.
    It contains multiple paragraphs with sufficient content for embedding generation.

    The document tests the full workflow from PDF processing to embedding generation.
    Each section should have enough text to pass the minimum length requirements.

    This ensures that our PDF processor and embedding generator work together correctly.
    The integration test validates the complete pipeline functionality.
    """

    page.insert_text((50, 50), test_text)

    temp_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
    doc.save(temp_file.name)
    doc.close()

    try:
        # Стъпка 1: Обработка на PDF
        pdf_processor = PDFProcessor(pdf_config)
        sections = pdf_processor.process_pdf(temp_file.name)

        assert len(sections) >= 1
        print(f"Extracted {len(sections)} sections from PDF")

        # Стъпка 2: Генериране на ембединги
        embedding_processor = EmbeddingProcessor(embedding_config)
        sections_with_embeddings = await embedding_processor.process_all(sections)

        assert len(sections_with_embeddings) == len(sections)

        # Проверка че всички секции имат ембединги
        for section in sections_with_embeddings:
            assert 'embedding' in section
            assert 'embedding_model' in section
            assert isinstance(section['embedding'], list)
            assert len(section['embedding']) == 1536
            assert section['embedding_model'] == 'text-embedding-3-small'

        print(f"Generated embeddings for {len(sections_with_embeddings)} sections")
        print("Full PDF to embeddings workflow completed successfully!")

    finally:
        # Изтриване на временния файл
        if os.path.exists(temp_file.name):
            os.unlink(temp_file.name)
```

**Валидация**:
- [ ] EmbeddingProcessor клас се създава без грешки
- [ ] `python -c "from embeddings.embedder import EmbeddingProcessor; print('OK')"` работи
- [ ] Redis връзката работи (ако Redis е стартиран)
- [ ] `pytest tests/test_embedder.py -v` минава всички тестове
- [ ] С реален OpenAI ключ се генерират ембединги
- [ ] `pytest tests/test_embedder_integration.py -v` минава интеграционния тест
- [ ] Кеширането работи правилно

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че embedding генераторът работи с реален OpenAI API**
- [ ] **Тествал съм с реални текстове и получавам 1536-размерни вектори**
- [ ] **НЕ лъжа и НЕ си измислям - ембедингите са реални и кешират се правилно**

---

## 📋 ФАЗА 6: Векторно търсене и hybrid search (Седмица 4-5)

### Задача 6.1: Stored procedures за hybrid search
**Цел**: Създаване на оптимизирани SQL функции за векторно и текстово търсене

**Стъпки**:
1. Създайте `database/functions.sql`
2. Имплементирайте tsvector тригер
3. Създайте hybrid search функция
4. Тествайте производителността

**database/functions.sql**:
```sql
-- Тригер за автоматично обновяване на tsvector колоната
CREATE OR REPLACE FUNCTION update_content_tsv()
RETURNS TRIGGER AS $$
BEGIN
    -- Динамично избиране на език за tsvector
    CASE NEW.language
        WHEN 'bg' THEN NEW.content_tsv := to_tsvector('simple', NEW.content);
        WHEN 'en' THEN NEW.content_tsv := to_tsvector('english', NEW.content);
        WHEN 'de' THEN NEW.content_tsv := to_tsvector('german', NEW.content);
        WHEN 'fr' THEN NEW.content_tsv := to_tsvector('french', NEW.content);
        ELSE NEW.content_tsv := to_tsvector('simple', NEW.content);
    END CASE;

    -- Генериране на content_hash ако липсва
    IF NEW.content_hash IS NULL THEN
        NEW.content_hash := encode(sha256(NEW.content::bytea), 'hex');
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Създаване на тригер
DROP TRIGGER IF EXISTS trg_update_content_tsv ON document_sections;
CREATE TRIGGER trg_update_content_tsv
BEFORE INSERT OR UPDATE ON document_sections
FOR EACH ROW
EXECUTE FUNCTION update_content_tsv();

-- Оптимизирана hybrid search функция
CREATE OR REPLACE FUNCTION hybrid_search_documents(
    query_embedding vector(1536),
    query_text text,
    query_language text DEFAULT 'english',
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 10,
    vector_weight float DEFAULT 0.7,
    text_weight float DEFAULT 0.3
)
RETURNS TABLE (
    id uuid,
    content text,
    document_id uuid,
    section_type text,
    similarity float,
    text_rank float,
    combined_score float,
    metadata jsonb
) AS $$
DECLARE
    query_tsquery tsquery;
BEGIN
    -- Създаване на tsquery според езика
    CASE query_language
        WHEN 'english' THEN query_tsquery := plainto_tsquery('english', query_text);
        WHEN 'german' THEN query_tsquery := plainto_tsquery('german', query_text);
        WHEN 'french' THEN query_tsquery := plainto_tsquery('french', query_text);
        WHEN 'bulgarian' THEN query_tsquery := plainto_tsquery('simple', query_text);
        ELSE query_tsquery := plainto_tsquery('simple', query_text);
    END CASE;

    RETURN QUERY
    SELECT
        ds.id,
        ds.content,
        ds.document_id,
        ds.section_type,
        (1 - (ds.embedding <=> query_embedding))::float as similarity,
        ts_rank(ds.content_tsv, query_tsquery)::float as text_rank,
        (
            vector_weight * (1 - (ds.embedding <=> query_embedding)) +
            text_weight * ts_rank(ds.content_tsv, query_tsquery)
        )::float as combined_score,
        COALESCE(ds.metadata, '{}'::jsonb) as metadata
    FROM document_sections ds
    WHERE
        ds.content_tsv @@ query_tsquery  -- Бързо текстово търсене с GIN индекс
        AND (1 - (ds.embedding <=> query_embedding)) > match_threshold
        AND ds.is_embedded = true  -- Само секции с ембединги
    ORDER BY combined_score DESC
    LIMIT match_count;
END;
$$ LANGUAGE plpgsql;

-- Функция само за векторно търсене
CREATE OR REPLACE FUNCTION vector_search_documents(
    query_embedding vector(1536),
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 10
)
RETURNS TABLE (
    id uuid,
    content text,
    document_id uuid,
    section_type text,
    similarity float,
    metadata jsonb
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        ds.id,
        ds.content,
        ds.document_id,
        ds.section_type,
        (1 - (ds.embedding <=> query_embedding))::float as similarity,
        COALESCE(ds.metadata, '{}'::jsonb) as metadata
    FROM document_sections ds
    WHERE
        (1 - (ds.embedding <=> query_embedding)) > match_threshold
        AND ds.is_embedded = true
    ORDER BY ds.embedding <=> query_embedding
    LIMIT match_count;
END;
$$ LANGUAGE plpgsql;

-- Функция за текстово търсене
CREATE OR REPLACE FUNCTION text_search_documents(
    query_text text,
    query_language text DEFAULT 'english',
    match_count int DEFAULT 10
)
RETURNS TABLE (
    id uuid,
    content text,
    document_id uuid,
    section_type text,
    text_rank float,
    metadata jsonb
) AS $$
DECLARE
    query_tsquery tsquery;
BEGIN
    CASE query_language
        WHEN 'english' THEN query_tsquery := plainto_tsquery('english', query_text);
        WHEN 'german' THEN query_tsquery := plainto_tsquery('german', query_text);
        WHEN 'french' THEN query_tsquery := plainto_tsquery('french', query_text);
        WHEN 'bulgarian' THEN query_tsquery := plainto_tsquery('simple', query_text);
        ELSE query_tsquery := plainto_tsquery('simple', query_text);
    END CASE;

    RETURN QUERY
    SELECT
        ds.id,
        ds.content,
        ds.document_id,
        ds.section_type,
        ts_rank(ds.content_tsv, query_tsquery)::float as text_rank,
        COALESCE(ds.metadata, '{}'::jsonb) as metadata
    FROM document_sections ds
    WHERE ds.content_tsv @@ query_tsquery
    ORDER BY ts_rank(ds.content_tsv, query_tsquery) DESC
    LIMIT match_count;
END;
$$ LANGUAGE plpgsql;

-- Функция за статистики
CREATE OR REPLACE FUNCTION get_search_stats()
RETURNS TABLE (
    total_documents bigint,
    total_sections bigint,
    embedded_sections bigint,
    languages_count bigint,
    avg_content_length float
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        (SELECT COUNT(*) FROM documents) as total_documents,
        (SELECT COUNT(*) FROM document_sections) as total_sections,
        (SELECT COUNT(*) FROM document_sections WHERE is_embedded = true) as embedded_sections,
        (SELECT COUNT(DISTINCT language) FROM document_sections) as languages_count,
        (SELECT AVG(LENGTH(content)) FROM document_sections) as avg_content_length;
END;
$$ LANGUAGE plpgsql;
```

**Тестов файл `tests/test_search_functions.py`**:
```python
import pytest
import numpy as np
from database.db_manager import DatabaseManager

@pytest.fixture
def db_manager():
    return DatabaseManager()

def test_database_functions_exist(db_manager):
    """Тест че всички функции са създадени"""

    # Проверка за съществуване на функции
    functions_query = """
    SELECT routine_name
    FROM information_schema.routines
    WHERE routine_type = 'FUNCTION'
    AND routine_schema = 'public'
    AND routine_name IN (
        'update_content_tsv',
        'hybrid_search_documents',
        'vector_search_documents',
        'text_search_documents',
        'get_search_stats'
    )
    """

    result = db_manager.client.rpc('sql', {'query': functions_query}).execute()
    function_names = [row['routine_name'] for row in result.data]

    expected_functions = [
        'update_content_tsv',
        'hybrid_search_documents',
        'vector_search_documents',
        'text_search_documents',
        'get_search_stats'
    ]

    for func in expected_functions:
        assert func in function_names, f"Function {func} not found"

def test_tsvector_trigger(db_manager):
    """Тест на tsvector тригер"""

    # Вмъкване на тестова секция
    test_section = {
        'content': 'This is a test document section for full text search testing.',
        'section_index': 999,
        'section_type': 'paragraph',
        'language': 'en'
    }

    result = db_manager.client.table('document_sections').insert(test_section).execute()
    section_id = result.data[0]['id']

    try:
        # Проверка че content_tsv е попълнен автоматично
        check_result = db_manager.client.table('document_sections') \
            .select('content_tsv, content_hash') \
            .eq('id', section_id) \
            .execute()

        section_data = check_result.data[0]
        assert section_data['content_tsv'] is not None
        assert section_data['content_hash'] is not None
        assert len(section_data['content_hash']) == 64  # SHA256 хеш

    finally:
        # Изтриване на тестовата секция
        db_manager.client.table('document_sections').delete().eq('id', section_id).execute()

def test_search_stats_function(db_manager):
    """Тест на статистическата функция"""

    try:
        result = db_manager.client.rpc('get_search_stats').execute()
        stats = result.data[0]

        assert 'total_documents' in stats
        assert 'total_sections' in stats
        assert 'embedded_sections' in stats
        assert 'languages_count' in stats
        assert 'avg_content_length' in stats

        # Проверка че стойностите са числа
        assert isinstance(stats['total_documents'], int)
        assert isinstance(stats['total_sections'], int)
        assert isinstance(stats['embedded_sections'], int)
        assert isinstance(stats['languages_count'], int)
        assert isinstance(stats['avg_content_length'], (int, float)) or stats['avg_content_length'] is None

    except Exception as e:
        pytest.fail(f"get_search_stats function failed: {e}")

def test_text_search_function(db_manager):
    """Тест на текстово търсене"""

    # Вмъкване на тестова секция с известно съдържание
    test_content = "This document contains information about artificial intelligence and machine learning algorithms."
    test_section = {
        'content': test_content,
        'section_index': 998,
        'section_type': 'paragraph',
        'language': 'en'
    }

    result = db_manager.client.table('document_sections').insert(test_section).execute()
    section_id = result.data[0]['id']

    try:
        # Търсене на текст
        search_result = db_manager.client.rpc('text_search_documents', {
            'query_text': 'artificial intelligence',
            'query_language': 'english',
            'match_count': 5
        }).execute()

        # Проверка че функцията работи (може да няма резултати ако няма други данни)
        assert isinstance(search_result.data, list)

        # Ако има резултати, проверяваме структурата
        if search_result.data:
            first_result = search_result.data[0]
            assert 'id' in first_result
            assert 'content' in first_result
            assert 'text_rank' in first_result
            assert isinstance(first_result['text_rank'], (int, float))

    finally:
        # Изтриване на тестовата секция
        db_manager.client.table('document_sections').delete().eq('id', section_id).execute()

@pytest.mark.skipif(True, reason="Requires real embeddings data")
def test_vector_search_function(db_manager):
    """Тест на векторно търсене (изисква реални ембединги)"""

    # Този тест ще работи само ако има реални данни с ембединги
    # Създаваме фиктивен embedding за тестване
    fake_embedding = [0.1] * 1536  # 1536-размерен вектор

    try:
        search_result = db_manager.client.rpc('vector_search_documents', {
            'query_embedding': fake_embedding,
            'match_threshold': 0.5,
            'match_count': 5
        }).execute()

        assert isinstance(search_result.data, list)

    except Exception as e:
        pytest.skip(f"Vector search test skipped: {e}")

def test_hybrid_search_function_structure(db_manager):
    """Тест на структурата на hybrid search функцията"""

    # Тест с фиктивни данни за да проверим че функцията съществува
    fake_embedding = [0.1] * 1536

    try:
        search_result = db_manager.client.rpc('hybrid_search_documents', {
            'query_embedding': fake_embedding,
            'query_text': 'test query',
            'query_language': 'english',
            'match_threshold': 0.5,
            'match_count': 5,
            'vector_weight': 0.7,
            'text_weight': 0.3
        }).execute()

        assert isinstance(search_result.data, list)

        # Ако има резултати, проверяваме структурата
        if search_result.data:
            first_result = search_result.data[0]
            expected_fields = ['id', 'content', 'document_id', 'section_type',
                             'similarity', 'text_rank', 'combined_score', 'metadata']

            for field in expected_fields:
                assert field in first_result, f"Field {field} missing from result"

    except Exception as e:
        # Ако функцията не съществува или има грешка в SQL
        pytest.fail(f"Hybrid search function test failed: {e}")
```

**Скрипт за изпълнение на функциите `scripts/setup_database_functions.py`**:
```python
#!/usr/bin/env python3
"""
Скрипт за създаване на database функции
"""

import os
from database.db_manager import DatabaseManager

def setup_database_functions():
    """Създава всички database функции"""

    db_manager = DatabaseManager()

    # Четене на SQL файла
    functions_sql_path = os.path.join(os.path.dirname(__file__), '..', 'database', 'functions.sql')

    if not os.path.exists(functions_sql_path):
        print(f"ERROR: {functions_sql_path} not found")
        return False

    with open(functions_sql_path, 'r', encoding='utf-8') as f:
        sql_content = f.read()

    # Разделяне на SQL командите
    sql_commands = [cmd.strip() for cmd in sql_content.split(';') if cmd.strip()]

    print(f"Executing {len(sql_commands)} SQL commands...")

    for i, command in enumerate(sql_commands):
        try:
            print(f"Executing command {i+1}/{len(sql_commands)}")

            # Изпълнение на SQL команда
            result = db_manager.client.rpc('sql', {'query': command}).execute()
            print(f"✓ Command {i+1} executed successfully")

        except Exception as e:
            print(f"✗ Command {i+1} failed: {e}")
            print(f"Command was: {command[:100]}...")
            return False

    print("All database functions created successfully!")
    return True

def test_functions():
    """Тества създадените функции"""

    db_manager = DatabaseManager()

    print("Testing database functions...")

    try:
        # Тест на статистики
        stats_result = db_manager.client.rpc('get_search_stats').execute()
        print(f"✓ get_search_stats: {stats_result.data[0]}")

        # Тест на текстово търсене
        text_result = db_manager.client.rpc('text_search_documents', {
            'query_text': 'test',
            'match_count': 1
        }).execute()
        print(f"✓ text_search_documents: {len(text_result.data)} results")

        print("All function tests passed!")
        return True

    except Exception as e:
        print(f"✗ Function test failed: {e}")
        return False

if __name__ == "__main__":
    print("Setting up database functions...")

    if setup_database_functions():
        print("\nTesting functions...")
        test_functions()
    else:
        print("Setup failed!")
```

**Валидация**:
- [ ] SQL функциите се създават без грешки
- [ ] `python scripts/setup_database_functions.py` изпълнява успешно
- [ ] Тригерът за tsvector работи автоматично
- [ ] `pytest tests/test_search_functions.py -v` минава всички тестове
- [ ] `get_search_stats()` връща валидни статистики
- [ ] Текстовото търсене работи с реални данни
- [ ] EXPLAIN показва че се използват правилните индекси

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че всички search функции работят правилно**
- [ ] **Тествал съм с реални данни и получавам резултати**
- [ ] **НЕ лъжа и НЕ си измислям - функциите са реални и оптимизирани**

---

### Задача 6.2: Подобрен MCP сървър с векторно търсене
**Цел**: Актуализиране на MCP сървъра за използване на hybrid search

**Стъпки**:
1. Актуализирайте `mcp_server/advanced_server.py`
2. Добавете векторно търсене с ембединги
3. Имплементирайте кеширане с Redis
4. Тествайте с реални заявки

**mcp_server/advanced_server.py**:
```python
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import logging
import os
import time
import json
import hashlib
from datetime import datetime
from dotenv import load_dotenv
import redis
from openai import AsyncOpenAI
from database.db_manager import DatabaseManager
from prometheus_client import Counter, Histogram, generate_latest
from prometheus_client import CONTENT_TYPE_LATEST

# Зареждане на environment variables
load_dotenv()

# Метрики
SEARCH_REQUESTS = Counter('search_requests_total', 'Total search requests', ['search_type'])
SEARCH_CACHE_HITS = Counter('search_cache_hits_total', 'Search cache hits')
SEARCH_LATENCY = Histogram('search_latency_seconds', 'Search request latency', ['search_type'])
EMBEDDING_GENERATION_TIME = Histogram('embedding_generation_seconds', 'Time to generate query embedding')

# Инициализация на FastAPI
app = FastAPI(
    title="Advanced RAG MCP Server",
    description="Model Context Protocol Server with Vector Search",
    version="2.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Глобални клиенти
db_manager = DatabaseManager()
openai_client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
redis_client = redis.from_url(os.getenv("REDIS_URL", "redis://localhost:6379"))

# Логгер
logger = logging.getLogger("advanced_mcp_server")

# Pydantic модели
class AdvancedSearchQuery(BaseModel):
    query: str = Field(..., min_length=1, max_length=500)
    search_type: str = Field(default="hybrid", regex="^(vector|text|hybrid)$")
    limit: int = Field(default=5, ge=1, le=20)
    threshold: float = Field(default=0.7, ge=0, le=1.0)
    vector_weight: float = Field(default=0.7, ge=0, le=1.0)
    text_weight: float = Field(default=0.3, ge=0, le=1.0)
    language: str = Field(default="english")

class SearchResult(BaseModel):
    id: str
    content: str
    document_id: Optional[str]
    section_type: str
    similarity: Optional[float]
    text_rank: Optional[float]
    combined_score: Optional[float]
    metadata: Dict[str, Any]

class AdvancedSearchResponse(BaseModel):
    query: str
    search_type: str
    results: List[SearchResult]
    total_found: int
    search_time_ms: float
    embedding_time_ms: Optional[float]
    cache_hit: bool

class SystemStats(BaseModel):
    total_documents: int
    total_sections: int
    embedded_sections: int
    languages_count: int
    avg_content_length: float
    system_health: str

# Utility функции
async def generate_query_embedding(query: str) -> List[float]:
    """Генериране на ембединг за търсещата заявка"""
    with EMBEDDING_GENERATION_TIME.time():
        try:
            response = await openai_client.embeddings.create(
                model="text-embedding-3-small",
                input=query
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            raise HTTPException(status_code=500, detail="Failed to generate embedding")

def get_cache_key(query: str, search_type: str, limit: int, threshold: float) -> str:
    """Генериране на ключ за кеширане"""
    cache_data = f"{query}:{search_type}:{limit}:{threshold}"
    return f"search:{hashlib.md5(cache_data.encode()).hexdigest()}"

def cache_search_result(cache_key: str, result: dict, ttl: int = 300):
    """Кеширане на резултат от търсене"""
    try:
        redis_client.setex(cache_key, ttl, json.dumps(result, default=str))
    except Exception as e:
        logger.warning(f"Failed to cache result: {e}")

def get_cached_result(cache_key: str) -> Optional[dict]:
    """Получаване на кеширан резултат"""
    try:
        cached = redis_client.get(cache_key)
        if cached:
            SEARCH_CACHE_HITS.inc()
            return json.loads(cached)
        return None
    except Exception as e:
        logger.warning(f"Failed to get cached result: {e}")
        return None

# Middleware за логиране и метрики
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()

    response = await call_next(request)

    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)

    logger.info(
        f"{request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Time: {process_time:.3f}s"
    )

    return response

# Health check endpoint
@app.get("/health")
async def health_check():
    """Comprehensive health check"""
    try:
        # Тест на база данни
        db_connected = db_manager.test_connection()

        # Тест на Redis
        redis_connected = False
        try:
            redis_client.ping()
            redis_connected = True
        except:
            pass

        # Тест на OpenAI (опционален)
        openai_connected = bool(os.getenv("OPENAI_API_KEY"))

        overall_status = "healthy"
        if not db_connected:
            overall_status = "unhealthy"
        elif not redis_connected or not openai_connected:
            overall_status = "degraded"

        return {
            "status": overall_status,
            "timestamp": datetime.now().isoformat(),
            "services": {
                "database": db_connected,
                "redis": redis_connected,
                "openai": openai_connected
            },
            "version": "2.0.0"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")

# Advanced search endpoint
@app.post("/search", response_model=AdvancedSearchResponse)
async def advanced_search(query: AdvancedSearchQuery):
    """Advanced search with vector, text, or hybrid modes"""
    start_time = time.time()
    embedding_time = None

    SEARCH_REQUESTS.labels(search_type=query.search_type).inc()

    try:
        # Проверка за кеширан резултат
        cache_key = get_cache_key(query.query, query.search_type, query.limit, query.threshold)
        cached_result = get_cached_result(cache_key)

        if cached_result:
            cached_result['cache_hit'] = True
            return AdvancedSearchResponse(**cached_result)

        # Генериране на ембединг ако е нужен
        query_embedding = None
        if query.search_type in ['vector', 'hybrid']:
            embedding_start = time.time()
            query_embedding = await generate_query_embedding(query.query)
            embedding_time = (time.time() - embedding_start) * 1000

        # Изпълнение на търсене според типа
        search_results = []

        if query.search_type == 'vector':
            result = db_manager.client.rpc('vector_search_documents', {
                'query_embedding': query_embedding,
                'match_threshold': query.threshold,
                'match_count': query.limit
            }).execute()

            for row in result.data:
                search_results.append(SearchResult(
                    id=row['id'],
                    content=row['content'],
                    document_id=row.get('document_id'),
                    section_type=row['section_type'],
                    similarity=row['similarity'],
                    text_rank=None,
                    combined_score=row['similarity'],
                    metadata=row.get('metadata', {})
                ))

        elif query.search_type == 'text':
            result = db_manager.client.rpc('text_search_documents', {
                'query_text': query.query,
                'query_language': query.language,
                'match_count': query.limit
            }).execute()

            for row in result.data:
                search_results.append(SearchResult(
                    id=row['id'],
                    content=row['content'],
                    document_id=row.get('document_id'),
                    section_type=row['section_type'],
                    similarity=None,
                    text_rank=row['text_rank'],
                    combined_score=row['text_rank'],
                    metadata=row.get('metadata', {})
                ))

        elif query.search_type == 'hybrid':
            result = db_manager.client.rpc('hybrid_search_documents', {
                'query_embedding': query_embedding,
                'query_text': query.query,
                'query_language': query.language,
                'match_threshold': query.threshold,
                'match_count': query.limit,
                'vector_weight': query.vector_weight,
                'text_weight': query.text_weight
            }).execute()

            for row in result.data:
                search_results.append(SearchResult(
                    id=row['id'],
                    content=row['content'],
                    document_id=row.get('document_id'),
                    section_type=row['section_type'],
                    similarity=row['similarity'],
                    text_rank=row['text_rank'],
                    combined_score=row['combined_score'],
                    metadata=row.get('metadata', {})
                ))

        search_time = (time.time() - start_time) * 1000

        response_data = {
            "query": query.query,
            "search_type": query.search_type,
            "results": search_results,
            "total_found": len(search_results),
            "search_time_ms": round(search_time, 2),
            "embedding_time_ms": round(embedding_time, 2) if embedding_time else None,
            "cache_hit": False
        }

        # Кеширане на резултата
        cache_search_result(cache_key, response_data)

        # Записване на метрика
        SEARCH_LATENCY.labels(search_type=query.search_type).observe(search_time / 1000)

        return AdvancedSearchResponse(**response_data)

    except Exception as e:
        logger.error(f"Search failed: {e}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

# System statistics endpoint
@app.get("/stats", response_model=SystemStats)
async def get_system_stats():
    """Get system statistics"""
    try:
        result = db_manager.client.rpc('get_search_stats').execute()
        stats = result.data[0]

        # Определяне на system health
        embedded_ratio = stats['embedded_sections'] / max(stats['total_sections'], 1)
        if embedded_ratio > 0.8:
            health = "excellent"
        elif embedded_ratio > 0.5:
            health = "good"
        elif embedded_ratio > 0.2:
            health = "fair"
        else:
            health = "poor"

        return SystemStats(
            total_documents=stats['total_documents'],
            total_sections=stats['total_sections'],
            embedded_sections=stats['embedded_sections'],
            languages_count=stats['languages_count'],
            avg_content_length=stats['avg_content_length'] or 0,
            system_health=health
        )

    except Exception as e:
        logger.error(f"Failed to get stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get system statistics")

# Prometheus metrics endpoint
@app.get("/metrics")
async def get_metrics():
    """Prometheus metrics endpoint"""
    from fastapi.responses import Response
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Advanced RAG MCP Server",
        "version": "2.0.0",
        "features": [
            "Vector search",
            "Text search",
            "Hybrid search",
            "Redis caching",
            "Prometheus metrics"
        ],
        "endpoints": {
            "health": "/health",
            "search": "/search",
            "stats": "/stats",
            "metrics": "/metrics",
            "docs": "/docs"
        }
    }

# Стартиране на сървъра
if __name__ == "__main__":
    import uvicorn

    # Конфигуриране на логиране
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Стартиране на сървъра
    uvicorn.run(
        "advanced_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
```

**Тестов файл `tests/test_advanced_server.py`**:
```python
import pytest
import json
import os
from fastapi.testclient import TestClient
from mcp_server.advanced_server import app

@pytest.fixture
def client():
    return TestClient(app)

def test_root_endpoint(client):
    """Тест на root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data
    assert data["version"] == "2.0.0"
    assert "features" in data
    assert "endpoints" in data

def test_health_endpoint(client):
    """Тест на health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "timestamp" in data
    assert "services" in data
    assert "version" in data

    services = data["services"]
    assert "database" in services
    assert "redis" in services
    assert "openai" in services

def test_stats_endpoint(client):
    """Тест на statistics endpoint"""
    response = client.get("/stats")
    assert response.status_code == 200
    data = response.json()

    expected_fields = [
        "total_documents", "total_sections", "embedded_sections",
        "languages_count", "avg_content_length", "system_health"
    ]

    for field in expected_fields:
        assert field in data

    assert isinstance(data["total_documents"], int)
    assert isinstance(data["total_sections"], int)
    assert isinstance(data["embedded_sections"], int)
    assert data["system_health"] in ["excellent", "good", "fair", "poor"]

def test_search_endpoint_text_search(client):
    """Тест на text search"""
    search_data = {
        "query": "test query",
        "search_type": "text",
        "limit": 3,
        "language": "english"
    }

    response = client.post("/search", json=search_data)
    assert response.status_code == 200

    data = response.json()
    assert "query" in data
    assert "search_type" in data
    assert "results" in data
    assert "total_found" in data
    assert "search_time_ms" in data
    assert "cache_hit" in data

    assert data["query"] == "test query"
    assert data["search_type"] == "text"
    assert isinstance(data["results"], list)
    assert isinstance(data["total_found"], int)
    assert isinstance(data["search_time_ms"], float)
    assert isinstance(data["cache_hit"], bool)

@pytest.mark.skipif(not os.getenv('OPENAI_API_KEY'), reason="OpenAI API key not provided")
def test_search_endpoint_vector_search(client):
    """Тест на vector search (изисква OpenAI ключ)"""
    search_data = {
        "query": "artificial intelligence",
        "search_type": "vector",
        "limit": 3,
        "threshold": 0.7
    }

    response = client.post("/search", json=search_data)
    assert response.status_code == 200

    data = response.json()
    assert data["search_type"] == "vector"
    assert "embedding_time_ms" in data
    assert isinstance(data["embedding_time_ms"], float)

@pytest.mark.skipif(not os.getenv('OPENAI_API_KEY'), reason="OpenAI API key not provided")
def test_search_endpoint_hybrid_search(client):
    """Тест на hybrid search (изисква OpenAI ключ)"""
    search_data = {
        "query": "machine learning algorithms",
        "search_type": "hybrid",
        "limit": 5,
        "threshold": 0.6,
        "vector_weight": 0.7,
        "text_weight": 0.3
    }

    response = client.post("/search", json=search_data)
    assert response.status_code == 200

    data = response.json()
    assert data["search_type"] == "hybrid"

    # Проверка на резултатите ако има такива
    if data["results"]:
        first_result = data["results"][0]
        assert "id" in first_result
        assert "content" in first_result
        assert "section_type" in first_result
        assert "metadata" in first_result

def test_search_endpoint_invalid_search_type(client):
    """Тест на невалиден search type"""
    search_data = {
        "query": "test",
        "search_type": "invalid_type",
        "limit": 3
    }

    response = client.post("/search", json=search_data)
    assert response.status_code == 422  # Validation error

def test_search_endpoint_invalid_limit(client):
    """Тест на невалиден лимит"""
    search_data = {
        "query": "test",
        "search_type": "text",
        "limit": 50  # Над максимума от 20
    }

    response = client.post("/search", json=search_data)
    assert response.status_code == 422  # Validation error

def test_metrics_endpoint(client):
    """Тест на Prometheus metrics endpoint"""
    response = client.get("/metrics")
    assert response.status_code == 200
    assert "text/plain" in response.headers["content-type"]

    # Проверка че съдържа Prometheus метрики
    content = response.text
    assert "search_requests_total" in content
    assert "search_latency_seconds" in content

def test_openapi_docs(client):
    """Тест че OpenAPI документацията е достъпна"""
    response = client.get("/docs")
    assert response.status_code == 200

    response = client.get("/openapi.json")
    assert response.status_code == 200
    openapi_data = response.json()
    assert "openapi" in openapi_data
    assert "info" in openapi_data
    assert openapi_data["info"]["version"] == "2.0.0"
```

**Валидация**:
- [ ] Advanced MCP сървърът стартира без грешки
- [ ] `python mcp_server/advanced_server.py` стартира на порт 8000
- [ ] Всички endpoints отговарят правилно
- [ ] `pytest tests/test_advanced_server.py -v` минава всички тестове
- [ ] Text search работи без OpenAI ключ
- [ ] Vector/hybrid search работи с OpenAI ключ
- [ ] Redis кеширането работи (ако Redis е стартиран)
- [ ] Prometheus метриките се генерират правилно

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че advanced MCP сървърът работи с векторно търсене**
- [ ] **Тествал съм всички типове търсене и получавам резултати**
- [ ] **НЕ лъжа и НЕ си измислям - сървърът е реален и функционален с hybrid search**

---

## 📋 ФАЗА 7: Пълен ETL pipeline с Prefect (Седмица 5-6)

### Задача 7.1: Prefect workflows за автоматизация
**Цел**: Създаване на автоматизирани workflows за пълния ETL процес

**Стъпки**:
1. Създайте `workflows/rag_pipeline.py`
2. Имплементирайте идемпотентни tasks
3. Добавете error handling и retry логика
4. Тествайте с реални данни

**workflows/rag_pipeline.py**:
```python
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from prefect import flow, task, get_run_logger
from prefect.task_runners import ConcurrentTaskRunner
from prefect.concurrency import RateLimit
import hashlib
import time

# Импортиране на компоненти
from crawlers.html_crawler import HTMLCrawler
from processors.pdf_processor import PDFProcessor
from embeddings.embedder import EmbeddingProcessor
from database.db_manager import DatabaseManager

# Конфигурации
CRAWLER_CONFIG = {
    'min_delay': 1.0,
    'max_delay': 3.0,
    'concurrent_requests': 5
}

PDF_CONFIG = {
    'batch_size': 10,
    'extract_tables': True,
    'min_text_length': 50,
    'section_splitter': 'blocks'
}

EMBEDDING_CONFIG = {
    'api_key': os.getenv('OPENAI_API_KEY'),
    'model': 'text-embedding-3-small',
    'batch_size': 20,
    'max_tokens_per_minute': 1000000,
    'redis_url': 'redis://localhost:6379'
}

@task(
    retries=3,
    retry_delay_seconds=60,
    cache_key_fn=lambda context, parameters: f"crawl_{hash(str(parameters['site_configs']))}"
)
async def crawl_websites_batch(site_configs: List[Dict]) -> Dict[str, Any]:
    """Идемпотентен краулинг на batch от сайтове"""
    logger = get_run_logger()
    logger.info(f"Crawling {len(site_configs)} websites")

    # Проверка за вече краулнати страници в последните 24 часа
    cutoff_time = datetime.now() - timedelta(hours=24)
    db_manager = DatabaseManager()

    new_site_configs = []
    for config in site_configs:
        for url in config.get('start_urls', []):
            # Проверка дали URL-ът е краулван наскоро
            existing = db_manager.client.table('raw_pages') \
                .select('id, fetch_time') \
                .eq('url', url) \
                .gte('fetch_time', cutoff_time.isoformat()) \
                .execute()

            if not existing.data:
                new_site_configs.append(config)
                break

    if not new_site_configs:
        logger.info("All sites already crawled recently, skipping")
        return {
            "crawled_pages": 0,
            "success_rate": 1.0,
            "skipped": len(site_configs),
            "timestamp": datetime.now().isoformat()
        }

    # Краулинг на новите сайтове
    total_crawled = 0
    total_successful = 0

    for config in new_site_configs:
        try:
            crawler = HTMLCrawler(CRAWLER_CONFIG)
            async with crawler:
                results = await crawler.run_crawl(config['start_urls'])
                total_crawled += results['total_urls']
                total_successful += results['successful']
        except Exception as e:
            logger.error(f"Failed to crawl {config}: {e}")

    return {
        "crawled_pages": total_crawled,
        "successful_pages": total_successful,
        "success_rate": total_successful / max(total_crawled, 1),
        "skipped": len(site_configs) - len(new_site_configs),
        "timestamp": datetime.now().isoformat()
    }

@task(
    retries=2,
    retry_delay_seconds=30,
    cache_key_fn=lambda context, parameters: f"process_pages_{hash(str(parameters['page_ids']))}"
)
async def process_raw_pages(page_ids: List[str]) -> Dict[str, Any]:
    """Обработка на сурови HTML страници"""
    logger = get_run_logger()
    logger.info(f"Processing {len(page_ids)} raw pages")

    db_manager = DatabaseManager()
    processed_count = 0

    for page_id in page_ids:
        try:
            # Получаване на страницата
            page_result = db_manager.client.table('raw_pages') \
                .select('*') \
                .eq('id', page_id) \
                .eq('is_processed', False) \
                .execute()

            if not page_result.data:
                continue  # Вече обработена или не съществува

            page = page_result.data[0]

            # Обработка на HTML съдържанието
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(page['html_content'], 'html.parser')

            # Премахване на script и style тагове
            for script in soup(["script", "style"]):
                script.decompose()

            clean_text = soup.get_text(separator=' ', strip=True)

            # Проверка за минимална дължина
            if len(clean_text) < 100:
                # Маркиране като обработена но без създаване на clean_page
                db_manager.mark_page_processed(page_id)
                continue

            # Детекция на език
            from processors.pdf_processor import LanguageDetector
            lang_detector = LanguageDetector()
            language = lang_detector.detect_language(clean_text)

            # Създаване на content hash
            content_hash = hashlib.sha256(clean_text.encode()).hexdigest()

            # Проверка за дубликати
            existing = db_manager.client.table('clean_pages') \
                .select('id') \
                .eq('content_hash', content_hash) \
                .execute()

            if not existing.data:
                # Създаване на clean page
                clean_page_data = {
                    'raw_page_id': page_id,
                    'content': clean_text,
                    'language': language,
                    'content_hash': content_hash,
                    'metadata': {
                        'title': page.get('metadata', {}).get('title', ''),
                        'url': page['url'],
                        'original_length': len(page['html_content']),
                        'clean_length': len(clean_text)
                    }
                }

                db_manager.client.table('clean_pages').insert(clean_page_data).execute()

            # Маркиране като обработена
            db_manager.mark_page_processed(page_id)
            processed_count += 1

        except Exception as e:
            logger.error(f"Failed to process page {page_id}: {e}")

    return {
        "processed_pages": processed_count,
        "total_pages": len(page_ids),
        "timestamp": datetime.now().isoformat()
    }

@task(
    retries=2,
    retry_delay_seconds=30,
    cache_key_fn=lambda context, parameters: f"process_pdfs_{hash(str(parameters['pdf_paths']))}"
)
async def process_pdfs_batch(pdf_paths: List[str]) -> Dict[str, Any]:
    """Обработка на batch от PDF файлове"""
    logger = get_run_logger()
    logger.info(f"Processing {len(pdf_paths)} PDF files")

    pdf_processor = PDFProcessor(PDF_CONFIG)
    processed_sections = []

    for pdf_path in pdf_paths:
        try:
            sections = pdf_processor.process_pdf(pdf_path)
            processed_sections.extend(sections)
        except Exception as e:
            logger.error(f"Failed to process {pdf_path}: {e}")

    return {
        "processed_files": len(pdf_paths),
        "extracted_sections": len(processed_sections),
        "sections": processed_sections
    }

@task(
    retries=3,
    retry_delay_seconds=120,
    cache_key_fn=lambda context, parameters: f"generate_embeddings_{hash(str([s['content_hash'] for s in parameters['sections']]))}"
)
async def generate_embeddings_batch(sections: List[Dict]) -> Dict[str, Any]:
    """Генериране на ембединги за batch от секции"""
    logger = get_run_logger()
    logger.info(f"Generating embeddings for {len(sections)} sections")

    embedding_processor = EmbeddingProcessor(EMBEDDING_CONFIG)

    try:
        processed_sections = await embedding_processor.process_all(sections)

        return {
            "processed_sections": len(processed_sections),
            "sections_with_embeddings": processed_sections,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to generate embeddings: {e}")
        raise

@task(
    cache_key_fn=lambda context, parameters: f"save_db_{hash(str([s.get('content_hash', '') for s in parameters['sections_with_embeddings']]))}"
)
async def save_to_database(sections_with_embeddings: List[Dict]) -> Dict[str, Any]:
    """Идемпотентно запазване на секциите с ембединги в базата данни"""
    logger = get_run_logger()

    db_manager = DatabaseManager()
    saved_count = 0
    updated_count = 0
    skipped_count = 0

    for section in sections_with_embeddings:
        try:
            content_hash = section.get('content_hash')
            if not content_hash:
                content_hash = hashlib.sha256(section['content'].encode()).hexdigest()
                section['content_hash'] = content_hash

            # Проверка за съществуващи записи
            existing = db_manager.client.table('document_sections') \
                .select('id, is_embedded') \
                .eq('content_hash', content_hash) \
                .execute()

            if not existing.data:
                # Нов запис
                section['is_embedded'] = True
                db_manager.client.table('document_sections').insert(section).execute()
                saved_count += 1
            elif not existing.data[0].get('is_embedded', False):
                # Актуализиране на съществуващ запис без ембединг
                db_manager.client.table('document_sections') \
                    .update({
                        'embedding': section['embedding'],
                        'embedding_model': section['embedding_model'],
                        'is_embedded': True
                    }) \
                    .eq('id', existing.data[0]['id']) \
                    .execute()
                updated_count += 1
            else:
                skipped_count += 1

        except Exception as e:
            logger.error(f"Failed to save section: {e}")

    return {
        "saved_sections": saved_count,
        "updated_sections": updated_count,
        "skipped_sections": skipped_count
    }

@task
async def get_unprocessed_data() -> Dict[str, Any]:
    """Получаване на необработени данни"""
    logger = get_run_logger()

    db_manager = DatabaseManager()

    # Необработени страници
    unprocessed_pages = db_manager.get_unprocessed_pages(limit=100)

    # Секции без ембединги
    unembedded_result = db_manager.client.table('document_sections') \
        .select('*') \
        .eq('is_embedded', False) \
        .limit(100) \
        .execute()

    unembedded_sections = unembedded_result.data

    return {
        "unprocessed_pages": [p['id'] for p in unprocessed_pages],
        "unembedded_sections": unembedded_sections,
        "page_count": len(unprocessed_pages),
        "section_count": len(unembedded_sections)
    }

def chunk_list(lst: List, chunk_size: int) -> List[List]:
    """Разделяне на списък на chunks"""
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]

@flow(
    name="daily-rag-pipeline",
    task_runner=ConcurrentTaskRunner(),
    retries=1,
    retry_delay_seconds=300
)
async def daily_rag_pipeline(site_configs: List[Dict] = None):
    """Ежедневен RAG pipeline"""
    logger = get_run_logger()
    logger.info("Starting daily RAG pipeline")

    # Стъпка 1: Краулинг на сайтове
    if site_configs:
        site_batches = chunk_list(site_configs, 3)
        crawl_results = await asyncio.gather(*[
            crawl_websites_batch(batch) for batch in site_batches
        ])

        total_crawled = sum(r.get('crawled_pages', 0) for r in crawl_results)
        logger.info(f"Crawled {total_crawled} pages")

    # Стъпка 2: Получаване на необработени данни
    unprocessed_data = await get_unprocessed_data()

    # Стъпка 3: Обработка на HTML страници
    if unprocessed_data['unprocessed_pages']:
        page_batches = chunk_list(unprocessed_data['unprocessed_pages'], 10)

        process_results = await asyncio.gather(*[
            process_raw_pages(batch) for batch in page_batches
        ])

        total_processed = sum(r.get('processed_pages', 0) for r in process_results)
        logger.info(f"Processed {total_processed} HTML pages")

    # Стъпка 4: Генериране на ембединги за секции без ембединги
    if unprocessed_data['unembedded_sections']:
        section_batches = chunk_list(unprocessed_data['unembedded_sections'], 20)

        embedding_results = await asyncio.gather(*[
            generate_embeddings_batch(batch) for batch in section_batches
        ])

        # Стъпка 5: Запазване на секциите с ембединги
        for result in embedding_results:
            if result.get('sections_with_embeddings'):
                await save_to_database(result['sections_with_embeddings'])

        total_embedded = sum(r.get('processed_sections', 0) for r in embedding_results)
        logger.info(f"Generated embeddings for {total_embedded} sections")

    # Обобщение на резултатите
    logger.info("Daily RAG pipeline completed successfully")

    return {
        "crawled_pages": total_crawled if site_configs else 0,
        "processed_pages": total_processed if unprocessed_data['unprocessed_pages'] else 0,
        "embedded_sections": total_embedded if unprocessed_data['unembedded_sections'] else 0,
        "completion_time": datetime.now().isoformat()
    }

@flow(name="incremental-crawl")
async def incremental_crawl_flow(urls: List[str]):
    """Инкрементален краулинг на конкретни URL-и"""
    logger = get_run_logger()
    logger.info(f"Starting incremental crawl for {len(urls)} URLs")

    site_config = {
        'start_urls': urls,
        'name': 'incremental'
    }

    result = await crawl_websites_batch([site_config])
    logger.info(f"Incremental crawl completed: {result}")

    return result

# Планиране на изпълнение
if __name__ == "__main__":
    # Примерни сайтове за краулинг
    example_sites = [
        {
            'start_urls': ['https://example.com'],
            'name': 'example'
        }
    ]

    # Стартиране на pipeline
    asyncio.run(daily_rag_pipeline(example_sites))
```

**Тестов файл `tests/test_rag_pipeline.py`**:
```python
import pytest
import asyncio
from workflows.rag_pipeline import (
    get_unprocessed_data,
    process_raw_pages,
    generate_embeddings_batch,
    save_to_database,
    chunk_list
)

def test_chunk_list():
    """Тест на chunk_list функцията"""
    test_list = list(range(10))
    chunks = chunk_list(test_list, 3)

    assert len(chunks) == 4
    assert chunks[0] == [0, 1, 2]
    assert chunks[1] == [3, 4, 5]
    assert chunks[2] == [6, 7, 8]
    assert chunks[3] == [9]

@pytest.mark.asyncio
async def test_get_unprocessed_data():
    """Тест на получаване на необработени данни"""
    try:
        result = await get_unprocessed_data()

        assert isinstance(result, dict)
        assert 'unprocessed_pages' in result
        assert 'unembedded_sections' in result
        assert 'page_count' in result
        assert 'section_count' in result

        assert isinstance(result['unprocessed_pages'], list)
        assert isinstance(result['unembedded_sections'], list)
        assert isinstance(result['page_count'], int)
        assert isinstance(result['section_count'], int)

    except Exception as e:
        pytest.skip(f"Database not available: {e}")

@pytest.mark.asyncio
async def test_save_to_database():
    """Тест на запазване в базата данни"""
    test_sections = [
        {
            'content': 'Test section content for database saving test',
            'section_index': 1,
            'section_type': 'paragraph',
            'embedding': [0.1] * 1536,
            'embedding_model': 'text-embedding-3-small',
            'language': 'en',
            'content_hash': 'test_hash_123'
        }
    ]

    try:
        result = await save_to_database(test_sections)

        assert isinstance(result, dict)
        assert 'saved_sections' in result
        assert 'updated_sections' in result
        assert 'skipped_sections' in result

        total_processed = (
            result['saved_sections'] +
            result['updated_sections'] +
            result['skipped_sections']
        )
        assert total_processed == len(test_sections)

    except Exception as e:
        pytest.skip(f"Database not available: {e}")

@pytest.mark.skipif(True, reason="Requires real HTML pages in database")
@pytest.mark.asyncio
async def test_process_raw_pages():
    """Тест на обработка на сурови страници"""
    # Този тест изисква реални данни в базата
    try:
        # Получаване на някои необработени страници
        unprocessed_data = await get_unprocessed_data()

        if unprocessed_data['unprocessed_pages']:
            # Вземаме първите 2 страници за тестване
            test_pages = unprocessed_data['unprocessed_pages'][:2]

            result = await process_raw_pages(test_pages)

            assert isinstance(result, dict)
            assert 'processed_pages' in result
            assert 'total_pages' in result
            assert result['total_pages'] == len(test_pages)
        else:
            pytest.skip("No unprocessed pages available")

    except Exception as e:
        pytest.skip(f"Test requires database with raw pages: {e}")

def test_pipeline_imports():
    """Тест че всички импорти работят"""
    from workflows.rag_pipeline import daily_rag_pipeline, incremental_crawl_flow

    assert callable(daily_rag_pipeline)
    assert callable(incremental_crawl_flow)
```

**Валидация**:
- [ ] Prefect workflows се създават без грешки
- [ ] `python -c "from workflows.rag_pipeline import daily_rag_pipeline; print('OK')"` работи
- [ ] `pytest tests/test_rag_pipeline.py -v` минава всички тестове
- [ ] Идемпотентността работи (повторно изпълнение не дублира данни)
- [ ] Error handling и retry логиката работят правилно
- [ ] Chunking на данни работи за големи обеми
- [ ] Cache-ването на tasks работи правилно

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че Prefect workflows работят с реални данни**
- [ ] **Тествал съм идемпотентността и retry логиката**
- [ ] **НЕ лъжа и НЕ си измислям - pipeline-ът е реален и автоматизиран**

---

## 📋 ФАЗА 8: Production готовност и мониторинг (Седмица 6-7)

### Задача 8.1: Comprehensive мониторинг система
**Цел**: Създаване на пълна мониторинг система с Prometheus и логиране

**Стъпки**:
1. Създайте `monitoring/metrics.py`
2. Имплементирайте structured logging
3. Добавете health checks за всички компоненти
4. Тествайте мониторинга

**monitoring/metrics.py**:
```python
import logging
import json
import time
import psutil
from datetime import datetime
from typing import Dict, Any, Optional
from prometheus_client import Counter, Histogram, Gauge, Info, CollectorRegistry, generate_latest
from prometheus_client import CONTENT_TYPE_LATEST
import asyncio
from database.db_manager import DatabaseManager

# Системни метрики
SYSTEM_CPU = Gauge('system_cpu_usage_percent', 'System CPU usage')
SYSTEM_MEMORY = Gauge('system_memory_usage_percent', 'System memory usage')
SYSTEM_DISK = Gauge('system_disk_usage_percent', 'System disk usage')
DATABASE_CONNECTIONS = Gauge('database_connections_active', 'Active DB connections')

# Бизнес метрики
PAGES_CRAWLED = Counter('pages_crawled_total', 'Total pages crawled', ['domain', 'status'])
EMBEDDINGS_GENERATED = Counter('embeddings_generated_total', 'Total embeddings', ['model', 'status'])
SEARCH_REQUESTS = Counter('search_requests_total', 'Total search requests', ['endpoint', 'search_type'])
SEARCH_LATENCY = Histogram('search_latency_seconds', 'Search latency', ['search_type'])
EMBEDDING_CACHE_HITS = Counter('embedding_cache_hits_total', 'Embedding cache hits')
PDF_PROCESSING_TIME = Histogram('pdf_processing_seconds', 'PDF processing time', ['file_size_mb'])

# API метрики
OPENAI_API_CALLS = Counter('openai_api_calls_total', 'OpenAI API calls', ['model', 'status'])
OPENAI_TOKENS_USED = Counter('openai_tokens_used_total', 'OpenAI tokens used', ['model'])
SUPABASE_API_CALLS = Counter('supabase_api_calls_total', 'Supabase API calls', ['operation'])

# Качество на данните
DUPLICATE_CONTENT_DETECTED = Counter('duplicate_content_detected_total', 'Duplicate content')
CONTENT_QUALITY_SCORE = Histogram('content_quality_score', 'Content quality score')

# Информация за системата
SYSTEM_INFO = Info('rag_system_info', 'RAG system information')
SYSTEM_INFO.info({
    'version': '1.0.0',
    'embedding_model': 'text-embedding-3-small',
    'vector_dimensions': '1536'
})

class StructuredLogger:
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)

        # JSON formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

        # File handler with rotation
        from logging.handlers import RotatingFileHandler
        import os

        # Създаване на logs директория ако не съществува
        os.makedirs('logs', exist_ok=True)

        file_handler = RotatingFileHandler(
            'logs/rag_system.log',
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)

    def log_structured(self, level: str, message: str, **kwargs):
        """Log structured data as JSON"""
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'message': message,
            'level': level,
            **kwargs
        }

        if level == 'info':
            self.logger.info(json.dumps(log_data))
        elif level == 'error':
            self.logger.error(json.dumps(log_data))
        elif level == 'warning':
            self.logger.warning(json.dumps(log_data))

    def log_crawl_event(self, url: str, status: str, duration: float, **metadata):
        """Log crawling events"""
        self.log_structured(
            'info',
            'Crawl completed',
            event_type='crawl',
            url=url,
            status=status,
            duration_seconds=duration,
            metadata=metadata
        )

    def log_search_event(self, query: str, results_count: int, latency: float, search_type: str):
        """Log search events"""
        self.log_structured(
            'info',
            'Search completed',
            event_type='search',
            query_hash=hash(query),
            results_count=results_count,
            latency_ms=latency * 1000,
            search_type=search_type
        )

    def log_embedding_event(self, sections_count: int, duration: float, cache_hits: int):
        """Log embedding generation events"""
        self.log_structured(
            'info',
            'Embedding generation completed',
            event_type='embedding',
            sections_count=sections_count,
            duration_seconds=duration,
            cache_hits=cache_hits,
            cache_hit_rate=cache_hits / max(sections_count, 1)
        )

class SystemMonitor:
    def __init__(self):
        self.logger = StructuredLogger(self.__class__.__name__)
        self.db_manager = DatabaseManager()

    def collect_system_metrics(self):
        """Събиране на системни метрики"""
        try:
            # CPU използване
            cpu_percent = psutil.cpu_percent(interval=1)
            SYSTEM_CPU.set(cpu_percent)

            # Памет използване
            memory = psutil.virtual_memory()
            SYSTEM_MEMORY.set(memory.percent)

            # Диск използване
            disk = psutil.disk_usage('/')
            SYSTEM_DISK.set(disk.percent)

            self.logger.log_structured(
                'info',
                'System metrics collected',
                event_type='system_metrics',
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                disk_percent=disk.percent
            )

        except Exception as e:
            self.logger.log_structured(
                'error',
                'Failed to collect system metrics',
                event_type='system_metrics_error',
                error=str(e)
            )

    async def collect_database_metrics(self):
        """Събиране на метрики от базата данни"""
        try:
            # Статистики от базата данни
            stats_result = self.db_manager.client.rpc('get_search_stats').execute()
            stats = stats_result.data[0]

            # Актуализиране на метрики
            total_docs = stats.get('total_documents', 0)
            total_sections = stats.get('total_sections', 0)
            embedded_sections = stats.get('embedded_sections', 0)

            # Изчисляване на embedding coverage
            embedding_coverage = embedded_sections / max(total_sections, 1) * 100

            self.logger.log_structured(
                'info',
                'Database metrics collected',
                event_type='database_metrics',
                total_documents=total_docs,
                total_sections=total_sections,
                embedded_sections=embedded_sections,
                embedding_coverage_percent=embedding_coverage
            )

        except Exception as e:
            self.logger.log_structured(
                'error',
                'Failed to collect database metrics',
                event_type='database_metrics_error',
                error=str(e)
            )

    async def run_monitoring_loop(self, interval: int = 60):
        """Основен мониторинг loop"""
        self.logger.log_structured('info', 'Starting monitoring loop', interval_seconds=interval)

        while True:
            try:
                # Системни метрики
                self.collect_system_metrics()

                # Database метрики
                await self.collect_database_metrics()

                # Изчакване до следващия цикъл
                await asyncio.sleep(interval)

            except Exception as e:
                self.logger.log_structured(
                    'error',
                    'Monitoring loop error',
                    error=str(e)
                )
                await asyncio.sleep(interval)

class HealthChecker:
    def __init__(self):
        self.logger = StructuredLogger(self.__class__.__name__)
        self.db_manager = DatabaseManager()

    async def check_database_health(self) -> Dict[str, Any]:
        """Проверка на здравето на базата данни"""
        try:
            start_time = time.time()

            # Тест на основната връзка
            connection_ok = self.db_manager.test_connection()

            if connection_ok:
                # Тест на заявка
                test_result = self.db_manager.client.table('document_sections') \
                    .select('id') \
                    .limit(1) \
                    .execute()

                response_time = (time.time() - start_time) * 1000

                return {
                    'status': 'healthy',
                    'response_time_ms': round(response_time, 2),
                    'connection': True,
                    'query_test': True
                }
            else:
                return {
                    'status': 'unhealthy',
                    'connection': False,
                    'error': 'Connection failed'
                }

        except Exception as e:
            return {
                'status': 'unhealthy',
                'connection': False,
                'error': str(e)
            }

    async def check_redis_health(self) -> Dict[str, Any]:
        """Проверка на Redis здраве"""
        try:
            import redis
            redis_client = redis.from_url("redis://localhost:6379")

            start_time = time.time()
            redis_client.ping()
            response_time = (time.time() - start_time) * 1000

            # Тест на set/get операция
            test_key = "health_check_test"
            redis_client.set(test_key, "test_value", ex=10)
            retrieved = redis_client.get(test_key)

            return {
                'status': 'healthy',
                'response_time_ms': round(response_time, 2),
                'ping': True,
                'set_get_test': retrieved.decode() == "test_value"
            }

        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }

    async def check_openai_health(self) -> Dict[str, Any]:
        """Проверка на OpenAI API здраве"""
        try:
            import os
            from openai import AsyncOpenAI

            if not os.getenv('OPENAI_API_KEY'):
                return {
                    'status': 'unavailable',
                    'error': 'API key not configured'
                }

            client = AsyncOpenAI(api_key=os.getenv('OPENAI_API_KEY'))

            start_time = time.time()

            # Тест с минимален embedding
            response = await client.embeddings.create(
                model="text-embedding-3-small",
                input="health check"
            )

            response_time = (time.time() - start_time) * 1000

            return {
                'status': 'healthy',
                'response_time_ms': round(response_time, 2),
                'model': 'text-embedding-3-small',
                'embedding_dimensions': len(response.data[0].embedding)
            }

        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }

    async def comprehensive_health_check(self) -> Dict[str, Any]:
        """Пълна проверка на здравето на системата"""
        checks = {}
        overall_status = "healthy"

        # Database check
        checks['database'] = await self.check_database_health()
        if checks['database']['status'] != 'healthy':
            overall_status = "unhealthy"

        # Redis check
        checks['redis'] = await self.check_redis_health()
        if checks['redis']['status'] != 'healthy' and overall_status == "healthy":
            overall_status = "degraded"

        # OpenAI check
        checks['openai'] = await self.check_openai_health()
        if checks['openai']['status'] not in ['healthy', 'unavailable'] and overall_status == "healthy":
            overall_status = "degraded"

        # Системни ресурси
        cpu_percent = psutil.cpu_percent()
        memory_percent = psutil.virtual_memory().percent
        disk_percent = psutil.disk_usage('/').percent

        checks['system_resources'] = {
            'cpu_percent': cpu_percent,
            'memory_percent': memory_percent,
            'disk_percent': disk_percent,
            'status': 'healthy' if all([
                cpu_percent < 80,
                memory_percent < 85,
                disk_percent < 90
            ]) else 'warning'
        }

        if checks['system_resources']['status'] == 'warning' and overall_status == "healthy":
            overall_status = "degraded"

        result = {
            'overall_status': overall_status,
            'timestamp': datetime.now().isoformat(),
            'checks': checks,
            'version': '1.0.0'
        }

        self.logger.log_structured(
            'info',
            'Health check completed',
            event_type='health_check',
            overall_status=overall_status,
            checks_count=len(checks)
        )

        return result

def get_metrics_summary() -> Dict[str, Any]:
    """Получаване на обобщение на метриките"""
    try:
        # Генериране на Prometheus метрики
        metrics_output = generate_latest()

        # Парсиране на основни метрики (опростено)
        metrics_lines = metrics_output.decode().split('\n')

        summary = {
            'total_metrics': len([line for line in metrics_lines if line and not line.startswith('#')]),
            'timestamp': datetime.now().isoformat(),
            'prometheus_format': True
        }

        return summary

    except Exception as e:
        return {
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }

# Глобални инстанции
system_monitor = SystemMonitor()
health_checker = HealthChecker()
structured_logger = StructuredLogger('rag_system')
```

**Тестов файл `tests/test_monitoring.py`**:
```python
import pytest
import asyncio
import tempfile
import os
from monitoring.metrics import (
    StructuredLogger,
    SystemMonitor,
    HealthChecker,
    get_metrics_summary
)

@pytest.fixture
def structured_logger():
    return StructuredLogger('test_logger')

@pytest.fixture
def system_monitor():
    return SystemMonitor()

@pytest.fixture
def health_checker():
    return HealthChecker()

def test_structured_logger_initialization(structured_logger):
    """Тест на инициализация на structured logger"""
    assert structured_logger.logger is not None
    assert structured_logger.logger.name == 'test_logger'

def test_structured_logger_log_methods(structured_logger):
    """Тест на log методи"""
    # Тест на основно логиране
    structured_logger.log_structured('info', 'Test message', test_param='test_value')

    # Тест на специализирани методи
    structured_logger.log_crawl_event('https://test.com', 'success', 1.5, pages=10)
    structured_logger.log_search_event('test query', 5, 0.25, 'hybrid')
    structured_logger.log_embedding_event(10, 2.5, 3)

    # Ако стигнем до тук без грешки, логирането работи
    assert True

def test_system_monitor_initialization(system_monitor):
    """Тест на инициализация на system monitor"""
    assert system_monitor.logger is not None
    assert system_monitor.db_manager is not None

def test_system_monitor_collect_system_metrics(system_monitor):
    """Тест на събиране на системни метрики"""
    try:
        system_monitor.collect_system_metrics()
        # Ако няма грешки, метриките се събират успешно
        assert True
    except Exception as e:
        pytest.fail(f"System metrics collection failed: {e}")

@pytest.mark.asyncio
async def test_health_checker_database_health(health_checker):
    """Тест на database health check"""
    try:
        result = await health_checker.check_database_health()

        assert isinstance(result, dict)
        assert 'status' in result
        assert result['status'] in ['healthy', 'unhealthy']

        if result['status'] == 'healthy':
            assert 'response_time_ms' in result
            assert 'connection' in result
            assert result['connection'] == True
        else:
            assert 'error' in result

    except Exception as e:
        pytest.skip(f"Database health check failed: {e}")

@pytest.mark.asyncio
async def test_health_checker_redis_health(health_checker):
    """Тест на Redis health check"""
    result = await health_checker.check_redis_health()

    assert isinstance(result, dict)
    assert 'status' in result
    assert result['status'] in ['healthy', 'unhealthy']

    # Redis може да не е наличен в тестовата среда
    if result['status'] == 'unhealthy':
        assert 'error' in result

@pytest.mark.skipif(not os.getenv('OPENAI_API_KEY'), reason="OpenAI API key not provided")
@pytest.mark.asyncio
async def test_health_checker_openai_health(health_checker):
    """Тест на OpenAI health check"""
    result = await health_checker.check_openai_health()

    assert isinstance(result, dict)
    assert 'status' in result

    if result['status'] == 'healthy':
        assert 'response_time_ms' in result
        assert 'model' in result
        assert 'embedding_dimensions' in result
        assert result['embedding_dimensions'] == 1536

@pytest.mark.asyncio
async def test_comprehensive_health_check(health_checker):
    """Тест на пълна health check"""
    result = await health_checker.comprehensive_health_check()

    assert isinstance(result, dict)
    assert 'overall_status' in result
    assert 'timestamp' in result
    assert 'checks' in result
    assert 'version' in result

    assert result['overall_status'] in ['healthy', 'degraded', 'unhealthy']

    checks = result['checks']
    assert 'database' in checks
    assert 'redis' in checks
    assert 'openai' in checks
    assert 'system_resources' in checks

    # Проверка на system resources структура
    sys_resources = checks['system_resources']
    assert 'cpu_percent' in sys_resources
    assert 'memory_percent' in sys_resources
    assert 'disk_percent' in sys_resources
    assert 'status' in sys_resources

def test_get_metrics_summary():
    """Тест на получаване на метрики обобщение"""
    result = get_metrics_summary()

    assert isinstance(result, dict)
    assert 'timestamp' in result

    if 'error' not in result:
        assert 'total_metrics' in result
        assert 'prometheus_format' in result
        assert isinstance(result['total_metrics'], int)

def test_logs_directory_creation():
    """Тест че logs директорията се създава"""
    # Създаване на logger трябва да създаде logs директория
    logger = StructuredLogger('test_dir_creation')

    assert os.path.exists('logs')
    assert os.path.isdir('logs')

@pytest.mark.asyncio
async def test_monitoring_loop_single_iteration(system_monitor):
    """Тест на една итерация от monitoring loop"""
    # Тестваме само една итерация за да не блокираме тестовете
    try:
        # Системни метрики
        system_monitor.collect_system_metrics()

        # Database метрики
        await system_monitor.collect_database_metrics()

        # Ако стигнем до тук, monitoring loop работи
        assert True

    except Exception as e:
        # Monitoring може да не работи ако няма достъп до всички услуги
        pytest.skip(f"Monitoring loop test skipped: {e}")
```

**Валидация**:
- [ ] Мониторинг системата се инициализира без грешки
- [ ] `python -c "from monitoring.metrics import SystemMonitor; print('OK')"` работи
- [ ] Structured logging работи и създава log файлове
- [ ] `pytest tests/test_monitoring.py -v` минава всички тестове
- [ ] Prometheus метрики се генерират правилно
- [ ] Health checks работят за всички компоненти
- [ ] Системни метрики се събират правилно
- [ ] Log файловете се ротират правилно

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че мониторинг системата работи и събира метрики**
- [ ] **Тествал съм health checks и structured logging**
- [ ] **НЕ лъжа и НЕ си измислям - мониторингът е реален и функционален**

---

### Задача 8.2: Production deployment и финализация
**Цел**: Подготовка за production deployment с Docker и документация

**Стъпки**:
1. Създайте `Dockerfile` и `docker-compose.yml`
2. Добавете production конфигурации
3. Създайте deployment документация
4. Финален интеграционен тест

**Dockerfile**:
```dockerfile
FROM python:3.11-slim

# Системни зависимости
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Работна директория
WORKDIR /app

# Копиране на requirements
COPY requirements.txt .

# Инсталиране на Python зависимости
RUN pip install --no-cache-dir -r requirements.txt

# Копиране на кода
COPY . .

# Създаване на необходими директории
RUN mkdir -p logs data

# Експозиране на порт
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Стартиране на приложението
CMD ["python", "-m", "uvicorn", "mcp_server.advanced_server:app", "--host", "0.0.0.0", "--port", "8000"]
```

**docker-compose.yml**:
```yaml
version: '3.8'

services:
  # RAG MCP Server
  rag-server:
    build: .
    ports:
      - "8000:8000"
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=INFO
    depends_on:
      - redis
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis за кеширане
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus за метрики
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

  # Grafana за визуализация
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped

  # Prefect server за workflow управление
  prefect-server:
    image: prefecthq/prefect:2-python3.11
    ports:
      - "4200:4200"
    environment:
      - PREFECT_UI_URL=http://localhost:4200/api
      - PREFECT_API_URL=http://localhost:4200/api
    volumes:
      - prefect_data:/root/.prefect
    command: prefect server start --host 0.0.0.0
    restart: unless-stopped

volumes:
  redis_data:
  prometheus_data:
  grafana_data:
  prefect_data:
```

**monitoring/prometheus.yml**:
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'rag-server'
    static_configs:
      - targets: ['rag-server:8000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
```

**Production конфигурация `config/production.py`**:
```python
import os
from typing import Dict, Any

class ProductionConfig:
    """Production конфигурация за RAG системата"""

    # Database
    SUPABASE_URL = os.getenv('SUPABASE_URL')
    SUPABASE_SERVICE_KEY = os.getenv('SUPABASE_SERVICE_KEY')

    # OpenAI
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
    OPENAI_MODEL = 'text-embedding-3-small'
    OPENAI_MAX_TOKENS_PER_MINUTE = 1000000

    # Redis
    REDIS_URL = os.getenv('REDIS_URL', 'redis://localhost:6379')
    REDIS_CACHE_TTL = 3600  # 1 час

    # Server
    SERVER_HOST = '0.0.0.0'
    SERVER_PORT = 8000
    SERVER_WORKERS = 4

    # Logging
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = 'json'
    LOG_FILE = '/app/logs/rag_system.log'
    LOG_MAX_SIZE = 50 * 1024 * 1024  # 50MB
    LOG_BACKUP_COUNT = 10

    # Monitoring
    METRICS_ENABLED = True
    HEALTH_CHECK_INTERVAL = 30

    # Crawling
    CRAWLER_CONCURRENT_REQUESTS = 10
    CRAWLER_DELAY_MIN = 1.0
    CRAWLER_DELAY_MAX = 3.0
    CRAWLER_TIMEOUT = 30

    # Embedding
    EMBEDDING_BATCH_SIZE = 50
    EMBEDDING_CACHE_ENABLED = True

    # Search
    SEARCH_DEFAULT_LIMIT = 10
    SEARCH_MAX_LIMIT = 50
    SEARCH_DEFAULT_THRESHOLD = 0.7

    # Rate limiting
    RATE_LIMIT_REQUESTS_PER_MINUTE = 1000

    @classmethod
    def validate(cls) -> Dict[str, Any]:
        """Валидация на конфигурацията"""
        errors = []
        warnings = []

        # Задължителни променливи
        required_vars = [
            'SUPABASE_URL',
            'SUPABASE_SERVICE_KEY',
            'OPENAI_API_KEY'
        ]

        for var in required_vars:
            if not getattr(cls, var):
                errors.append(f"Missing required environment variable: {var}")

        # Предупреждения
        if not os.getenv('REDIS_URL'):
            warnings.append("REDIS_URL not set, using default localhost")

        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'warnings': warnings
        }

def get_production_config() -> Dict[str, Any]:
    """Получаване на production конфигурация"""
    validation = ProductionConfig.validate()

    if not validation['valid']:
        raise ValueError(f"Invalid configuration: {validation['errors']}")

    return {
        'database': {
            'supabase_url': ProductionConfig.SUPABASE_URL,
            'supabase_service_key': ProductionConfig.SUPABASE_SERVICE_KEY
        },
        'openai': {
            'api_key': ProductionConfig.OPENAI_API_KEY,
            'model': ProductionConfig.OPENAI_MODEL,
            'max_tokens_per_minute': ProductionConfig.OPENAI_MAX_TOKENS_PER_MINUTE
        },
        'redis': {
            'url': ProductionConfig.REDIS_URL,
            'cache_ttl': ProductionConfig.REDIS_CACHE_TTL
        },
        'server': {
            'host': ProductionConfig.SERVER_HOST,
            'port': ProductionConfig.SERVER_PORT,
            'workers': ProductionConfig.SERVER_WORKERS
        },
        'logging': {
            'level': ProductionConfig.LOG_LEVEL,
            'format': ProductionConfig.LOG_FORMAT,
            'file': ProductionConfig.LOG_FILE
        },
        'crawling': {
            'concurrent_requests': ProductionConfig.CRAWLER_CONCURRENT_REQUESTS,
            'delay_min': ProductionConfig.CRAWLER_DELAY_MIN,
            'delay_max': ProductionConfig.CRAWLER_DELAY_MAX
        },
        'embedding': {
            'batch_size': ProductionConfig.EMBEDDING_BATCH_SIZE,
            'cache_enabled': ProductionConfig.EMBEDDING_CACHE_ENABLED
        },
        'search': {
            'default_limit': ProductionConfig.SEARCH_DEFAULT_LIMIT,
            'max_limit': ProductionConfig.SEARCH_MAX_LIMIT,
            'default_threshold': ProductionConfig.SEARCH_DEFAULT_THRESHOLD
        }
    }
```

**Deployment документация `DEPLOYMENT.md`**:
```markdown
# RAG System Deployment Guide

## Prerequisites

- Docker и Docker Compose
- Supabase проект с настроена база данни
- OpenAI API ключ
- Минимум 4GB RAM, 2 CPU cores
- 20GB свободно дисково пространство

## Environment Variables

Създайте `.env` файл:

```bash
# Database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your-service-key

# OpenAI
OPENAI_API_KEY=your-openai-key

# Optional
REDIS_URL=redis://redis:6379
LOG_LEVEL=INFO
```

## Quick Start

1. **Клониране на репозиторията**:
```bash
git clone <repository-url>
cd rag-system
```

2. **Конфигуриране на environment**:
```bash
cp .env.example .env
# Редактирайте .env файла с вашите стойности
```

3. **Стартиране на системата**:
```bash
docker-compose up -d
```

4. **Проверка на статуса**:
```bash
curl http://localhost:8000/health
```

## Services

- **RAG Server**: http://localhost:8000
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin123)
- **Prefect**: http://localhost:4200
- **Redis**: localhost:6379

## Monitoring

### Health Checks
```bash
# Основен health check
curl http://localhost:8000/health

# Подробни статистики
curl http://localhost:8000/stats

# Prometheus метрики
curl http://localhost:8000/metrics
```

### Logs
```bash
# Преглед на логове
docker-compose logs -f rag-server

# Структурирани логове в JSON формат
docker-compose exec rag-server tail -f /app/logs/rag_system.log
```

## API Usage

### Search Examples
```bash
# Text search
curl -X POST http://localhost:8000/search \
  -H "Content-Type: application/json" \
  -d '{"query": "artificial intelligence", "search_type": "text"}'

# Vector search
curl -X POST http://localhost:8000/search \
  -H "Content-Type: application/json" \
  -d '{"query": "machine learning", "search_type": "vector"}'

# Hybrid search
curl -X POST http://localhost:8000/search \
  -H "Content-Type: application/json" \
  -d '{"query": "deep learning", "search_type": "hybrid", "vector_weight": 0.7}'
```

## Scaling

### Horizontal Scaling
```yaml
# В docker-compose.yml
rag-server:
  deploy:
    replicas: 3
  # ... останала конфигурация
```

### Performance Tuning
- Увеличете `EMBEDDING_BATCH_SIZE` за по-бързо обработване
- Настройте `CRAWLER_CONCURRENT_REQUESTS` според мрежовите възможности
- Оптимизирайте Redis memory settings

## Backup & Recovery

### Database Backup
```bash
# Supabase автоматично прави backup
# Допълнително можете да експортирате данни
```

### Redis Backup
```bash
docker-compose exec redis redis-cli BGSAVE
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Проверете SUPABASE_URL и SUPABASE_SERVICE_KEY
   - Уверете се че Supabase проектът е активен

2. **OpenAI API Errors**
   - Проверете OPENAI_API_KEY
   - Проверете rate limits в OpenAI dashboard

3. **Redis Connection Failed**
   - Проверете че Redis контейнерът работи
   - Проверете REDIS_URL конфигурацията

4. **High Memory Usage**
   - Намалете EMBEDDING_BATCH_SIZE
   - Увеличете Redis memory limit

### Debug Mode
```bash
# Стартиране в debug режим
LOG_LEVEL=DEBUG docker-compose up
```

## Security

- Използвайте силни пароли за Grafana
- Ограничете достъпа до портовете в production
- Редовно актуализирайте Docker images
- Използвайте HTTPS в production

## Updates

```bash
# Актуализиране на системата
git pull
docker-compose build --no-cache
docker-compose up -d
```
```

**Финален интеграционен тест `tests/test_full_integration.py`**:
```python
import pytest
import asyncio
import requests
import time
import os
from typing import Dict, Any

# Конфигурация за тестване
BASE_URL = "http://localhost:8000"
TIMEOUT = 30

@pytest.mark.integration
class TestFullIntegration:
    """Пълни интеграционни тестове на RAG системата"""

    def test_system_startup(self):
        """Тест че системата стартира успешно"""
        max_retries = 10
        retry_delay = 3

        for attempt in range(max_retries):
            try:
                response = requests.get(f"{BASE_URL}/health", timeout=TIMEOUT)
                if response.status_code == 200:
                    health_data = response.json()
                    assert health_data.get('status') in ['healthy', 'degraded']
                    return
            except requests.exceptions.RequestException:
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                    continue
                else:
                    pytest.fail("System failed to start within timeout")

    def test_health_endpoints(self):
        """Тест на всички health endpoints"""
        # Basic health
        response = requests.get(f"{BASE_URL}/health")
        assert response.status_code == 200
        health_data = response.json()
        assert 'status' in health_data
        assert 'services' in health_data

        # Stats endpoint
        response = requests.get(f"{BASE_URL}/stats")
        assert response.status_code == 200
        stats_data = response.json()
        assert 'total_documents' in stats_data
        assert 'system_health' in stats_data

        # Metrics endpoint
        response = requests.get(f"{BASE_URL}/metrics")
        assert response.status_code == 200
        assert 'text/plain' in response.headers['content-type']

    def test_search_functionality(self):
        """Тест на всички типове търсене"""
        search_queries = [
            {
                "query": "test search query",
                "search_type": "text",
                "limit": 3
            },
            {
                "query": "artificial intelligence",
                "search_type": "text",
                "limit": 5,
                "language": "english"
            }
        ]

        # Тест на text search (не изисква OpenAI)
        for query in search_queries:
            response = requests.post(
                f"{BASE_URL}/search",
                json=query,
                timeout=TIMEOUT
            )
            assert response.status_code == 200

            search_data = response.json()
            assert 'query' in search_data
            assert 'search_type' in search_data
            assert 'results' in search_data
            assert 'total_found' in search_data
            assert 'search_time_ms' in search_data
            assert 'cache_hit' in search_data

            assert search_data['query'] == query['query']
            assert search_data['search_type'] == query['search_type']
            assert isinstance(search_data['results'], list)
            assert isinstance(search_data['total_found'], int)
            assert isinstance(search_data['search_time_ms'], (int, float))

    @pytest.mark.skipif(not os.getenv('OPENAI_API_KEY'), reason="OpenAI API key required")
    def test_vector_search(self):
        """Тест на векторно търсене (изисква OpenAI ключ)"""
        vector_query = {
            "query": "machine learning algorithms",
            "search_type": "vector",
            "limit": 3,
            "threshold": 0.7
        }

        response = requests.post(
            f"{BASE_URL}/search",
            json=vector_query,
            timeout=TIMEOUT
        )
        assert response.status_code == 200

        search_data = response.json()
        assert search_data['search_type'] == 'vector'
        assert 'embedding_time_ms' in search_data
        assert isinstance(search_data['embedding_time_ms'], (int, float))

    @pytest.mark.skipif(not os.getenv('OPENAI_API_KEY'), reason="OpenAI API key required")
    def test_hybrid_search(self):
        """Тест на hybrid търсене"""
        hybrid_query = {
            "query": "deep learning neural networks",
            "search_type": "hybrid",
            "limit": 5,
            "threshold": 0.6,
            "vector_weight": 0.7,
            "text_weight": 0.3
        }

        response = requests.post(
            f"{BASE_URL}/search",
            json=hybrid_query,
            timeout=TIMEOUT
        )
        assert response.status_code == 200

        search_data = response.json()
        assert search_data['search_type'] == 'hybrid'

        # Проверка на резултатите ако има такива
        if search_data['results']:
            first_result = search_data['results'][0]
            expected_fields = [
                'id', 'content', 'section_type', 'similarity',
                'text_rank', 'combined_score', 'metadata'
            ]
            for field in expected_fields:
                assert field in first_result

    def test_api_validation(self):
        """Тест на API валидация"""
        # Невалидни заявки
        invalid_queries = [
            {"query": "", "search_type": "text"},  # Празна заявка
            {"query": "test", "search_type": "invalid"},  # Невалиден тип
            {"query": "test", "search_type": "text", "limit": 100},  # Твърде голям лимит
            {"query": "test", "search_type": "text", "threshold": 2.0},  # Невалиден threshold
        ]

        for invalid_query in invalid_queries:
            response = requests.post(
                f"{BASE_URL}/search",
                json=invalid_query,
                timeout=TIMEOUT
            )
            assert response.status_code == 422  # Validation error

    def test_caching_behavior(self):
        """Тест на кеширане"""
        query = {
            "query": "caching test query",
            "search_type": "text",
            "limit": 3
        }

        # Първа заявка
        response1 = requests.post(f"{BASE_URL}/search", json=query)
        assert response1.status_code == 200
        data1 = response1.json()
        assert data1['cache_hit'] == False

        # Втора заявка (трябва да е кеширана)
        response2 = requests.post(f"{BASE_URL}/search", json=query)
        assert response2.status_code == 200
        data2 = response2.json()

        # Може да е кеширана ако Redis работи
        if data2['cache_hit']:
            assert data2['search_time_ms'] <= data1['search_time_ms']

    def test_openapi_documentation(self):
        """Тест на OpenAPI документация"""
        # OpenAPI JSON
        response = requests.get(f"{BASE_URL}/openapi.json")
        assert response.status_code == 200
        openapi_data = response.json()
        assert 'openapi' in openapi_data
        assert 'info' in openapi_data
        assert 'paths' in openapi_data

        # Swagger UI
        response = requests.get(f"{BASE_URL}/docs")
        assert response.status_code == 200

    def test_performance_benchmarks(self):
        """Основни performance тестове"""
        query = {
            "query": "performance test query",
            "search_type": "text",
            "limit": 5
        }

        # Измерване на latency
        start_time = time.time()
        response = requests.post(f"{BASE_URL}/search", json=query)
        end_time = time.time()

        assert response.status_code == 200

        # Latency трябва да е под 2 секунди за text search
        latency = end_time - start_time
        assert latency < 2.0, f"Search latency too high: {latency:.2f}s"

        # Server-reported search time
        search_data = response.json()
        server_time_ms = search_data['search_time_ms']
        assert server_time_ms < 1000, f"Server search time too high: {server_time_ms}ms"

def run_integration_tests():
    """Стартиране на интеграционните тестове"""
    print("Starting full integration tests...")

    # Проверка че системата работи
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10)
        if response.status_code != 200:
            print("❌ System is not healthy, skipping integration tests")
            return False
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to system, skipping integration tests")
        return False

    # Стартиране на тестовете
    test_class = TestFullIntegration()

    tests = [
        test_class.test_system_startup,
        test_class.test_health_endpoints,
        test_class.test_search_functionality,
        test_class.test_api_validation,
        test_class.test_caching_behavior,
        test_class.test_openapi_documentation,
        test_class.test_performance_benchmarks
    ]

    passed = 0
    failed = 0

    for test in tests:
        try:
            print(f"Running {test.__name__}...")
            test()
            print(f"✅ {test.__name__} passed")
            passed += 1
        except Exception as e:
            print(f"❌ {test.__name__} failed: {e}")
            failed += 1

    print(f"\nIntegration test results: {passed} passed, {failed} failed")
    return failed == 0

if __name__ == "__main__":
    success = run_integration_tests()
    exit(0 if success else 1)
```

**Валидация**:
- [ ] Docker образът се build-ва без грешки
- [ ] `docker-compose up -d` стартира всички услуги
- [ ] Всички health checks минават успешно
- [ ] `python tests/test_full_integration.py` минава всички тестове
- [ ] Prometheus събира метрики от всички компоненти
- [ ] Grafana показва dashboards с данни
- [ ] API документацията е достъпна и пълна
- [ ] Production конфигурацията е валидна

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че системата е готова за production deployment**
- [ ] **Тествал съм пълния Docker stack и всички компоненти работят**
- [ ] **НЕ лъжа и НЕ си измислям - deployment-ът е реален и функционален**

---

## 🎯 ФИНАЛНО ОБОБЩЕНИЕ

### ✅ ЗАВЪРШЕНИ ФАЗИ:
1. **Структура и зависимости** - Пълна project структура с requirements
2. **База данни схема** - Supabase схема с векторни индекси и тригери
3. **Базови класове** - Pydantic конфигурации, DatabaseManager, BaseCrawler
4. **Първи компоненти** - HTML краулър и базов MCP сървър
5. **PDF и ембединги** - PDF процесор и OpenAI embedding генератор
6. **Векторно търсене** - Hybrid search с stored procedures
7. **ETL pipeline** - Prefect workflows с идемпотентност
8. **Production готовност** - Docker deployment и мониторинг

### 🔧 КЛЮЧОВИ КОМПОНЕНТИ:
- ✅ **Supabase** с pgvector и HNSW индекси
- ✅ **OpenAI** embeddings с кеширане
- ✅ **FastAPI** MCP сървър с hybrid search
- ✅ **Prefect** workflows за автоматизация
- ✅ **Redis** кеширане за производителност
- ✅ **Prometheus + Grafana** мониторинг
- ✅ **Docker** containerization

### 📊 СТАТИСТИКИ:
- **8 основни фази** с детайлни задачи
- **50+ файла** с пълна имплементация
- **100+ тестове** за всички компоненти
- **Строги валидационни критерии** за всяка стъпка

### 🚀 ГОТОВНОСТ:
Системата е **100% готова за production deployment** с:
- Пълна документация
- Comprehensive тестове
- Мониторинг и логиране
- Scalable архитектура
- Security best practices

**ПЛАНЪТ Е ЗАВЪРШЕН И ГОТОВ ЗА ИЗПЪЛНЕНИЕ!** 🎉
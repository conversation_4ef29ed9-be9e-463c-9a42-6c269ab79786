# RAG Система - Пошагов план за имплементация

## 🎯 Правила за изпълнение
- ✅ **ЗАДЪЛЖИТЕЛНО**: След всяка задача попълнете валидационното квадратче
- ⚠️ **ЗАБРАНЕНО**: Преминаване към следваща задача без 100% валидация
- 🔍 **ИЗИСКВАНЕ**: Всяка задача трябва да бъде тествана и потвърдена че работи
- 📝 **ДОКУМЕНТАЦИЯ**: Запишете точно какво сте тествали и как

---

## 📋 ФАЗА 1: Подготовка и основна структура (Седмица 1)

### Задача 1.1: Създаване на проектна структура
**Цел**: Създаване на всички директории и основни файлове

**Стъпки**:
1. Създайте главна директория `rag-system/`
2. Създайте всички поддиректории според плана
3. Създайте основните файлове (`README.md`, `requirements.txt`, `.gitignore`, etc.)
4. Инициализирайте Git репозитори

**Файлове за създаване**:
```
rag-system/
├── crawlers/
├── processors/
├── database/
├── embeddings/
├── mcp_server/
├── security/
├── monitoring/
├── config/
├── tests/
├── docs/
├── workflows/
├── main.py
├── requirements.txt
├── .env.example
├── .gitignore
└── README.md
```

**Валидация**:
- [ ] Всички директории са създадени
- [ ] Всички основни файлове съществуват
- [ ] Git репозитори е инициализиран
- [ ] `find rag-system -type d | wc -l` връща поне 10 директории
- [ ] `find rag-system -type f | wc -l` връща поне 15 файла

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че структурата е създадена правилно**
- [ ] **Тествал съм с командите по-горе и всичко работи**
- [ ] **НЕ лъжа и НЕ си измислям - всичко е реално направено**

---

### Задача 1.2: Настройка на зависимости
**Цел**: Инсталиране и тестване на всички Python библиотеки

**Стъпки**:
1. Създайте виртуална среда
2. Попълнете `requirements.txt` с актуализираните версии
3. Инсталирайте всички зависимости
4. Тествайте импортирането на ключови библиотеки

**requirements.txt съдържание**:
```
# Основни
scrapy==2.11.0
supabase==2.3.0
fastapi==0.104.1
uvicorn==0.24.0
python-dotenv==1.0.0

# Обработка на данни
beautifulsoup4==4.12.2
pymupdf==1.23.8
langdetect==1.0.9
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Асинхронни операции
aiohttp==3.9.1
playwright==1.40.0
scrapy-asyncer==0.1.0

# AI и ембединги
openai>=1.6.0
pgvector==0.2.4

# Мониторинг и оркестрация
prometheus-client==0.16.0
prefect==2.10.8
tenacity==8.2.2

# Сигурност
cryptography==40.0.2

# Тестване
pytest==7.3.1
pytest-asyncio==0.21.0
```

**Валидация**:
- [ ] Виртуалната среда е създадена и активирана
- [ ] Всички пакети се инсталират без грешки
- [ ] `python -c "import openai; print(openai.__version__)"` работи
- [ ] `python -c "import pydantic; print(pydantic.__version__)"` показва >=2.5.0
- [ ] `python -c "import supabase; print('OK')"` работи
- [ ] `python -c "import fastapi; print('OK')"` работи
- [ ] `playwright install` се изпълнява успешно

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че всички зависимости работят**
- [ ] **Тествал съм всички import команди и няма грешки**
- [ ] **НЕ лъжа и НЕ си измислям - всичко е реално тествано**

---

### Задача 1.3: Настройка на Supabase проект
**Цел**: Създаване и конфигуриране на Supabase база данни

**Стъпки**:
1. Създайте Supabase проект на https://supabase.com/
2. Активирайте pgvector разширение
3. Създайте `.env` файл с credentials
4. Тествайте връзката

**SQL команди за изпълнение**:
```sql
-- Активиране на разширения
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS pg_cron;
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Тестова таблица
CREATE TABLE test_connection (
  id SERIAL PRIMARY KEY,
  message TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

INSERT INTO test_connection (message) VALUES ('Connection test successful');
```

**.env файл**:
```
SUPABASE_URL=your_supabase_url_here
SUPABASE_SERVICE_KEY=your_service_key_here
SUPABASE_ANON_KEY=your_anon_key_here
OPENAI_API_KEY=your_openai_key_here
ENCRYPTION_KEY=your_encryption_key_here
```

**Валидация**:
- [ ] Supabase проект е създаден
- [ ] pgvector разширение е активирано
- [ ] `.env` файл съществува с всички ключове
- [ ] `python -c "from supabase import create_client; import os; from dotenv import load_dotenv; load_dotenv(); client = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_SERVICE_KEY')); print(client.table('test_connection').select('*').execute())"` работи
- [ ] Заявката връща данни от test_connection таблицата

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че Supabase връзката работи**
- [ ] **Тествал съм заявката и получавам данни**
- [ ] **НЕ лъжа и НЕ си измислям - връзката е реална и функционална**

---

## 📋 ФАЗА 2: Базова схема на базата данни (Седмица 1-2)

### Задача 2.1: Създаване на основни таблици
**Цел**: Създаване на всички таблици за Medallion архитектурата

**Стъпки**:
1. Създайте `database/schema.sql`
2. Изпълнете SQL скрипта в Supabase
3. Проверете че всички таблици са създадени
4. Тествайте основни CRUD операции

**database/schema.sql**:
```sql
-- Бронзов слой (сурови данни)
CREATE TABLE raw_pages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  url TEXT NOT NULL,
  html_content TEXT,
  fetch_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB,
  is_processed BOOLEAN DEFAULT FALSE
);

CREATE TABLE raw_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  url TEXT NOT NULL,
  file_path TEXT,
  file_type TEXT,
  storage_bucket TEXT DEFAULT 'documents',
  fetch_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB,
  is_processed BOOLEAN DEFAULT FALSE
);

-- Сребърен слой (обработени данни)
CREATE TABLE clean_pages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  raw_page_id UUID REFERENCES raw_pages(id),
  content TEXT NOT NULL,
  language TEXT,
  content_hash TEXT UNIQUE,
  processed_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);

-- Златен слой (крайни данни)
CREATE TABLE documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT,
  source TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);

CREATE TABLE document_sections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID REFERENCES documents(id),
  content TEXT NOT NULL,
  content_tsv TSVECTOR,
  content_hash TEXT UNIQUE,
  section_index INTEGER,
  section_type VARCHAR(20),
  embedding VECTOR(1536),
  embedding_model TEXT DEFAULT 'text-embedding-3-small',
  language TEXT DEFAULT 'en',
  is_embedded BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Валидация**:
- [ ] Всички таблици са създадени без грешки
- [ ] `SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'` показва всички таблици
- [ ] `INSERT INTO raw_pages (url) VALUES ('https://test.com')` работи
- [ ] `SELECT * FROM raw_pages` връща тестовия запис
- [ ] `DELETE FROM raw_pages WHERE url = 'https://test.com'` работи

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че всички таблици са създадени правилно**
- [ ] **Тествал съм CRUD операции и всичко работи**
- [ ] **НЕ лъжа и НЕ си измислям - таблиците са реални и функционални**

---

### Задача 2.2: Създаване на индекси и функции
**Цел**: Оптимизиране на базата данни с индекси и stored procedures

**Стъпки**:
1. Създайте `database/indexes.sql`
2. Създайте `database/functions.sql`
3. Изпълнете скриптовете
4. Тествайте производителността

**database/indexes.sql**:
```sql
-- Основни индекси
CREATE INDEX idx_raw_pages_url ON raw_pages(url);
CREATE INDEX idx_raw_pages_processed ON raw_pages(is_processed);
CREATE INDEX idx_raw_documents_url ON raw_documents(url);
CREATE INDEX idx_clean_pages_content_hash ON clean_pages(content_hash);
CREATE INDEX idx_document_sections_content_hash ON document_sections(content_hash);
CREATE INDEX idx_document_sections_embedded ON document_sections(is_embedded);

-- HNSW индекс за векторно търсене
CREATE INDEX idx_document_sections_embedding ON document_sections
USING hnsw (embedding vector_cosine_ops)
WITH (m = 32, ef_construction = 128);

-- GIN индекс за текстово търсене
CREATE INDEX idx_document_sections_tsv ON document_sections USING GIN(content_tsv);
```

**Валидация**:
- [ ] Всички индекси са създадени без грешки
- [ ] `SELECT indexname FROM pg_indexes WHERE tablename = 'document_sections'` показва всички индекси
- [ ] `EXPLAIN SELECT * FROM raw_pages WHERE url = 'test'` използва индекс
- [ ] Няма грешки при създаване на HNSW индекс

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че всички индекси работят правилно**
- [ ] **Тествал съм EXPLAIN заявки и индексите се използват**
- [ ] **НЕ лъжа и НЕ си измислям - индексите са реални и оптимизират заявките**

---

## 📋 ФАЗА 3: Базови конфигурационни класове (Седмица 2)

### Задача 3.1: Pydantic конфигурационни модели
**Цел**: Създаване на type-safe конфигурация с Pydantic V2

**Стъпки**:
1. Създайте `config/models.py`
2. Имплементирайте всички конфигурационни класове
3. Тествайте валидацията
4. Тествайте зареждането от .env

**config/models.py**:
```python
from pydantic import BaseModel, Field, field_validator, ConfigDict
from pydantic_settings import BaseSettings
from typing import List, Optional, Literal

class CrawlerConfig(BaseSettings):
    model_config = ConfigDict(env_file='.env', env_prefix='CRAWLER_')

    start_urls: List[str] = Field(default=[])
    allowed_domains: List[str] = Field(default=[])
    download_delay: float = 2.0
    concurrent_requests: int = 8
    max_pages: Optional[int] = None

    @field_validator('concurrent_requests')
    @classmethod
    def validate_concurrent_requests(cls, v):
        if v < 1 or v > 32:
            raise ValueError('concurrent_requests must be between 1 and 32')
        return v

class DatabaseConfig(BaseSettings):
    model_config = ConfigDict(env_file='.env', env_prefix='DB_')

    supabase_url: str
    supabase_service_key: str
    supabase_anon_key: str

class EmbeddingConfig(BaseSettings):
    model_config = ConfigDict(env_file='.env', env_prefix='EMBEDDING_')

    model: str = "text-embedding-3-small"
    batch_size: int = Field(default=20, ge=1, le=100)
    dimensions: int = 1536
    api_key: str
    max_tokens_per_minute: int = Field(default=1000000, ge=10000)

class DocumentSection(BaseModel):
    content: str
    section_index: int
    section_type: Literal["heading", "paragraph", "list", "table", "image", "code"]
    embedding: Optional[List[float]] = None
    content_hash: Optional[str] = None

    @field_validator("content")
    @classmethod
    def validate_content_length(cls, v):
        if len(v) < 10:
            raise ValueError("Section content too short")
        return v
```

**Тестов файл `tests/test_config.py`**:
```python
import pytest
from config.models import CrawlerConfig, DatabaseConfig, EmbeddingConfig, DocumentSection
from pydantic import ValidationError

def test_crawler_config_validation():
    # Валиден конфиг
    config = CrawlerConfig(
        start_urls=["https://example.com"],
        allowed_domains=["example.com"],
        concurrent_requests=8
    )
    assert config.concurrent_requests == 8

    # Невалиден конфиг
    with pytest.raises(ValidationError):
        CrawlerConfig(concurrent_requests=50)  # Над лимита

def test_document_section_validation():
    # Валидна секция
    section = DocumentSection(
        content="This is a test content that is long enough",
        section_index=1,
        section_type="paragraph"
    )
    assert section.section_type == "paragraph"

    # Невалидна секция
    with pytest.raises(ValidationError):
        DocumentSection(
            content="Short",  # Твърде кратко
            section_index=1,
            section_type="paragraph"
        )
```

**Валидация**:
- [ ] Всички класове се импортират без грешки
- [ ] `python -c "from config.models import CrawlerConfig; print('OK')"` работи
- [ ] `pytest tests/test_config.py -v` минава всички тестове
- [ ] Валидацията хвърля грешки при невалидни данни
- [ ] Конфигурацията се зарежда от .env файл

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че Pydantic моделите работят правилно**
- [ ] **Тествал съм валидацията и тя хвърля грешки при нужда**
- [ ] **НЕ лъжа и НЕ си измислям - всички тестове минават успешно**

---

### Задача 3.2: Database Manager клас
**Цел**: Създаване на централизиран клас за работа с базата данни

**Стъпки**:
1. Създайте `database/db_manager.py`
2. Имплементирайте основни CRUD операции
3. Добавете connection pooling
4. Тествайте всички методи

**database/db_manager.py**:
```python
import os
import asyncio
from typing import List, Dict, Any, Optional
from supabase import create_client, Client
from dotenv import load_dotenv
import logging

class DatabaseManager:
    def __init__(self):
        load_dotenv()
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_SERVICE_KEY")

        if not self.supabase_url or not self.supabase_key:
            raise ValueError("Missing Supabase credentials in .env file")

        self.client: Client = create_client(self.supabase_url, self.supabase_key)
        self.logger = logging.getLogger(self.__class__.__name__)

    def test_connection(self) -> bool:
        """Тестване на връзката с базата данни"""
        try:
            result = self.client.table('test_connection').select('*').limit(1).execute()
            return True
        except Exception as e:
            self.logger.error(f"Database connection failed: {e}")
            return False

    def insert_raw_page(self, url: str, html_content: str, metadata: Dict = None) -> str:
        """Вмъкване на сурова страница"""
        try:
            data = {
                'url': url,
                'html_content': html_content,
                'metadata': metadata or {},
                'is_processed': False
            }

            result = self.client.table('raw_pages').insert(data).execute()
            return result.data[0]['id']
        except Exception as e:
            self.logger.error(f"Failed to insert raw page: {e}")
            raise

    def get_unprocessed_pages(self, limit: int = 100) -> List[Dict[str, Any]]:
        """Получаване на необработени страници"""
        try:
            result = self.client.table('raw_pages') \
                .select('*') \
                .eq('is_processed', False) \
                .limit(limit) \
                .execute()

            return result.data
        except Exception as e:
            self.logger.error(f"Failed to get unprocessed pages: {e}")
            return []

    def mark_page_processed(self, page_id: str) -> bool:
        """Маркиране на страница като обработена"""
        try:
            self.client.table('raw_pages') \
                .update({'is_processed': True}) \
                .eq('id', page_id) \
                .execute()
            return True
        except Exception as e:
            self.logger.error(f"Failed to mark page as processed: {e}")
            return False

    def insert_document_section(self, section_data: Dict[str, Any]) -> str:
        """Вмъкване на секция от документ"""
        try:
            result = self.client.table('document_sections').insert(section_data).execute()
            return result.data[0]['id']
        except Exception as e:
            self.logger.error(f"Failed to insert document section: {e}")
            raise

    def search_similar_sections(self, embedding: List[float], limit: int = 5, threshold: float = 0.7) -> List[Dict]:
        """Търсене на подобни секции по embedding"""
        try:
            # Тук ще използваме stored procedure когато я създадем
            # За сега връщаме празен списък
            return []
        except Exception as e:
            self.logger.error(f"Failed to search similar sections: {e}")
            return []
```

**Тестов файл `tests/test_db_manager.py`**:
```python
import pytest
from database.db_manager import DatabaseManager
import uuid

@pytest.fixture
def db_manager():
    return DatabaseManager()

def test_database_connection(db_manager):
    """Тест на връзката с базата данни"""
    assert db_manager.test_connection() == True

def test_insert_and_get_raw_page(db_manager):
    """Тест на вмъкване и извличане на сурова страница"""
    test_url = f"https://test-{uuid.uuid4()}.com"
    test_content = "<html><body>Test content</body></html>"

    # Вмъкване
    page_id = db_manager.insert_raw_page(test_url, test_content, {"test": True})
    assert page_id is not None

    # Извличане на необработени страници
    unprocessed = db_manager.get_unprocessed_pages()
    test_page = next((p for p in unprocessed if p['id'] == page_id), None)
    assert test_page is not None
    assert test_page['url'] == test_url
    assert test_page['is_processed'] == False

    # Маркиране като обработена
    success = db_manager.mark_page_processed(page_id)
    assert success == True

    # Проверка че е маркирана
    unprocessed_after = db_manager.get_unprocessed_pages()
    test_page_after = next((p for p in unprocessed_after if p['id'] == page_id), None)
    assert test_page_after is None  # Не трябва да се намира в необработените

def test_insert_document_section(db_manager):
    """Тест на вмъкване на секция от документ"""
    section_data = {
        'content': 'This is a test section with enough content to pass validation',
        'section_index': 1,
        'section_type': 'paragraph',
        'content_hash': 'test_hash_' + str(uuid.uuid4()),
        'language': 'en'
    }

    section_id = db_manager.insert_document_section(section_data)
    assert section_id is not None
```

**Валидация**:
- [ ] DatabaseManager клас се създава без грешки
- [ ] `python -c "from database.db_manager import DatabaseManager; dm = DatabaseManager(); print(dm.test_connection())"` връща True
- [ ] `pytest tests/test_db_manager.py -v` минава всички тестове
- [ ] Всички CRUD операции работят правилно
- [ ] Грешките се обработват правилно

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че DatabaseManager работи правилно**
- [ ] **Тествал съм всички методи и те работят с реалната база данни**
- [ ] **НЕ лъжа и НЕ си измислям - всички тестове минават и операциите са реални**

---

### Задача 3.3: Базов краулър клас
**Цел**: Създаване на абстрактен базов клас за всички краулъри

**Стъпки**:
1. Създайте `crawlers/base_crawler.py`
2. Имплементирайте базовата функционалност
3. Добавете метрики и логиране
4. Тествайте базовите методи

**crawlers/base_crawler.py**:
```python
from abc import ABC, abstractmethod
import logging
import time
import random
import hashlib
from typing import Dict, Any, List, Optional
from prometheus_client import Counter, Gauge, Histogram
from database.db_manager import DatabaseManager

# Метрики
PAGES_CRAWLED = Counter('pages_crawled_total', 'Total pages crawled', ['crawler_type', 'status'])
CRAWL_TIME = Histogram('crawl_time_seconds', 'Time taken to crawl', ['crawler_type'])
ACTIVE_CRAWLERS = Gauge('active_crawlers', 'Number of active crawlers', ['crawler_type'])

class BaseCrawler(ABC):
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.db_manager = DatabaseManager()
        self.crawler_type = self.__class__.__name__

        # User agents за ротация
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        ]

    def get_random_user_agent(self) -> str:
        """Връща случаен User-Agent"""
        return random.choice(self.user_agents)

    def add_delay(self):
        """Добавя случайно забавяне между заявки"""
        min_delay = self.config.get('min_delay', 1)
        max_delay = self.config.get('max_delay', 5)
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)

    def generate_content_hash(self, content: str) -> str:
        """Генерира хеш на съдържанието"""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()

    def is_duplicate_url(self, url: str) -> bool:
        """Проверява дали URL-ът вече е краулнат"""
        try:
            result = self.db_manager.client.table('raw_pages') \
                .select('id') \
                .eq('url', url) \
                .limit(1) \
                .execute()

            return len(result.data) > 0
        except Exception as e:
            self.logger.error(f"Error checking duplicate URL: {e}")
            return False

    @abstractmethod
    async def crawl_url(self, url: str) -> Dict[str, Any]:
        """Краулинг на конкретен URL - трябва да се имплементира в наследниците"""
        pass

    @abstractmethod
    def save_crawl_result(self, url: str, result: Dict[str, Any]) -> bool:
        """Запазване на резултата от краулинг - трябва да се имплементира в наследниците"""
        pass

    async def run_crawl(self, urls: List[str]) -> Dict[str, Any]:
        """Основен метод за краулинг на списък от URL-и"""
        ACTIVE_CRAWLERS.labels(crawler_type=self.crawler_type).inc()

        results = {
            'total_urls': len(urls),
            'successful': 0,
            'failed': 0,
            'duplicates': 0,
            'errors': []
        }

        start_time = time.time()

        try:
            for url in urls:
                try:
                    # Проверка за дубликати
                    if self.is_duplicate_url(url):
                        self.logger.info(f"Skipping duplicate URL: {url}")
                        results['duplicates'] += 1
                        PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status='duplicate').inc()
                        continue

                    # Краулинг
                    crawl_result = await self.crawl_url(url)

                    if crawl_result.get('success', False):
                        # Запазване
                        if self.save_crawl_result(url, crawl_result):
                            results['successful'] += 1
                            PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status='success').inc()
                        else:
                            results['failed'] += 1
                            PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status='save_failed').inc()
                    else:
                        results['failed'] += 1
                        results['errors'].append(f"{url}: {crawl_result.get('error', 'Unknown error')}")
                        PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status='crawl_failed').inc()

                    # Забавяне между заявки
                    self.add_delay()

                except Exception as e:
                    self.logger.error(f"Error crawling {url}: {e}")
                    results['failed'] += 1
                    results['errors'].append(f"{url}: {str(e)}")
                    PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status='error').inc()

        finally:
            end_time = time.time()
            CRAWL_TIME.labels(crawler_type=self.crawler_type).observe(end_time - start_time)
            ACTIVE_CRAWLERS.labels(crawler_type=self.crawler_type).dec()

        self.logger.info(f"Crawl completed: {results}")
        return results
```

**Тестов файл `tests/test_base_crawler.py`**:
```python
import pytest
import asyncio
from crawlers.base_crawler import BaseCrawler

class TestCrawler(BaseCrawler):
    """Тестова имплементация на BaseCrawler"""

    async def crawl_url(self, url: str):
        # Симулация на краулинг
        await asyncio.sleep(0.1)
        return {
            'success': True,
            'content': f'<html><body>Content for {url}</body></html>',
            'title': f'Title for {url}'
        }

    def save_crawl_result(self, url: str, result: Dict[str, Any]) -> bool:
        try:
            page_id = self.db_manager.insert_raw_page(
                url=url,
                html_content=result['content'],
                metadata={'title': result['title']}
            )
            return page_id is not None
        except Exception as e:
            self.logger.error(f"Failed to save crawl result: {e}")
            return False

@pytest.fixture
def test_crawler():
    config = {
        'min_delay': 0.1,
        'max_delay': 0.2
    }
    return TestCrawler(config)

def test_crawler_initialization(test_crawler):
    """Тест на инициализация на краулър"""
    assert test_crawler.crawler_type == 'TestCrawler'
    assert len(test_crawler.user_agents) > 0
    assert test_crawler.db_manager is not None

def test_get_random_user_agent(test_crawler):
    """Тест на генериране на User-Agent"""
    ua1 = test_crawler.get_random_user_agent()
    ua2 = test_crawler.get_random_user_agent()

    assert ua1 in test_crawler.user_agents
    assert ua2 in test_crawler.user_agents

def test_generate_content_hash(test_crawler):
    """Тест на генериране на хеш"""
    content = "Test content"
    hash1 = test_crawler.generate_content_hash(content)
    hash2 = test_crawler.generate_content_hash(content)
    hash3 = test_crawler.generate_content_hash("Different content")

    assert hash1 == hash2  # Същото съдържание -> същ хеш
    assert hash1 != hash3  # Различно съдържание -> различен хеш
    assert len(hash1) == 64  # SHA256 хеш

@pytest.mark.asyncio
async def test_run_crawl(test_crawler):
    """Тест на основния краулинг процес"""
    test_urls = [
        f'https://test-{i}.com'
        for i in range(3)
    ]

    results = await test_crawler.run_crawl(test_urls)

    assert results['total_urls'] == 3
    assert results['successful'] >= 0
    assert results['failed'] >= 0
    assert results['duplicates'] >= 0
    assert results['successful'] + results['failed'] + results['duplicates'] == 3
```

**Валидация**:
- [ ] BaseCrawler клас се създава без грешки
- [ ] `python -c "from crawlers.base_crawler import BaseCrawler; print('OK')"` работи
- [ ] `pytest tests/test_base_crawler.py -v` минава всички тестове
- [ ] Метриките се записват правилно
- [ ] Дубликатите се откриват правилно

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че BaseCrawler работи правилно**
- [ ] **Тествал съм всички методи включително async операции**
- [ ] **НЕ лъжа и НЕ си измислям - всички тестове минават и функционалността е реална**

---

## 📋 ФАЗА 4: Първи работещ краулър (Седмица 2-3)

### Задача 4.1: HTML краулър с aiohttp
**Цел**: Създаване на прост но функционален HTML краулър

**Стъпки**:
1. Създайте `crawlers/html_crawler.py`
2. Имплементирайте асинхронен краулинг с aiohttp
3. Добавете обработка на HTML с BeautifulSoup
4. Тествайте с реални сайтове

**crawlers/html_crawler.py**:
```python
import asyncio
import aiohttp
from bs4 import BeautifulSoup
from typing import Dict, Any, List
from urllib.parse import urljoin, urlparse
import logging
from crawlers.base_crawler import BaseCrawler

class HTMLCrawler(BaseCrawler):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.session = None
        self.timeout = aiohttp.ClientTimeout(total=30)

    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=self.timeout,
            headers={'User-Agent': self.get_random_user_agent()}
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()

    async def crawl_url(self, url: str) -> Dict[str, Any]:
        """Краулинг на HTML страница"""
        try:
            if not self.session:
                raise RuntimeError("HTMLCrawler must be used as async context manager")

            self.logger.info(f"Crawling HTML page: {url}")

            async with self.session.get(url) as response:
                if response.status == 200:
                    html_content = await response.text()

                    # Парсиране на HTML
                    soup = BeautifulSoup(html_content, 'html.parser')

                    # Извличане на основна информация
                    title = soup.title.string.strip() if soup.title else ""

                    # Премахване на script и style тагове
                    for script in soup(["script", "style"]):
                        script.decompose()

                    # Извличане на чист текст
                    text_content = soup.get_text(separator=' ', strip=True)

                    # Извличане на линкове
                    links = []
                    for link in soup.find_all('a', href=True):
                        absolute_url = urljoin(url, link['href'])
                        links.append({
                            'url': absolute_url,
                            'text': link.get_text(strip=True)
                        })

                    # Извличане на метаданни
                    meta_description = ""
                    meta_tag = soup.find('meta', attrs={'name': 'description'})
                    if meta_tag:
                        meta_description = meta_tag.get('content', '')

                    return {
                        'success': True,
                        'url': url,
                        'title': title,
                        'html_content': html_content,
                        'text_content': text_content,
                        'meta_description': meta_description,
                        'links': links,
                        'status_code': response.status,
                        'content_type': response.headers.get('Content-Type', ''),
                        'content_length': len(html_content)
                    }
                else:
                    return {
                        'success': False,
                        'url': url,
                        'error': f'HTTP {response.status}',
                        'status_code': response.status
                    }

        except asyncio.TimeoutError:
            return {
                'success': False,
                'url': url,
                'error': 'Request timeout'
            }
        except Exception as e:
            return {
                'success': False,
                'url': url,
                'error': str(e)
            }

    def save_crawl_result(self, url: str, result: Dict[str, Any]) -> bool:
        """Запазване на резултата от HTML краулинг"""
        try:
            metadata = {
                'title': result.get('title', ''),
                'meta_description': result.get('meta_description', ''),
                'links_count': len(result.get('links', [])),
                'status_code': result.get('status_code'),
                'content_type': result.get('content_type', ''),
                'content_length': result.get('content_length', 0),
                'text_length': len(result.get('text_content', ''))
            }

            page_id = self.db_manager.insert_raw_page(
                url=url,
                html_content=result['html_content'],
                metadata=metadata
            )

            self.logger.info(f"Saved HTML page {url} with ID {page_id}")
            return page_id is not None

        except Exception as e:
            self.logger.error(f"Failed to save HTML crawl result for {url}: {e}")
            return False

    async def crawl_multiple_urls(self, urls: List[str], max_concurrent: int = 5) -> Dict[str, Any]:
        """Краулинг на множество URL-и с ограничение на concurrent заявки"""
        semaphore = asyncio.Semaphore(max_concurrent)

        async def crawl_with_semaphore(url):
            async with semaphore:
                return await self.crawl_url(url)

        # Изпълнение на всички краулинг задачи
        tasks = [crawl_with_semaphore(url) for url in urls]
        crawl_results = await asyncio.gather(*tasks, return_exceptions=True)

        # Обработка на резултатите
        results = {
            'total_urls': len(urls),
            'successful': 0,
            'failed': 0,
            'errors': []
        }

        for i, result in enumerate(crawl_results):
            url = urls[i]

            if isinstance(result, Exception):
                results['failed'] += 1
                results['errors'].append(f"{url}: {str(result)}")
            elif result.get('success', False):
                if self.save_crawl_result(url, result):
                    results['successful'] += 1
                else:
                    results['failed'] += 1
                    results['errors'].append(f"{url}: Failed to save")
            else:
                results['failed'] += 1
                results['errors'].append(f"{url}: {result.get('error', 'Unknown error')}")

        return results
```

**Тестов файл `tests/test_html_crawler.py`**:
```python
import pytest
import asyncio
from crawlers.html_crawler import HTMLCrawler

@pytest.fixture
def html_crawler():
    config = {
        'min_delay': 0.1,
        'max_delay': 0.2
    }
    return HTMLCrawler(config)

@pytest.mark.asyncio
async def test_html_crawler_single_url(html_crawler):
    """Тест на краулинг на една HTML страница"""
    test_url = "https://httpbin.org/html"  # Тестов сайт

    async with html_crawler:
        result = await html_crawler.crawl_url(test_url)

    assert result['success'] == True
    assert result['url'] == test_url
    assert 'html_content' in result
    assert 'text_content' in result
    assert 'title' in result
    assert result['status_code'] == 200

@pytest.mark.asyncio
async def test_html_crawler_invalid_url(html_crawler):
    """Тест на краулинг на невалиден URL"""
    test_url = "https://this-domain-does-not-exist-12345.com"

    async with html_crawler:
        result = await html_crawler.crawl_url(test_url)

    assert result['success'] == False
    assert 'error' in result

@pytest.mark.asyncio
async def test_html_crawler_multiple_urls(html_crawler):
    """Тест на краулинг на множество URL-и"""
    test_urls = [
        "https://httpbin.org/html",
        "https://httpbin.org/json",
        "https://httpbin.org/xml"
    ]

    async with html_crawler:
        results = await html_crawler.crawl_multiple_urls(test_urls, max_concurrent=2)

    assert results['total_urls'] == 3
    assert results['successful'] + results['failed'] == 3
    assert results['successful'] >= 1  # Поне един трябва да е успешен

def test_html_crawler_save_result(html_crawler):
    """Тест на запазване на резултат"""
    test_result = {
        'success': True,
        'url': 'https://test.com',
        'title': 'Test Page',
        'html_content': '<html><body>Test</body></html>',
        'text_content': 'Test',
        'meta_description': 'Test description',
        'links': [],
        'status_code': 200,
        'content_type': 'text/html',
        'content_length': 30
    }

    success = html_crawler.save_crawl_result('https://test.com', test_result)
    assert success == True
```

**Интеграционен тест `tests/test_html_crawler_integration.py`**:
```python
import pytest
import asyncio
from crawlers.html_crawler import HTMLCrawler
from database.db_manager import DatabaseManager

@pytest.mark.asyncio
async def test_full_html_crawling_workflow():
    """Пълен интеграционен тест на HTML краулинг"""

    # Конфигурация
    config = {
        'min_delay': 0.5,
        'max_delay': 1.0
    }

    # URL-и за тестване
    test_urls = [
        "https://httpbin.org/html",
        "https://example.com"
    ]

    # Краулинг
    crawler = HTMLCrawler(config)

    async with crawler:
        results = await crawler.run_crawl(test_urls)

    # Проверки
    assert results['total_urls'] == 2
    assert results['successful'] >= 1

    # Проверка в базата данни
    db_manager = DatabaseManager()
    unprocessed_pages = db_manager.get_unprocessed_pages()

    # Трябва да има поне една нова страница
    new_pages = [p for p in unprocessed_pages if p['url'] in test_urls]
    assert len(new_pages) >= 1

    print(f"Crawling results: {results}")
    print(f"New pages in database: {len(new_pages)}")
```

**Валидация**:
- [ ] HTMLCrawler клас се създава без грешки
- [ ] `python -c "from crawlers.html_crawler import HTMLCrawler; print('OK')"` работи
- [ ] `pytest tests/test_html_crawler.py -v` минава всички тестове
- [ ] `pytest tests/test_html_crawler_integration.py -v` минава интеграционния тест
- [ ] Реални страници се краулват и запазват в базата данни
- [ ] Async context manager работи правилно

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че HTML краулърът работи с реални сайтове**
- [ ] **Тествал съм с httpbin.org и example.com - данните се запазват в базата**
- [ ] **НЕ лъжа и НЕ си измислям - краулърът е реален и функционален**

---

### Задача 4.2: Първи MCP сървър endpoint
**Цел**: Създаване на базов FastAPI сървър с един работещ endpoint

**Стъпки**:
1. Създайте `mcp_server/basic_server.py`
2. Имплементирайте health check endpoint
3. Добавете basic search endpoint
4. Тествайте с реални заявки

**mcp_server/basic_server.py**:
```python
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import logging
import os
from dotenv import load_dotenv
from database.db_manager import DatabaseManager
import time
from datetime import datetime

# Зареждане на environment variables
load_dotenv()

# Инициализация на FastAPI
app = FastAPI(
    title="RAG MCP Server",
    description="Model Context Protocol Server for RAG System",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Database manager
db_manager = DatabaseManager()

# Логгер
logger = logging.getLogger("mcp_server")

# Pydantic модели
class HealthResponse(BaseModel):
    status: str
    timestamp: str
    database_connected: bool
    version: str

class SearchQuery(BaseModel):
    query: str = Field(..., min_length=1, max_length=500)
    limit: int = Field(default=5, ge=1, le=20)

class SearchResult(BaseModel):
    id: str
    url: str
    title: str
    content_snippet: str
    metadata: Dict[str, Any]

class SearchResponse(BaseModel):
    query: str
    results: List[SearchResult]
    total_found: int
    search_time_ms: float

# Middleware за логиране на заявки
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()

    response = await call_next(request)

    process_time = time.time() - start_time
    logger.info(
        f"{request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Time: {process_time:.3f}s"
    )

    return response

# Health check endpoint
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    try:
        # Тест на връзката с базата данни
        db_connected = db_manager.test_connection()

        return HealthResponse(
            status="healthy" if db_connected else "degraded",
            timestamp=datetime.now().isoformat(),
            database_connected=db_connected,
            version="1.0.0"
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Health check failed")

# Basic search endpoint
@app.post("/search", response_model=SearchResponse)
async def search_pages(query: SearchQuery):
    """Търсене в краулнатите страници"""
    start_time = time.time()

    try:
        logger.info(f"Search query: '{query.query}' with limit {query.limit}")

        # Простo текстово търсене в raw_pages таблицата
        # За сега използваме LIKE заявка, по-късно ще добавим векторно търсене
        search_term = f"%{query.query.lower()}%"

        result = db_manager.client.table('raw_pages') \
            .select('id, url, html_content, metadata') \
            .or_(f'url.ilike.{search_term},html_content.ilike.{search_term}') \
            .limit(query.limit) \
            .execute()

        search_results = []
        for row in result.data:
            # Извличане на title от metadata или HTML
            title = ""
            if row.get('metadata') and row['metadata'].get('title'):
                title = row['metadata']['title']
            else:
                # Опит за извличане на title от HTML
                from bs4 import BeautifulSoup
                try:
                    soup = BeautifulSoup(row['html_content'], 'html.parser')
                    if soup.title:
                        title = soup.title.string.strip()
                except:
                    title = "No title"

            # Създаване на snippet
            content = row['html_content'] or ""
            if len(content) > 200:
                # Намиране на позицията на търсения термин
                lower_content = content.lower()
                query_pos = lower_content.find(query.query.lower())

                if query_pos != -1:
                    # Snippet около търсения термин
                    start = max(0, query_pos - 100)
                    end = min(len(content), query_pos + 100)
                    snippet = content[start:end]
                    if start > 0:
                        snippet = "..." + snippet
                    if end < len(content):
                        snippet = snippet + "..."
                else:
                    # Първите 200 символа
                    snippet = content[:200] + "..."
            else:
                snippet = content

            search_results.append(SearchResult(
                id=row['id'],
                url=row['url'],
                title=title,
                content_snippet=snippet,
                metadata=row.get('metadata', {})
            ))

        search_time = (time.time() - start_time) * 1000  # в милисекунди

        return SearchResponse(
            query=query.query,
            results=search_results,
            total_found=len(search_results),
            search_time_ms=round(search_time, 2)
        )

    except Exception as e:
        logger.error(f"Search failed: {e}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint с информация за API"""
    return {
        "message": "RAG MCP Server",
        "version": "1.0.0",
        "endpoints": {
            "health": "/health",
            "search": "/search",
            "docs": "/docs"
        }
    }

# Стартиране на сървъра
if __name__ == "__main__":
    import uvicorn

    # Конфигуриране на логиране
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Стартиране на сървъра
    uvicorn.run(
        "basic_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
```

**Тестов файл `tests/test_basic_server.py`**:
```python
import pytest
from fastapi.testclient import TestClient
from mcp_server.basic_server import app
import json

@pytest.fixture
def client():
    return TestClient(app)

def test_root_endpoint(client):
    """Тест на root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data
    assert "endpoints" in data

def test_health_endpoint(client):
    """Тест на health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert "timestamp" in data
    assert "database_connected" in data
    assert "version" in data
    assert data["version"] == "1.0.0"

def test_search_endpoint_valid_query(client):
    """Тест на search endpoint с валидна заявка"""
    search_data = {
        "query": "test",
        "limit": 5
    }

    response = client.post("/search", json=search_data)
    assert response.status_code == 200

    data = response.json()
    assert "query" in data
    assert "results" in data
    assert "total_found" in data
    assert "search_time_ms" in data
    assert data["query"] == "test"
    assert isinstance(data["results"], list)
    assert isinstance(data["total_found"], int)
    assert isinstance(data["search_time_ms"], float)

def test_search_endpoint_invalid_query(client):
    """Тест на search endpoint с невалидна заявка"""
    # Празна заявка
    search_data = {
        "query": "",
        "limit": 5
    }

    response = client.post("/search", json=search_data)
    assert response.status_code == 422  # Validation error

def test_search_endpoint_invalid_limit(client):
    """Тест на search endpoint с невалиден лимит"""
    search_data = {
        "query": "test",
        "limit": 50  # Над максимума от 20
    }

    response = client.post("/search", json=search_data)
    assert response.status_code == 422  # Validation error

def test_openapi_docs(client):
    """Тест че OpenAPI документацията е достъпна"""
    response = client.get("/docs")
    assert response.status_code == 200

    response = client.get("/openapi.json")
    assert response.status_code == 200
    openapi_data = response.json()
    assert "openapi" in openapi_data
    assert "info" in openapi_data
```

**Ръчен тест скрипт `scripts/test_server.py`**:
```python
#!/usr/bin/env python3
"""
Ръчен тест на MCP сървъра
Стартирайте сървъра и след това изпълнете този скрипт
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_health():
    """Тест на health endpoint"""
    print("Testing health endpoint...")
    response = requests.get(f"{BASE_URL}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()

def test_search():
    """Тест на search endpoint"""
    print("Testing search endpoint...")

    search_data = {
        "query": "test",
        "limit": 3
    }

    response = requests.post(f"{BASE_URL}/search", json=search_data)
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()

def test_root():
    """Тест на root endpoint"""
    print("Testing root endpoint...")
    response = requests.get(f"{BASE_URL}/")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()

if __name__ == "__main__":
    print("Starting MCP Server tests...")
    print("Make sure the server is running on http://localhost:8000")
    print()

    try:
        test_root()
        test_health()
        test_search()
        print("All tests completed!")
    except requests.exceptions.ConnectionError:
        print("ERROR: Could not connect to server. Make sure it's running on http://localhost:8000")
    except Exception as e:
        print(f"ERROR: {e}")
```

**Валидация**:
- [ ] FastAPI сървърът стартира без грешки
- [ ] `python mcp_server/basic_server.py` стартира сървъра на порт 8000
- [ ] `curl http://localhost:8000/health` връща JSON отговор
- [ ] `curl http://localhost:8000/` връща информация за API
- [ ] `pytest tests/test_basic_server.py -v` минава всички тестове
- [ ] `python scripts/test_server.py` работи успешно
- [ ] OpenAPI документацията е достъпна на http://localhost:8000/docs

**✅ ПОТВЪРЖДЕНИЕ**:
- [ ] **100% съм убеден че MCP сървърът работи и отговаря на заявки**
- [ ] **Тествал съм всички endpoints с curl и pytest**
- [ ] **НЕ лъжа и НЕ си измислям - сървърът е реален и функционален**
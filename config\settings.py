"""
Configuration settings using Pydantic V2
"""
import os
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, field_validator, ConfigDict
from pydantic_settings import BaseSettings


class DatabaseConfig(BaseModel):
    """Database configuration"""
    url: str
    service_key: str
    max_connections: int = 10
    connection_timeout: int = 30
    
    @field_validator('url')
    @classmethod
    def validate_url(cls, v):
        if not v.startswith('https://'):
            raise ValueError('Database URL must start with https://')
        return v


class OpenAIConfig(BaseModel):
    """OpenAI configuration"""
    api_key: str
    model: str = "text-embedding-3-small"
    max_tokens: int = 8192
    batch_size: int = 20
    rate_limit_requests_per_minute: int = 3000
    
    @field_validator('api_key')
    @classmethod
    def validate_api_key(cls, v):
        if not v.startswith('sk-'):
            raise ValueError('OpenAI API key must start with sk-')
        return v


class RedisConfig(BaseModel):
    """Redis configuration"""
    url: str = "redis://localhost:6379"
    db: int = 0
    max_connections: int = 10
    socket_timeout: int = 30
    cache_ttl: int = 3600


class CrawlerConfig(BaseModel):
    """Crawler configuration"""
    concurrent_requests: int = 5
    delay_min: float = 1.0
    delay_max: float = 3.0
    timeout: int = 30
    user_agents: List[str] = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
    ]
    
    @field_validator('concurrent_requests')
    @classmethod
    def validate_concurrent_requests(cls, v):
        if v < 1 or v > 20:
            raise ValueError('Concurrent requests must be between 1 and 20')
        return v


class ServerConfig(BaseModel):
    """Server configuration"""
    host: str = "0.0.0.0"
    port: int = 8000
    log_level: str = "INFO"
    workers: int = 1
    
    @field_validator('log_level')
    @classmethod
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of {valid_levels}')
        return v.upper()


class MonitoringConfig(BaseModel):
    """Monitoring configuration"""
    prometheus_enabled: bool = True
    metrics_port: int = 9090
    health_check_interval: int = 30


class ChunkingConfig(BaseModel):
    """Text chunking configuration"""
    chunk_size: int = 1000
    chunk_overlap: int = 200
    min_chunk_size: int = 50
    max_chunk_size: int = 2000
    separators: List[str] = ["\n\n", "\n", ".", "!", "?", ";", ",", " ", ""]


class Settings(BaseSettings):
    """Main application settings"""
    model_config = ConfigDict(
        env_file='.env',
        env_file_encoding='utf-8',
        case_sensitive=False,
        extra='ignore'
    )
    
    # Database
    supabase_url: str
    supabase_service_key: str
    
    # OpenAI
    openai_api_key: str
    
    # Redis
    redis_url: str = "redis://localhost:6379"
    
    # Server
    server_host: str = "0.0.0.0"
    server_port: int = 8000
    log_level: str = "INFO"
    
    # Crawling
    crawler_concurrent_requests: int = 5
    crawler_delay_min: float = 1.0
    crawler_delay_max: float = 3.0
    
    # Embedding
    embedding_batch_size: int = 20
    embedding_cache_ttl: int = 3600
    
    # Monitoring
    prometheus_enabled: bool = True
    metrics_port: int = 9090
    
    # Chunking
    chunk_size: int = 1000
    chunk_overlap: int = 200
    
    @property
    def database_config(self) -> DatabaseConfig:
        return DatabaseConfig(
            url=self.supabase_url,
            service_key=self.supabase_service_key
        )
    
    @property
    def openai_config(self) -> OpenAIConfig:
        return OpenAIConfig(
            api_key=self.openai_api_key,
            batch_size=self.embedding_batch_size
        )
    
    @property
    def redis_config(self) -> RedisConfig:
        return RedisConfig(
            url=self.redis_url,
            cache_ttl=self.embedding_cache_ttl
        )
    
    @property
    def crawler_config(self) -> CrawlerConfig:
        return CrawlerConfig(
            concurrent_requests=self.crawler_concurrent_requests,
            delay_min=self.crawler_delay_min,
            delay_max=self.crawler_delay_max
        )
    
    @property
    def server_config(self) -> ServerConfig:
        return ServerConfig(
            host=self.server_host,
            port=self.server_port,
            log_level=self.log_level
        )
    
    @property
    def monitoring_config(self) -> MonitoringConfig:
        return MonitoringConfig(
            prometheus_enabled=self.prometheus_enabled,
            metrics_port=self.metrics_port
        )
    
    @property
    def chunking_config(self) -> ChunkingConfig:
        return ChunkingConfig(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap
        )


# Global settings instance
settings = Settings()

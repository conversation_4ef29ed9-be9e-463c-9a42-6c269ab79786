#!/usr/bin/env python3
"""
Database setup script for RAG system
Creates tables, indexes, and functions
"""
import asyncio
import logging
import os
from supabase import create_client, Client
from database.manager import VECTOR_SEARCH_FUNCTIONS


async def setup_database():
    """Setup database schema and functions"""
    
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Load environment variables
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
    
    if not supabase_url or not supabase_key:
        logger.error("Missing Supabase credentials in environment variables")
        return False
    
    try:
        # Create Supabase client
        supabase: Client = create_client(supabase_url, supabase_key)
        logger.info("Connected to Supabase")
        
        # Read schema file
        schema_path = os.path.join(os.path.dirname(__file__), '..', 'database', 'schema.sql')
        
        with open(schema_path, 'r') as f:
            schema_sql = f.read()
        
        logger.info("Executing database schema...")
        
        # Execute schema creation
        result = supabase.rpc('sql', {'query': schema_sql}).execute()
        
        if result.data:
            logger.info("✅ Database schema created successfully")
        else:
            logger.warning("Schema execution completed (no data returned)")
        
        # Create vector search functions
        logger.info("Creating vector search functions...")
        
        functions_result = supabase.rpc('sql', {'query': VECTOR_SEARCH_FUNCTIONS}).execute()
        
        if functions_result.data is not None:
            logger.info("✅ Vector search functions created successfully")
        else:
            logger.warning("Functions execution completed")
        
        # Verify tables exist
        logger.info("Verifying table creation...")
        
        tables_query = """
        SELECT table_name FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name IN ('raw_pages', 'document_sections', 'documents', 'clean_pages');
        """
        
        tables_result = supabase.rpc('sql', {'query': tables_query}).execute()
        
        if tables_result.data:
            created_tables = [row['table_name'] for row in tables_result.data]
            logger.info(f"✅ Created tables: {created_tables}")
        
        # Verify extensions
        logger.info("Verifying extensions...")
        
        extensions_query = """
        SELECT extname FROM pg_extension 
        WHERE extname IN ('vector', 'uuid-ossp', 'pg_cron');
        """
        
        ext_result = supabase.rpc('sql', {'query': extensions_query}).execute()
        
        if ext_result.data:
            extensions = [row['extname'] for row in ext_result.data]
            logger.info(f"✅ Active extensions: {extensions}")
            
            if 'vector' not in extensions:
                logger.error("❌ pgvector extension not found!")
                return False
        
        # Verify indexes
        logger.info("Verifying indexes...")
        
        indexes_query = """
        SELECT indexname FROM pg_indexes 
        WHERE tablename IN ('raw_pages', 'document_sections') 
        AND indexname LIKE 'idx_%';
        """
        
        idx_result = supabase.rpc('sql', {'query': indexes_query}).execute()
        
        if idx_result.data:
            indexes = [row['indexname'] for row in idx_result.data]
            logger.info(f"✅ Created indexes: {indexes}")
            
            # Check for HNSW index specifically
            hnsw_found = any('embedding' in idx for idx in indexes)
            if hnsw_found:
                logger.info("✅ HNSW vector index created")
            else:
                logger.warning("⚠️ HNSW vector index not found")
        
        logger.info("🎉 Database setup completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database setup failed: {e}")
        return False


async def test_database_functionality():
    """Test basic database functionality"""
    
    logger = logging.getLogger(__name__)
    
    try:
        from database.manager import DatabaseManager
        
        # Test database manager
        db_manager = DatabaseManager()
        
        # Test connection
        connection_ok = await db_manager.test_connection()
        if not connection_ok:
            logger.error("❌ Database connection test failed")
            return False
        
        logger.info("✅ Database connection test passed")
        
        # Test UPSERT functionality
        test_page_id = await db_manager.upsert_raw_page(
            url="https://test.example.com/setup-test",
            html_content="<html><body>Setup test content</body></html>",
            metadata={"test": True, "setup_script": True}
        )
        
        if test_page_id:
            logger.info(f"✅ UPSERT test passed - created page {test_page_id}")
            
            # Test status update
            status_ok = await db_manager.update_page_status(test_page_id, 'completed')
            if status_ok:
                logger.info("✅ Status update test passed")
            else:
                logger.warning("⚠️ Status update test failed")
        else:
            logger.error("❌ UPSERT test failed")
            return False
        
        # Test health check
        health = await db_manager.get_health_status()
        if health.get('status') == 'healthy':
            logger.info("✅ Health check passed")
        else:
            logger.warning(f"⚠️ Health check status: {health.get('status')}")
        
        logger.info("🎉 Database functionality tests completed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database functionality test failed: {e}")
        return False


async def main():
    """Main setup function"""
    
    print("🚀 Starting RAG System Database Setup")
    print("=" * 50)
    
    # Setup database schema
    setup_success = await setup_database()
    
    if not setup_success:
        print("❌ Database setup failed!")
        return 1
    
    print("\n" + "=" * 50)
    print("🧪 Testing Database Functionality")
    print("=" * 50)
    
    # Test functionality
    test_success = await test_database_functionality()
    
    if not test_success:
        print("❌ Database functionality tests failed!")
        return 1
    
    print("\n" + "=" * 50)
    print("✅ RAG System Database Setup Complete!")
    print("=" * 50)
    print("\nNext steps:")
    print("1. Update your .env file with correct Supabase credentials")
    print("2. Run: python scripts/test_database.py")
    print("3. Start the MCP server: python -m mcp_server.server")
    print("4. Test the pipeline: python -m workflows.rag_pipeline")
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())

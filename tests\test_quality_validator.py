"""
Tests for Content Quality Validator
Ensures quality gates work correctly to filter out low-quality content
"""
import pytest
from processors.quality_validator import ComprehensiveDataQualityValidator, ContentType


@pytest.fixture
def validator():
    """Quality validator fixture"""
    return ComprehensiveDataQualityValidator()


def test_high_quality_content(validator):
    """Test that high-quality content passes validation"""
    
    high_quality_content = """
    Machine Learning in Healthcare: A Comprehensive Overview
    
    Machine learning (ML) has revolutionized healthcare by enabling predictive analytics, 
    personalized treatment plans, and improved diagnostic accuracy. This article explores 
    the current applications and future potential of ML in medical practice.
    
    Key Applications:
    1. Medical imaging analysis with 95% accuracy rates
    2. Drug discovery acceleration by 40-60%
    3. Personalized treatment recommendations
    4. Early disease detection systems
    
    Recent studies show that ML algorithms can detect certain cancers with higher accuracy 
    than human radiologists. For example, Google's AI system achieved 94.5% accuracy in 
    mammography screening, compared to 88.0% for human radiologists.
    
    The integration of ML in healthcare requires careful consideration of data privacy, 
    algorithm bias, and regulatory compliance. Healthcare providers must ensure that 
    AI systems are transparent, explainable, and continuously monitored for performance.
    """
    
    result = validator.validate_content_quality(high_quality_content)
    
    assert result['is_acceptable'] is True
    assert result['quality_score'] > 0.7
    assert result['content_type'] == ContentType.VALUABLE_CONTENT.value
    assert len(result['failed_gates']) == 0


def test_low_quality_content(validator):
    """Test that low-quality content fails validation"""
    
    low_quality_content = """
    Click here to buy now! Limited time offer!
    Subscribe to our newsletter for amazing deals.
    """
    
    result = validator.validate_content_quality(low_quality_content)
    
    assert result['is_acceptable'] is False
    assert result['quality_score'] < 0.7
    assert len(result['failed_gates']) > 0


def test_boilerplate_content(validator):
    """Test that boilerplate content is rejected"""
    
    boilerplate_content = """
    Home | About | Contact | Privacy Policy | Terms of Service
    Copyright 2024. All rights reserved.
    Subscribe to our newsletter.
    Follow us on social media.
    """
    
    result = validator.validate_content_quality(boilerplate_content)
    
    assert result['is_acceptable'] is False
    assert result['content_type'] in [ContentType.BOILERPLATE.value, ContentType.NAVIGATION.value]


def test_error_page_content(validator):
    """Test that error pages are rejected"""
    
    error_content = """
    404 - Page Not Found
    
    The page you are looking for could not be found.
    Please check the URL or return to the homepage.
    """
    
    result = validator.validate_content_quality(error_content)
    
    assert result['is_acceptable'] is False
    assert result['content_type'] == ContentType.ERROR_PAGE.value


def test_advertisement_content(validator):
    """Test that advertisement content is rejected"""
    
    ad_content = """
    AMAZING DEAL! Buy now and save 50%!
    Limited time offer - click here to purchase!
    Sponsored content - best products for you!
    """
    
    result = validator.validate_content_quality(ad_content)
    
    assert result['is_acceptable'] is False
    assert result['content_type'] == ContentType.ADVERTISEMENT.value


def test_short_content(validator):
    """Test that very short content fails length validation"""
    
    short_content = "Short text."
    
    result = validator.validate_content_quality(short_content)
    
    assert result['is_acceptable'] is False
    assert 'content_length' in result['failed_gates']


def test_content_length_scoring(validator):
    """Test content length scoring function"""
    
    # Test different content lengths
    very_short = "Hi"
    short = "This is a short piece of text that should get a low score."
    medium = "This is a medium-length piece of text. " * 10
    long = "This is a longer piece of text that should score well. " * 50
    very_long = "This is very long content. " * 500
    
    assert validator._score_content_length(very_short) == 0.0
    assert validator._score_content_length(short) < 0.5
    assert validator._score_content_length(medium) > 0.5
    assert validator._score_content_length(long) >= 0.9
    assert validator._score_content_length(very_long) < 1.0  # Penalty for too long


def test_information_density_scoring(validator):
    """Test information density scoring"""
    
    # High information density
    dense_content = "Machine learning algorithms achieve 95% accuracy in medical diagnosis. The study included 10,000 patients across 50 hospitals. Results showed significant improvement in early detection rates."
    
    # Low information density (repetitive)
    sparse_content = "The the the the the the the the the the the the the the the the the the the the the the the the the the the the the the."
    
    dense_score = validator._score_information_density(dense_content)
    sparse_score = validator._score_information_density(sparse_content)
    
    assert dense_score > sparse_score
    assert dense_score > 0.5
    assert sparse_score < 0.3


def test_structure_quality_scoring(validator):
    """Test structure quality scoring"""
    
    # Well-structured content
    structured_content = """
    Title: Machine Learning Overview
    
    Introduction:
    Machine learning is a subset of artificial intelligence.
    
    Key Points:
    - Supervised learning
    - Unsupervised learning
    - Reinforcement learning
    
    Conclusion:
    ML has many applications in various fields.
    """
    
    # Poorly structured content
    unstructured_content = "Machine learning is a subset of artificial intelligence supervised learning unsupervised learning reinforcement learning ML has many applications"
    
    structured_score = validator._score_structure_quality(structured_content)
    unstructured_score = validator._score_structure_quality(unstructured_content)
    
    assert structured_score > unstructured_score


def test_uniqueness_scoring(validator):
    """Test uniqueness (anti-boilerplate) scoring"""
    
    # Unique content
    unique_content = "This research paper presents novel findings about quantum computing applications in cryptography. The experimental results demonstrate significant improvements in security protocols."
    
    # Boilerplate content
    boilerplate_content = "Privacy policy terms of service all rights reserved copyright 2024 subscribe to newsletter follow us on social media"
    
    unique_score = validator._score_uniqueness(unique_content)
    boilerplate_score = validator._score_uniqueness(boilerplate_content)
    
    assert unique_score > boilerplate_score
    assert unique_score > 0.7
    assert boilerplate_score < 0.3


def test_language_confidence_scoring(validator):
    """Test language confidence scoring"""
    
    # Clear English content
    clear_content = "This is a clear English sentence with proper grammar and structure."
    
    # Unclear/mixed content
    unclear_content = "asdkjf lkjsdf lkjsdf random text 123 !@# mixed content"
    
    clear_score = validator._score_language_confidence(clear_content)
    unclear_score = validator._score_language_confidence(unclear_content)
    
    # Note: This test might be flaky depending on langdetect behavior
    # We mainly test that the function doesn't crash
    assert isinstance(clear_score, float)
    assert isinstance(unclear_score, float)
    assert 0.0 <= clear_score <= 1.0
    assert 0.0 <= unclear_score <= 1.0


def test_content_type_classification(validator):
    """Test content type classification"""
    
    # Test different content types
    test_cases = [
        ("404 not found error", ContentType.ERROR_PAGE),
        ("Home About Contact Privacy Terms", ContentType.NAVIGATION),
        ("Copyright 2024 all rights reserved", ContentType.FOOTER),
        ("Buy now limited time offer click here", ContentType.ADVERTISEMENT),
        ("Privacy policy cookie policy terms of service", ContentType.BOILERPLATE),
        ("This is valuable content about machine learning", ContentType.VALUABLE_CONTENT)
    ]
    
    for content, expected_type in test_cases:
        result_type = validator._classify_content_type(content)
        assert result_type == expected_type


def test_quality_gates_configuration(validator):
    """Test that quality gates are properly configured"""
    
    assert len(validator.quality_gates) > 0
    
    # Check that all gates have required properties
    for gate in validator.quality_gates:
        assert hasattr(gate, 'name')
        assert hasattr(gate, 'min_score')
        assert hasattr(gate, 'weight')
        assert hasattr(gate, 'description')
        assert 0.0 <= gate.min_score <= 1.0
        assert 0.0 <= gate.weight <= 1.0


def test_validation_result_structure(validator):
    """Test that validation results have correct structure"""
    
    test_content = "This is test content for validation."
    result = validator.validate_content_quality(test_content)
    
    # Check required fields
    required_fields = [
        'is_acceptable', 'quality_score', 'content_type',
        'individual_scores', 'failed_gates', 'content_hash',
        'word_count', 'char_count', 'reason'
    ]
    
    for field in required_fields:
        assert field in result
    
    # Check data types
    assert isinstance(result['is_acceptable'], bool)
    assert isinstance(result['quality_score'], float)
    assert isinstance(result['content_type'], str)
    assert isinstance(result['individual_scores'], dict)
    assert isinstance(result['failed_gates'], list)
    assert isinstance(result['content_hash'], str)
    assert isinstance(result['word_count'], int)
    assert isinstance(result['char_count'], int)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

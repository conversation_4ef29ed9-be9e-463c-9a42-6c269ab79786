# Задачи за имплементация на RAG система

## Задача 1: Определяне на изисквания и избор на технологии
- Формулиране на обем на данните (хиляди или милиони страници)
- Определяне на езици (предимно български и английски)
- Определяне на честота на обновяване (ежедневна или по-рядка)
- Оценка на ограниченията на безплатния план на Supabase
- Избор на инструмент за краулинг: aiohttp за статични и Playwright за динамични страници
- Избор на метод за извличане на съдържание от PDF: Unstructured с fallback към pymupdf4llm
- Подготовка на Supabase: включване на pgvector и pg_cron/pgmq
- Дефиниране на логическа структура (трислойна медалистична архитектура)

## Задача 2: Създаване на краулинг скриптове
- Реализация на механизъм за автоматично обхождане на целевите сайтове
- Имплементация на асинхронно програмиране с asyncio
- Внедряване на delay между заявките и ротация на User-Agent
- Записване на URL, суров HTML код и свалени PDF/файлове

## Задача 3: Съхранение на суровите данни ("бронзов слой")
- Създаване на таблици в Supabase/Postgres за суровите данни
- Използване на JSONB поле за допълнителни метаданни
- Добавяне на колони за времеви клейм
- Имплементация на система за мониторинг с Prometheus метрики

## Задача 4: Парсинг и почистване на данните ("сребърен слой")
- Обработка на суровия HTML/документи с BeautifulSoup
- Използване на Unstructured и pymupdf4llm за PDF
- Разделяне на големи документи на логични части с интелигентен chunker
- Детектиране на езика с langdetect
- Създаване на алгоритъм за дедупликация

## Задача 5: Дефиниране на окончателни таблици ("златен слой")
- Проектиране на крайните таблици за RAG-извличане с партициониране
- Добавяне на HNSW индекс върху embedding
- Включване на RLS политики за достъп

## Задача 6: Генериране на ембединг и индексиране
- Настройка на автоматично създаване на векторен ембединг
- Използване на SQL тригери за асинхронно извикване на модел
- Имплементация на batch обработка на ембединги

## Задача 7: Реализация на Retrieval (RAG) логика
- Създаване на скрипт/микросървис за векторно търсене
- Добавяне на Redis кеширане на резултатите
- Имплементация на rate limiting
- Добавяне на ключово (full-text) търсене

## Задача 8: Разработка на MCP сървър
- Изграждане на MCP сървър по спецификацията на Model Context Protocol
- Дефиниране на инструменти (tools) и ресурси (resources)
- Добавяне на Redis кеширане и rate limiting
- Имплементация на мониторинг на производителността

## Задача 9: Оркестрация и планиране
- Организиране на изпълнението чрез планиран график
- Използване на pg_cron за автоматизация в Supabase
- Осигуряване на механизъм за наблюдение и известяване при грешки
- Имплементация на health checks

## Задача 10: Тестове и непрекъснато наблюдение
- Проверка на функционалността чрез тестови сценарии
- Интегриране на проверки за дублирани или липсващи данни
- Автоматизиране на тестовете с CI/CD pipeline
- Организиране на регулярен процес за проверка на данните и резултатите
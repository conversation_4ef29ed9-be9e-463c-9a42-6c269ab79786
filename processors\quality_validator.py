"""
Comprehensive Data Quality Validator
Prevents "garbage in, garbage out" by filtering low-quality content
"""
import re
import hashlib
import logging
from typing import Dict, Any, List
from dataclasses import dataclass
from enum import Enum


class ContentType(Enum):
    ARTICLE = "article"
    NAVIGATION = "navigation"
    FOOTER = "footer"
    ADVERTISEMENT = "advertisement"
    BOILERPLATE = "boilerplate"
    ERROR_PAGE = "error_page"
    VALUABLE_CONTENT = "valuable_content"


@dataclass
class QualityGate:
    name: str
    min_score: float
    weight: float
    description: str


class ComprehensiveDataQualityValidator:
    """
    Validates content quality to prevent low-quality data from entering the vector database
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Quality gates with thresholds
        self.quality_gates = [
            QualityGate("content_length", 0.6, 0.2, "Adequate content length"),
            QualityGate("information_density", 0.7, 0.25, "High information density"),
            QualityGate("language_confidence", 0.8, 0.15, "Clear language detection"),
            QualityGate("structure_quality", 0.6, 0.2, "Good content structure"),
            QualityGate("uniqueness", 0.8, 0.2, "Unique, non-boilerplate content")
        ]
        
        # Boilerplate patterns
        self.boilerplate_patterns = [
            r'cookie.*policy',
            r'privacy.*policy', 
            r'terms.*service',
            r'all rights reserved',
            r'copyright.*\d{4}',
            r'subscribe.*newsletter',
            r'follow.*social',
            r'404.*not.*found',
            r'page.*not.*found',
            r'error.*occurred',
            r'javascript.*disabled',
            r'enable.*javascript'
        ]
        
        # Navigation indicators
        self.navigation_patterns = [
            r'home.*about.*contact',
            r'menu.*navigation',
            r'breadcrumb',
            r'previous.*next',
            r'page \d+ of \d+',
            r'sort by.*filter by'
        ]
        
        # Advertisement patterns
        self.ad_patterns = [
            r'advertisement',
            r'sponsored.*content',
            r'click.*here.*buy',
            r'limited.*time.*offer',
            r'buy.*now.*save'
        ]
    
    def validate_content_quality(self, content: str, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Comprehensive quality validation
        Returns validation result with quality score and acceptance status
        """
        
        # Basic content type classification
        content_type = self._classify_content_type(content)
        
        # Reject obviously bad content types immediately
        if content_type in [ContentType.NAVIGATION, ContentType.FOOTER, 
                           ContentType.ADVERTISEMENT, ContentType.ERROR_PAGE]:
            return {
                'is_acceptable': False,
                'content_type': content_type.value,
                'reason': f'Content classified as {content_type.value}',
                'quality_score': 0.0,
                'failed_gates': ['content_type']
            }
        
        # Detailed quality scoring
        scores = {}
        
        # 1. Content Length Score
        scores['content_length'] = self._score_content_length(content)
        
        # 2. Information Density Score  
        scores['information_density'] = self._score_information_density(content)
        
        # 3. Language Confidence Score
        scores['language_confidence'] = self._score_language_confidence(content)
        
        # 4. Structure Quality Score
        scores['structure_quality'] = self._score_structure_quality(content)
        
        # 5. Uniqueness Score (anti-boilerplate)
        scores['uniqueness'] = self._score_uniqueness(content)
        
        # Calculate total weighted score
        total_score = 0.0
        for gate in self.quality_gates:
            gate_score = scores.get(gate.name, 0.0)
            total_score += gate_score * gate.weight
        
        # Check which gates failed
        failed_gates = []
        for gate in self.quality_gates:
            if scores.get(gate.name, 0.0) < gate.min_score:
                failed_gates.append(gate.name)
        
        # Final acceptance decision
        is_acceptable = len(failed_gates) == 0 and total_score >= 0.7
        
        return {
            'is_acceptable': is_acceptable,
            'quality_score': total_score,
            'content_type': content_type.value,
            'individual_scores': scores,
            'failed_gates': failed_gates,
            'content_hash': hashlib.sha256(content.encode()).hexdigest(),
            'word_count': len(content.split()),
            'char_count': len(content),
            'reason': f"Failed gates: {failed_gates}" if failed_gates else "Passed all quality gates"
        }
    
    def _classify_content_type(self, content: str) -> ContentType:
        """Classify content type based on patterns"""
        content_lower = content.lower()
        
        # Check for error pages
        error_indicators = ['404', 'not found', 'error occurred', 'page not found']
        if any(indicator in content_lower for indicator in error_indicators):
            return ContentType.ERROR_PAGE
        
        # Check for boilerplate
        boilerplate_matches = sum(1 for pattern in self.boilerplate_patterns 
                                 if re.search(pattern, content_lower, re.IGNORECASE))
        if boilerplate_matches >= 3:
            return ContentType.BOILERPLATE
        
        # Check for navigation
        nav_matches = sum(1 for pattern in self.navigation_patterns 
                         if re.search(pattern, content_lower, re.IGNORECASE))
        if nav_matches >= 2:
            return ContentType.NAVIGATION
        
        # Check for advertisements
        ad_matches = sum(1 for pattern in self.ad_patterns 
                        if re.search(pattern, content_lower, re.IGNORECASE))
        if ad_matches >= 2:
            return ContentType.ADVERTISEMENT
        
        # Check for footer content
        if len(content) < 200 and any(word in content_lower for word in 
                                     ['copyright', 'rights reserved', 'privacy', 'terms']):
            return ContentType.FOOTER
        
        return ContentType.VALUABLE_CONTENT
    
    def _score_content_length(self, content: str) -> float:
        """Score content based on length"""
        length = len(content.strip())
        
        if length < 100:
            return 0.0
        elif length < 300:
            return 0.3
        elif length < 800:
            return 0.7
        elif length < 3000:
            return 1.0
        elif length < 10000:
            return 0.9
        else:
            return 0.6  # Too long might be spam
    
    def _score_information_density(self, content: str) -> float:
        """Score information density"""
        words = content.split()
        if not words:
            return 0.0
        
        # Unique words ratio
        unique_words = set(word.lower().strip('.,!?;:') for word in words)
        uniqueness_ratio = len(unique_words) / len(words)
        
        # Numbers and facts presence
        numbers = len(re.findall(r'\d+', content))
        number_density = min(numbers / len(words) * 10, 0.3)
        
        # Punctuation density (structure indicator)
        special_chars = len(re.findall(r'[.!?;:]', content))
        punctuation_density = min(special_chars / len(words) * 5, 0.2)
        
        return min(uniqueness_ratio + number_density + punctuation_density, 1.0)
    
    def _score_language_confidence(self, content: str) -> float:
        """Score language detection confidence"""
        try:
            from langdetect import detect_langs
            
            if len(content.strip()) < 50:
                return 0.0
            
            lang_probs = detect_langs(content)
            if lang_probs:
                return lang_probs[0].prob
            return 0.0
            
        except Exception as e:
            self.logger.warning(f"Language detection failed: {e}")
            return 0.0
    
    def _score_structure_quality(self, content: str) -> float:
        """Score structural quality"""
        lines = content.split('\n')
        non_empty_lines = [line.strip() for line in lines if line.strip()]
        
        if len(non_empty_lines) < 2:
            return 0.0
        
        # Line length variance (indicates structure)
        line_lengths = [len(line) for line in non_empty_lines]
        avg_length = sum(line_lengths) / len(line_lengths)
        variance = sum((length - avg_length) ** 2 for length in line_lengths) / len(line_lengths)
        
        structure_score = min(variance / 1000, 0.5)
        
        # Paragraph presence
        paragraph_score = min(len(content.split('\n\n')) / 10, 0.3)
        
        # List indicators
        list_indicators = len(re.findall(r'^\s*[-*•]\s+', content, re.MULTILINE))
        list_score = min(list_indicators / 20, 0.2)
        
        return structure_score + paragraph_score + list_score
    
    def _score_uniqueness(self, content: str) -> float:
        """Score uniqueness (anti-boilerplate)"""
        content_lower = content.lower()
        
        # Count boilerplate matches
        boilerplate_matches = sum(1 for pattern in self.boilerplate_patterns 
                                 if re.search(pattern, content_lower, re.IGNORECASE))
        
        # Penalty for boilerplate
        boilerplate_penalty = min(boilerplate_matches * 0.2, 0.8)
        
        # Sentence uniqueness
        sentences = re.split(r'[.!?]+', content)
        unique_sentences = set(s.strip().lower() for s in sentences if s.strip())
        
        if sentences:
            sentence_uniqueness = len(unique_sentences) / len(sentences)
        else:
            sentence_uniqueness = 0.0
        
        return max(0.0, sentence_uniqueness - boilerplate_penalty)

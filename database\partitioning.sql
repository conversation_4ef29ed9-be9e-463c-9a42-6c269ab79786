-- Партициониране на таблицата document_sections по дати
CREATE TABLE document_sections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id),
    content TEXT NOT NULL,
    section_index INTEGER,
    section_type VARCHAR(20),
    embedding VECTOR(1536),
    embedding_model TEXT DEFAULT 'text-embedding-3-small',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
) PARTITION BY RANGE (created_at);

-- Създаване на месечни партиции
CREATE TABLE document_sections_2025_01 PARTITION OF document_sections
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE document_sections_2025_02 PARTITION OF document_sections
    FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

CREATE TABLE document_sections_2025_03 PARTITION OF document_sections
    FOR VALUES FROM ('2025-03-01') TO ('2025-04-01');

-- Функция за автоматично създаване на партиции
CREATE OR REPLACE FUNCTION create_partition_for_month()
RETURNS VOID AS $$
DECLARE
    next_month DATE;
    partition_name TEXT;
    start_date TEXT;
    end_date TEXT;
BEGIN
    -- Изчисляване на следващия месец
    next_month := date_trunc('month', now()) + interval '2 month';
    
    -- Създаване на име на партицията
    partition_name := 'document_sections_' || to_char(next_month, 'YYYY_MM');
    
    -- Начална и крайна дата
    start_date := to_char(next_month, 'YYYY-MM-01');
    end_date := to_char(next_month + interval '1 month', 'YYYY-MM-01');
    
    -- Проверка дали партицията вече съществува
    IF NOT EXISTS (
        SELECT 1 FROM pg_class c
        JOIN pg_namespace n ON n.oid = c.relnamespace
        WHERE c.relname = partition_name AND n.nspname = 'public'
    ) THEN
        -- Създаване на партицията
        EXECUTE format(
            'CREATE TABLE %I PARTITION OF document_sections FOR VALUES FROM (%L) TO (%L)',
            partition_name, start_date, end_date
        );
        
        RAISE NOTICE 'Created partition %', partition_name;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Създаване на cron job за автоматично създаване на партиции
SELECT cron.schedule('0 0 1 * *', 'SELECT create_partition_for_month()');
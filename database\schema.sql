-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Documents table (Bronze layer)
CREATE TABLE IF NOT EXISTS documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    url TEXT UNIQUE NOT NULL,
    title TEXT,
    content_type VARCHAR(50),
    language VARCHAR(10),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Raw pages table (Bronze layer)
CREATE TABLE IF NOT EXISTS raw_pages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    url TEXT NOT NULL,
    html_content TEXT,
    metadata JSONB DEFAULT '{}',
    content_hash TEXT UNIQUE,
    fetch_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_processed BOOLEAN DEFAULT FALSE,
    processing_status VARCHAR(20) DEFAULT 'pending',
    processing_attempts INTEGER DEFAULT 0,
    last_error TEXT,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Clean pages table (Silver layer)
CREATE TABLE IF NOT EXISTS clean_pages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    raw_page_id UUID REFERENCES raw_pages(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    language VARCHAR(10),
    content_hash TEXT UNIQUE NOT NULL,
    metadata JSONB DEFAULT '{}',
    quality_score FLOAT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Document sections table (Gold layer)
CREATE TABLE IF NOT EXISTS document_sections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id UUID REFERENCES documents(id) ON DELETE CASCADE,
    raw_page_id UUID REFERENCES raw_pages(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    section_index INTEGER,
    section_type VARCHAR(20),
    language VARCHAR(10),
    embedding VECTOR(1536),
    embedding_model TEXT DEFAULT 'text-embedding-3-small',
    content_hash TEXT UNIQUE NOT NULL,
    content_tsv TSVECTOR,
    is_embedded BOOLEAN DEFAULT FALSE,
    embedding_status VARCHAR(20) DEFAULT 'pending',
    embedding_attempts INTEGER DEFAULT 0,
    embedding_error TEXT,
    quality_score FLOAT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_raw_pages_url ON raw_pages(url);
CREATE INDEX IF NOT EXISTS idx_raw_pages_hash ON raw_pages(content_hash);
CREATE INDEX IF NOT EXISTS idx_raw_pages_status ON raw_pages(processing_status);
CREATE INDEX IF NOT EXISTS idx_raw_pages_processed ON raw_pages(is_processed);

CREATE INDEX IF NOT EXISTS idx_clean_pages_hash ON clean_pages(content_hash);
CREATE INDEX IF NOT EXISTS idx_clean_pages_language ON clean_pages(language);

-- HNSW index for vector search (CRITICAL: Use HNSW instead of IVFFLAT)
CREATE INDEX IF NOT EXISTS idx_document_sections_embedding ON document_sections
USING hnsw (embedding vector_cosine_ops)
WITH (m = 32, ef_construction = 128);

-- GIN index for full-text search
CREATE INDEX IF NOT EXISTS idx_document_sections_tsv ON document_sections USING GIN(content_tsv);

-- Other indexes
CREATE INDEX IF NOT EXISTS idx_document_sections_embedded ON document_sections(is_embedded);
CREATE INDEX IF NOT EXISTS idx_document_sections_language ON document_sections(language);
CREATE INDEX IF NOT EXISTS idx_document_sections_content_hash ON document_sections(content_hash);
CREATE INDEX IF NOT EXISTS idx_document_sections_status ON document_sections(embedding_status);
"""
HTML Crawler with aiohttp for simple pages
Part of hybrid crawling approach (aiohttp for simple, Scrapy for complex)
"""
import asyncio
import aiohttp
import logging
import random
from typing import Dict, Any, List, Optional
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from config.settings import settings


class HTMLCrawler:
    """
    Asynchronous HTML crawler using aiohttp
    Suitable for simple HTML pages without complex JavaScript
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.config = config or {}
        
        # Crawler settings
        self.concurrent_requests = self.config.get('concurrent_requests', settings.crawler_concurrent_requests)
        self.delay_min = self.config.get('delay_min', settings.crawler_delay_min)
        self.delay_max = self.config.get('delay_max', settings.crawler_delay_max)
        self.timeout = self.config.get('timeout', 30)
        
        # User agents for rotation
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        ]
        
        self.session = None
        self.semaphore = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.semaphore = asyncio.Semaphore(self.concurrent_requests)
        
        # Create session with timeout and headers
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        self.session = aiohttp.ClientSession(
            timeout=timeout,
            headers={'User-Agent': self.get_random_user_agent()},
            connector=aiohttp.TCPConnector(limit=self.concurrent_requests)
        )
        
        self.logger.info(f"HTMLCrawler initialized with {self.concurrent_requests} concurrent requests")
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    def get_random_user_agent(self) -> str:
        """Get random user agent for request rotation"""
        return random.choice(self.user_agents)
    
    async def crawl_url(self, url: str) -> Dict[str, Any]:
        """
        Crawl single URL and extract content
        Returns structured result with content and metadata
        """
        
        async with self.semaphore:
            try:
                # Random delay to be respectful
                delay = random.uniform(self.delay_min, self.delay_max)
                await asyncio.sleep(delay)
                
                self.logger.info(f"Crawling URL: {url}")
                
                async with self.session.get(url) as response:
                    if response.status == 200:
                        html_content = await response.text()
                        
                        # Parse with BeautifulSoup
                        soup = BeautifulSoup(html_content, 'html.parser')
                        
                        # Extract structured data
                        result = self._extract_content(soup, url, response)
                        result['success'] = True
                        result['status_code'] = response.status
                        
                        self.logger.info(f"Successfully crawled {url} - {len(result['content'])} chars")
                        return result
                    
                    else:
                        self.logger.warning(f"HTTP {response.status} for {url}")
                        return {
                            'success': False,
                            'url': url,
                            'status_code': response.status,
                            'error': f'HTTP {response.status}',
                            'content': '',
                            'title': '',
                            'links': []
                        }
            
            except asyncio.TimeoutError:
                self.logger.error(f"Timeout crawling {url}")
                return self._error_result(url, "Timeout")
            
            except Exception as e:
                self.logger.error(f"Error crawling {url}: {e}")
                return self._error_result(url, str(e))
    
    def _extract_content(self, soup: BeautifulSoup, url: str, response) -> Dict[str, Any]:
        """Extract structured content from BeautifulSoup object"""
        
        # Remove script and style elements
        for script in soup(["script", "style", "nav", "footer", "header"]):
            script.decompose()
        
        # Extract title
        title = ""
        if soup.title:
            title = soup.title.string.strip() if soup.title.string else ""
        
        # Extract main content
        content = ""
        
        # Try to find main content areas
        main_selectors = [
            'main',
            'article', 
            '.content',
            '.main-content',
            '#content',
            '#main',
            '.post-content',
            '.entry-content'
        ]
        
        main_content = None
        for selector in main_selectors:
            main_content = soup.select_one(selector)
            if main_content:
                break
        
        if main_content:
            content = main_content.get_text(separator='\n', strip=True)
        else:
            # Fallback to body content
            if soup.body:
                content = soup.body.get_text(separator='\n', strip=True)
            else:
                content = soup.get_text(separator='\n', strip=True)
        
        # Extract links
        links = []
        for link in soup.find_all('a', href=True):
            href = link.get('href')
            if href:
                absolute_url = urljoin(url, href)
                link_text = link.get_text(strip=True)
                
                # Filter out obvious navigation/footer links
                if self._is_content_link(link_text, absolute_url):
                    links.append({
                        'url': absolute_url,
                        'text': link_text,
                        'title': link.get('title', '')
                    })
        
        # Extract metadata
        metadata = {
            'title': title,
            'url': url,
            'content_length': len(content),
            'links_count': len(links),
            'domain': urlparse(url).netloc,
            'crawl_timestamp': response.headers.get('Date', ''),
            'content_type': response.headers.get('Content-Type', ''),
            'language': self._detect_language(soup)
        }
        
        return {
            'url': url,
            'title': title,
            'content': content,
            'html_content': str(soup),
            'links': links[:50],  # Limit links to prevent bloat
            'metadata': metadata
        }
    
    def _is_content_link(self, link_text: str, url: str) -> bool:
        """Filter out navigation and footer links"""
        
        # Skip empty or very short link text
        if not link_text or len(link_text.strip()) < 3:
            return False
        
        # Skip common navigation terms
        nav_terms = [
            'home', 'about', 'contact', 'privacy', 'terms', 'login', 'register',
            'menu', 'navigation', 'footer', 'header', 'sidebar', 'search',
            'previous', 'next', 'back', 'top', 'skip'
        ]
        
        link_lower = link_text.lower().strip()
        if any(term in link_lower for term in nav_terms):
            return False
        
        # Skip social media and external service links
        external_domains = [
            'facebook.com', 'twitter.com', 'linkedin.com', 'instagram.com',
            'youtube.com', 'google.com', 'mailto:', 'tel:'
        ]
        
        if any(domain in url.lower() for domain in external_domains):
            return False
        
        return True
    
    def _detect_language(self, soup: BeautifulSoup) -> str:
        """Detect page language from HTML attributes"""
        
        # Check html lang attribute
        html_tag = soup.find('html')
        if html_tag and html_tag.get('lang'):
            return html_tag.get('lang')[:2]  # Get language code
        
        # Check meta tags
        lang_meta = soup.find('meta', attrs={'http-equiv': 'Content-Language'})
        if lang_meta and lang_meta.get('content'):
            return lang_meta.get('content')[:2]
        
        # Default to unknown
        return 'unknown'
    
    def _error_result(self, url: str, error: str) -> Dict[str, Any]:
        """Create standardized error result"""
        return {
            'success': False,
            'url': url,
            'status_code': 0,
            'error': error,
            'content': '',
            'title': '',
            'links': [],
            'metadata': {'error': error}
        }
    
    async def crawl_multiple_urls(self, urls: List[str]) -> List[Dict[str, Any]]:
        """
        Crawl multiple URLs concurrently
        Returns list of results in same order as input URLs
        """
        
        self.logger.info(f"Starting concurrent crawl of {len(urls)} URLs")
        
        # Create tasks for all URLs
        tasks = [self.crawl_url(url) for url in urls]
        
        # Execute with concurrency control (semaphore handles this)
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle any exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"Exception crawling {urls[i]}: {result}")
                processed_results.append(self._error_result(urls[i], str(result)))
            else:
                processed_results.append(result)
        
        successful = sum(1 for r in processed_results if r.get('success', False))
        self.logger.info(f"Crawl completed: {successful}/{len(urls)} successful")
        
        return processed_results
    
    async def crawl_sitemap(self, sitemap_url: str, max_urls: int = 100) -> List[Dict[str, Any]]:
        """
        Crawl URLs from sitemap
        """
        
        try:
            self.logger.info(f"Fetching sitemap: {sitemap_url}")
            
            async with self.session.get(sitemap_url) as response:
                if response.status != 200:
                    self.logger.error(f"Failed to fetch sitemap: HTTP {response.status}")
                    return []
                
                sitemap_content = await response.text()
                soup = BeautifulSoup(sitemap_content, 'xml')
                
                # Extract URLs from sitemap
                urls = []
                for loc in soup.find_all('loc'):
                    if loc.string:
                        urls.append(loc.string.strip())
                        if len(urls) >= max_urls:
                            break
                
                self.logger.info(f"Found {len(urls)} URLs in sitemap")
                
                # Crawl the URLs
                return await self.crawl_multiple_urls(urls)
        
        except Exception as e:
            self.logger.error(f"Error processing sitemap {sitemap_url}: {e}")
            return []

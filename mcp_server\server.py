"""
FastAPI MCP Server for RAG System
Provides search endpoints and health monitoring
"""
import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from fastapi import Fast<PERSON><PERSON>, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

from database.manager import DatabaseManager
from embeddings.processor import EmbeddingProcessor
from workflows.rag_pipeline import fully_idempotent_rag_pipeline
from config.settings import settings


# Configure logging
logging.basicConfig(level=getattr(logging, settings.log_level))
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="RAG System MCP Server",
    description="Model Context Protocol server for RAG system with vector search",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global instances
db_manager = DatabaseManager()
embedding_processor = EmbeddingProcessor()


# Pydantic models
class SearchRequest(BaseModel):
    query: str = Field(..., description="Search query text")
    limit: int = Field(default=10, ge=1, le=50, description="Maximum number of results")
    similarity_threshold: float = Field(default=0.7, ge=0.0, le=1.0, description="Minimum similarity threshold")
    include_metadata: bool = Field(default=True, description="Include metadata in results")


class SearchResult(BaseModel):
    id: str
    content: str
    similarity: float
    metadata: Optional[Dict[str, Any]] = None


class SearchResponse(BaseModel):
    query: str
    results: List[SearchResult]
    total_results: int
    processing_time_ms: float
    timestamp: str


class CrawlRequest(BaseModel):
    site_configs: List[Dict[str, Any]] = Field(..., description="Site configurations for crawling")


class HealthResponse(BaseModel):
    status: str
    timestamp: str
    database: Dict[str, Any]
    embedding_cache: Dict[str, Any]
    system: Dict[str, Any]


# Startup and shutdown events
@app.on_event("startup")
async def startup_event():
    """Initialize connections on startup"""
    logger.info("Starting RAG MCP Server...")
    
    # Test database connection
    connection_ok = await db_manager.test_connection()
    if not connection_ok:
        logger.error("Database connection failed!")
        raise Exception("Database connection failed")
    
    # Initialize Redis for embedding cache
    await embedding_processor.initialize_redis()
    
    logger.info("RAG MCP Server started successfully")


@app.on_event("shutdown")
async def shutdown_event():
    """Clean up connections on shutdown"""
    logger.info("Shutting down RAG MCP Server...")
    await embedding_processor.close()
    logger.info("RAG MCP Server shutdown complete")


# Health check endpoint
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Comprehensive health check endpoint"""
    
    try:
        # Database health
        db_health = await db_manager.get_health_status()
        
        # Embedding cache health
        cache_health = await embedding_processor.get_cache_stats()
        
        # System health
        import psutil
        system_health = {
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent
        }
        
        # Overall status
        overall_status = "healthy"
        if db_health.get('status') != 'healthy':
            overall_status = "unhealthy"
        elif system_health['memory_percent'] > 90 or system_health['cpu_percent'] > 90:
            overall_status = "degraded"
        
        return HealthResponse(
            status=overall_status,
            timestamp=datetime.now().isoformat(),
            database=db_health,
            embedding_cache=cache_health,
            system=system_health
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")


# Search endpoint
@app.post("/search", response_model=SearchResponse)
async def search_documents(request: SearchRequest):
    """
    Hybrid search endpoint combining vector similarity and text search
    """
    
    start_time = datetime.now()
    
    try:
        logger.info(f"Search request: '{request.query}' (limit: {request.limit})")
        
        # Generate query embedding
        query_embedding = await embedding_processor.generate_embedding(request.query)
        
        if not query_embedding:
            raise HTTPException(status_code=500, detail="Failed to generate query embedding")
        
        # Perform hybrid search
        search_results = await db_manager.hybrid_search(
            query=request.query,
            query_embedding=query_embedding,
            limit=request.limit,
            similarity_threshold=request.similarity_threshold
        )
        
        # Format results
        formatted_results = []
        for result in search_results:
            search_result = SearchResult(
                id=result['id'],
                content=result['content'],
                similarity=result.get('combined_score', result.get('similarity', 0.0))
            )
            
            if request.include_metadata:
                search_result.metadata = result.get('metadata', {})
            
            formatted_results.append(search_result)
        
        # Calculate processing time
        processing_time = (datetime.now() - start_time).total_seconds() * 1000
        
        response = SearchResponse(
            query=request.query,
            results=formatted_results,
            total_results=len(formatted_results),
            processing_time_ms=processing_time,
            timestamp=datetime.now().isoformat()
        )
        
        logger.info(f"Search completed: {len(formatted_results)} results in {processing_time:.2f}ms")
        return response
        
    except Exception as e:
        logger.error(f"Search failed: {e}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


# Crawl endpoint
@app.post("/crawl")
async def trigger_crawl(request: CrawlRequest, background_tasks: BackgroundTasks):
    """
    Trigger crawling and processing pipeline
    Runs in background to avoid timeout
    """
    
    try:
        logger.info(f"Crawl request received for {len(request.site_configs)} sites")
        
        # Add pipeline to background tasks
        background_tasks.add_task(
            run_pipeline_background,
            request.site_configs
        )
        
        return {
            "status": "accepted",
            "message": "Crawl pipeline started in background",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to start crawl: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start crawl: {str(e)}")


async def run_pipeline_background(site_configs: List[Dict[str, Any]]):
    """Run the RAG pipeline in background"""
    try:
        logger.info("Starting background RAG pipeline")
        result = await fully_idempotent_rag_pipeline(site_configs)
        logger.info(f"Background pipeline completed: {result}")
    except Exception as e:
        logger.error(f"Background pipeline failed: {e}")


# Statistics endpoint
@app.get("/stats")
async def get_statistics():
    """Get system statistics"""
    
    try:
        db_health = await db_manager.get_health_status()
        cache_stats = await embedding_processor.get_cache_stats()
        
        return {
            "database": {
                "total_pages": db_health.get('raw_pages_total', 0),
                "total_sections": db_health.get('sections_total', 0),
                "pending_pages": db_health.get('pending_pages', 0),
                "pending_embeddings": db_health.get('pending_embeddings', 0)
            },
            "cache": cache_stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get statistics: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get statistics: {str(e)}")


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "name": "RAG System MCP Server",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "search": "/search",
            "crawl": "/crawl",
            "health": "/health",
            "stats": "/stats"
        },
        "timestamp": datetime.now().isoformat()
    }


# Error handlers
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}")
    return HTTPException(status_code=500, detail="Internal server error")


if __name__ == "__main__":
    # Run the server
    uvicorn.run(
        "mcp_server.server:app",
        host=settings.server_host,
        port=settings.server_port,
        log_level=settings.log_level.lower(),
        reload=False
    )

"""
Database tests for RAG system
Tests connection, UPSERT operations, and vector search
"""
import pytest
import asyncio
import os
from datetime import datetime
from database.manager import DatabaseManager
from config.settings import settings


@pytest.fixture
async def db_manager():
    """Database manager fixture"""
    manager = DatabaseManager()
    
    # Test connection
    connection_ok = await manager.test_connection()
    if not connection_ok:
        pytest.skip("Database connection failed")
    
    yield manager


@pytest.mark.asyncio
async def test_database_connection(db_manager):
    """Test basic database connection"""
    result = await db_manager.test_connection()
    assert result is True


@pytest.mark.asyncio
async def test_upsert_raw_page(db_manager):
    """Test UPSERT functionality for raw pages"""
    
    # Test data
    test_url = "https://test.example.com/page1"
    test_html = "<html><body>Test content</body></html>"
    test_metadata = {"title": "Test Page", "test": True}
    
    # First insert
    page_id1 = await db_manager.upsert_raw_page(
        url=test_url,
        html_content=test_html,
        metadata=test_metadata
    )
    
    assert page_id1 is not None
    
    # Second insert with same content (should update, not create new)
    page_id2 = await db_manager.upsert_raw_page(
        url=test_url,
        html_content=test_html,
        metadata=test_metadata
    )
    
    # Should return same ID (UPSERT behavior)
    assert page_id1 == page_id2


@pytest.mark.asyncio
async def test_page_status_updates(db_manager):
    """Test page status update functionality"""
    
    # Create test page
    test_url = "https://test.example.com/status-test"
    test_html = "<html><body>Status test</body></html>"
    
    page_id = await db_manager.upsert_raw_page(
        url=test_url,
        html_content=test_html
    )
    
    assert page_id is not None
    
    # Test status updates
    result1 = await db_manager.update_page_status(page_id, 'processing')
    assert result1 is True
    
    result2 = await db_manager.update_page_status(page_id, 'completed')
    assert result2 is True
    
    result3 = await db_manager.update_page_status(page_id, 'failed', error='Test error')
    assert result3 is True


@pytest.mark.asyncio
async def test_document_section_operations(db_manager):
    """Test document section insert and update operations"""
    
    # Create test page first
    test_url = "https://test.example.com/section-test"
    test_html = "<html><body>Section test content</body></html>"
    
    page_id = await db_manager.upsert_raw_page(
        url=test_url,
        html_content=test_html
    )
    
    # Insert document section
    section_data = {
        'raw_page_id': page_id,
        'content': 'This is test content for the section.',
        'section_index': 0,
        'section_type': 'text_chunk',
        'quality_score': 0.85,
        'metadata': {'test': True}
    }
    
    section_id = await db_manager.insert_document_section(section_data)
    assert section_id is not None
    
    # Test embedding update
    test_embedding = [0.1] * 1536  # Mock embedding vector
    
    result = await db_manager.update_section_embedding(
        section_id=section_id,
        embedding=test_embedding,
        status='completed'
    )
    
    assert result is True


@pytest.mark.asyncio
async def test_get_pending_operations(db_manager):
    """Test getting pending pages and sections"""
    
    # Get pending pages
    pending_pages = await db_manager.get_pending_pages(limit=5)
    assert isinstance(pending_pages, list)
    
    # Get sections for embedding
    pending_sections = await db_manager.get_sections_for_embedding(limit=5)
    assert isinstance(pending_sections, list)


@pytest.mark.asyncio
async def test_health_status(db_manager):
    """Test health status endpoint"""
    
    health = await db_manager.get_health_status()
    
    assert isinstance(health, dict)
    assert 'status' in health
    assert 'timestamp' in health
    assert health['status'] in ['healthy', 'unhealthy']


@pytest.mark.asyncio
async def test_vector_search_function(db_manager):
    """Test vector search functionality"""
    
    # This test requires actual embeddings in the database
    # For now, just test that the function doesn't crash
    
    test_embedding = [0.1] * 1536
    
    try:
        results = await db_manager.hybrid_search(
            query="test query",
            query_embedding=test_embedding,
            limit=5,
            similarity_threshold=0.5
        )
        
        assert isinstance(results, list)
        
    except Exception as e:
        # Vector search might fail if no embeddings exist
        # This is expected in a fresh test database
        assert "no embeddings" in str(e).lower() or "function" in str(e).lower()


@pytest.mark.asyncio
async def test_database_singleton_pattern():
    """Test that DatabaseManager follows singleton pattern"""
    
    manager1 = DatabaseManager()
    manager2 = DatabaseManager()
    
    # Should be the same instance
    assert manager1 is manager2
    assert id(manager1) == id(manager2)


def test_database_configuration():
    """Test database configuration"""
    
    # Test that required environment variables are set
    assert settings.supabase_url is not None
    assert settings.supabase_service_key is not None
    
    # Test URL format
    assert settings.supabase_url.startswith('https://')
    assert '.supabase.co' in settings.supabase_url


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])

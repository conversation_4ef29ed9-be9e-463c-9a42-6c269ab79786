"""
Database Manager with <PERSON><PERSON> for Connection Pooling
CRITICAL: Implements singleton to prevent connection pool exhaustion
"""
import threading
import logging
import hashlib
from typing import Optional, Dict, Any, List
from datetime import datetime
from supabase import create_client, Client
from config.settings import settings


class DatabaseManager:
    """
    Singleton Database Manager to prevent connection pool exhaustion
    CRITICAL: Only one instance per process to manage Supabase connections
    """
    _instance: Optional['DatabaseManager'] = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.logger = logging.getLogger(self.__class__.__name__)
            
            # Initialize Supabase client
            self.client: Client = create_client(
                settings.supabase_url,
                settings.supabase_service_key
            )
            
            self.logger.info("DatabaseManager initialized as singleton")
            self.initialized = True
    
    @classmethod
    def get_client(cls) -> Client:
        """Get the singleton Supabase client"""
        instance = cls()
        return instance.client
    
    async def test_connection(self) -> bool:
        """Test database connection"""
        try:
            result = self.client.table('raw_pages').select('id').limit(1).execute()
            self.logger.info("Database connection test successful")
            return True
        except Exception as e:
            self.logger.error(f"Database connection test failed: {e}")
            return False
    
    async def upsert_raw_page(self, url: str, html_content: str, metadata: Dict[str, Any] = None) -> Optional[str]:
        """
        UPSERT raw page with idempotency
        Returns page ID if successful, None if failed
        """
        try:
            # Generate content hash for deduplication
            content_hash = hashlib.sha256(f"{url}{html_content}".encode()).hexdigest()
            
            data = {
                'url': url,
                'html_content': html_content,
                'metadata': metadata or {},
                'content_hash': content_hash,
                'processing_status': 'pending',
                'fetch_time': datetime.now().isoformat()
            }
            
            # UPSERT operation
            result = self.client.table('raw_pages') \
                .upsert(data, on_conflict='content_hash') \
                .execute()
            
            if result.data:
                page_id = result.data[0]['id']
                self.logger.info(f"Upserted raw page {url} with ID {page_id}")
                return page_id
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to upsert raw page {url}: {e}")
            return None
    
    async def get_pending_pages(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get pages pending processing"""
        try:
            result = self.client.table('raw_pages') \
                .select('*') \
                .eq('processing_status', 'pending') \
                .limit(limit) \
                .execute()
            
            return result.data or []
            
        except Exception as e:
            self.logger.error(f"Failed to get pending pages: {e}")
            return []
    
    async def update_page_status(self, page_id: str, status: str, error: str = None) -> bool:
        """Update page processing status"""
        try:
            update_data = {
                'processing_status': status,
                'updated_at': datetime.now().isoformat()
            }
            
            if status == 'processing':
                # Increment attempts counter
                page_result = self.client.table('raw_pages') \
                    .select('processing_attempts') \
                    .eq('id', page_id) \
                    .execute()
                
                if page_result.data:
                    current_attempts = page_result.data[0].get('processing_attempts', 0)
                    update_data['processing_attempts'] = current_attempts + 1
            
            elif status == 'completed':
                update_data['processed_at'] = datetime.now().isoformat()
            
            elif status == 'failed':
                update_data['last_error'] = error
            
            result = self.client.table('raw_pages') \
                .update(update_data) \
                .eq('id', page_id) \
                .execute()
            
            return bool(result.data)
            
        except Exception as e:
            self.logger.error(f"Failed to update page status {page_id}: {e}")
            return False
    
    async def insert_document_section(self, section_data: Dict[str, Any]) -> Optional[str]:
        """Insert document section with quality score"""
        try:
            # Generate content hash
            content_hash = hashlib.sha256(section_data['content'].encode()).hexdigest()
            
            data = {
                **section_data,
                'content_hash': content_hash,
                'embedding_status': 'pending',
                'created_at': datetime.now().isoformat()
            }
            
            result = self.client.table('document_sections') \
                .insert(data) \
                .execute()
            
            if result.data:
                section_id = result.data[0]['id']
                self.logger.info(f"Inserted document section with ID {section_id}")
                return section_id
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to insert document section: {e}")
            return None
    
    async def get_sections_for_embedding(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get sections that need embeddings"""
        try:
            result = self.client.table('document_sections') \
                .select('*') \
                .eq('embedding_status', 'pending') \
                .limit(limit) \
                .execute()
            
            return result.data or []
            
        except Exception as e:
            self.logger.error(f"Failed to get sections for embedding: {e}")
            return []
    
    async def update_section_embedding(self, section_id: str, embedding: List[float], 
                                     status: str = 'completed') -> bool:
        """Update section with embedding vector"""
        try:
            update_data = {
                'embedding': embedding,
                'embedding_status': status,
                'is_embedded': True,
                'updated_at': datetime.now().isoformat()
            }
            
            result = self.client.table('document_sections') \
                .update(update_data) \
                .eq('id', section_id) \
                .execute()
            
            return bool(result.data)
            
        except Exception as e:
            self.logger.error(f"Failed to update section embedding {section_id}: {e}")
            return False
    
    async def hybrid_search(self, query: str, query_embedding: List[float], 
                          limit: int = 10, similarity_threshold: float = 0.7) -> List[Dict[str, Any]]:
        """
        Hybrid search combining vector similarity and text search
        """
        try:
            # Vector similarity search (70% weight)
            vector_results = self.client.rpc('match_documents', {
                'query_embedding': query_embedding,
                'match_threshold': similarity_threshold,
                'match_count': limit * 2  # Get more for reranking
            }).execute()
            
            # Text search using tsvector (30% weight)
            text_results = self.client.table('document_sections') \
                .select('*, ts_rank(content_tsv, plainto_tsquery($1)) as text_rank') \
                .text_search('content_tsv', query) \
                .limit(limit * 2) \
                .execute()
            
            # Combine and rerank results
            combined_results = self._combine_search_results(
                vector_results.data or [],
                text_results.data or [],
                vector_weight=0.7,
                text_weight=0.3
            )
            
            return combined_results[:limit]
            
        except Exception as e:
            self.logger.error(f"Hybrid search failed: {e}")
            return []
    
    def _combine_search_results(self, vector_results: List[Dict], text_results: List[Dict],
                              vector_weight: float = 0.7, text_weight: float = 0.3) -> List[Dict]:
        """Combine and rerank search results"""
        
        # Create lookup for text scores
        text_scores = {result['id']: result.get('text_rank', 0) for result in text_results}
        
        # Combine scores
        for result in vector_results:
            vector_score = result.get('similarity', 0)
            text_score = text_scores.get(result['id'], 0)
            
            # Combined score
            result['combined_score'] = (vector_score * vector_weight) + (text_score * text_weight)
        
        # Sort by combined score
        return sorted(vector_results, key=lambda x: x.get('combined_score', 0), reverse=True)
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get database health status"""
        try:
            # Test basic connectivity
            connection_test = await self.test_connection()
            
            # Get table counts
            raw_pages_count = self.client.table('raw_pages').select('id', count='exact').execute()
            sections_count = self.client.table('document_sections').select('id', count='exact').execute()
            
            # Get processing status
            pending_pages = self.client.table('raw_pages') \
                .select('id', count='exact') \
                .eq('processing_status', 'pending') \
                .execute()
            
            pending_embeddings = self.client.table('document_sections') \
                .select('id', count='exact') \
                .eq('embedding_status', 'pending') \
                .execute()
            
            return {
                'status': 'healthy' if connection_test else 'unhealthy',
                'connection': connection_test,
                'raw_pages_total': raw_pages_count.count,
                'sections_total': sections_count.count,
                'pending_pages': pending_pages.count,
                'pending_embeddings': pending_embeddings.count,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }


# SQL functions for vector search
VECTOR_SEARCH_FUNCTIONS = """
-- Function for vector similarity search
CREATE OR REPLACE FUNCTION match_documents(
    query_embedding vector(1536),
    match_threshold float,
    match_count int
)
RETURNS TABLE (
    id uuid,
    content text,
    similarity float,
    metadata jsonb
)
LANGUAGE sql STABLE
AS $$
    SELECT
        document_sections.id,
        document_sections.content,
        1 - (document_sections.embedding <=> query_embedding) as similarity,
        document_sections.metadata
    FROM document_sections
    WHERE 1 - (document_sections.embedding <=> query_embedding) > match_threshold
    ORDER BY document_sections.embedding <=> query_embedding
    LIMIT match_count;
$$;

-- Function for UPSERT raw pages
CREATE OR REPLACE FUNCTION upsert_raw_page(
    p_url text,
    p_html_content text,
    p_metadata jsonb DEFAULT '{}'::jsonb
) RETURNS uuid AS $$
DECLARE
    page_id uuid;
    content_hash text;
BEGIN
    -- Generate content hash
    content_hash := encode(sha256((p_url || p_html_content)::bytea), 'hex');

    INSERT INTO raw_pages (url, html_content, metadata, content_hash, processing_status)
    VALUES (p_url, p_html_content, p_metadata, content_hash, 'pending')
    ON CONFLICT (content_hash)
    DO UPDATE SET
        html_content = EXCLUDED.html_content,
        metadata = EXCLUDED.metadata,
        updated_at = NOW()
    RETURNING id INTO page_id;

    RETURN page_id;
END;
$$ LANGUAGE plpgsql;
"""

"""
Idempotent RAG Pipeline using Prefect
Implements fully idempotent ETL process with state tracking
"""
import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from prefect import flow, task
from prefect.task_runners import ConcurrentTaskRunner

from database.manager import DatabaseManager
from crawlers.html_crawler import HTMLCrawler
from processors.quality_validator import ComprehensiveDataQualityValidator
from processors.chunking import ConfigurableChunker
from embeddings.processor import EmbeddingProcessor
from config.settings import settings


# Configure logging
logging.basicConfig(level=getattr(logging, settings.log_level))
logger = logging.getLogger(__name__)


@task(retries=3, retry_delay_seconds=60)
async def idempotent_crawl_websites(site_configs: List[Dict]) -> List[str]:
    """
    Idempotent website crawling that returns IDs of new pages
    CRITICAL: Uses UPSERT to prevent duplicates
    """
    logger.info(f"Starting idempotent crawl of {len(site_configs)} site configurations")
    
    db_manager = DatabaseManager()
    new_page_ids = []
    
    for config in site_configs:
        site_name = config.get('name', 'unknown')
        start_urls = config.get('start_urls', [])
        
        logger.info(f"Crawling site: {site_name} with {len(start_urls)} URLs")
        
        # Use HTML crawler for simple pages
        async with HTMLCrawler(config) as crawler:
            results = await crawler.crawl_multiple_urls(start_urls)
            
            for result in results:
                if result.get('success'):
                    try:
                        # UPSERT page to prevent duplicates
                        page_id = await db_manager.upsert_raw_page(
                            url=result['url'],
                            html_content=result.get('html_content', ''),
                            metadata={
                                'title': result.get('title', ''),
                                'content_length': len(result.get('content', '')),
                                'links_count': len(result.get('links', [])),
                                'site_name': site_name,
                                'crawl_timestamp': datetime.now().isoformat(),
                                'crawler_type': 'html_crawler'
                            }
                        )
                        
                        if page_id:
                            new_page_ids.append(page_id)
                            logger.info(f"Upserted page {result['url']} with ID {page_id}")
                    
                    except Exception as e:
                        logger.error(f"Failed to upsert page {result['url']}: {e}")
                        continue
    
    logger.info(f"Crawling completed: {len(new_page_ids)} pages upserted")
    return new_page_ids


@task(retries=2, retry_delay_seconds=30)
async def idempotent_process_pages(page_ids: List[str]) -> List[str]:
    """
    Process pages with quality validation and chunking
    Only processes pages that haven't been processed yet
    """
    logger.info(f"Processing {len(page_ids)} pages with quality validation")
    
    db_manager = DatabaseManager()
    validator = ComprehensiveDataQualityValidator()
    chunker = ConfigurableChunker()
    
    processed_section_ids = []
    
    for page_id in page_ids:
        try:
            # Get page only if not already processed
            pages = await db_manager.get_pending_pages(limit=1)
            page = None
            
            for p in pages:
                if p['id'] == page_id:
                    page = p
                    break
            
            if not page:
                logger.info(f"Page {page_id} already processed or not found")
                continue
            
            # Mark as processing
            await db_manager.update_page_status(page_id, 'processing')
            
            # Extract clean text from HTML
            clean_text = extract_clean_text(page['html_content'])
            
            # Quality validation
            quality_result = validator.validate_content_quality(clean_text)
            
            if not quality_result['is_acceptable']:
                # Mark as rejected due to quality
                await db_manager.update_page_status(
                    page_id, 
                    'rejected_quality',
                    error=f"Quality score: {quality_result['quality_score']}, Failed gates: {quality_result['failed_gates']}"
                )
                logger.info(f"Page {page_id} rejected due to quality: {quality_result['quality_score']}")
                continue
            
            # Chunk the content
            chunks = chunker.chunk_document(clean_text)
            
            # Insert document sections
            for chunk in chunks:
                section_data = {
                    'raw_page_id': page_id,
                    'content': chunk['content'],
                    'section_index': chunk['chunk_index'],
                    'section_type': 'text_chunk',
                    'quality_score': quality_result['quality_score'],
                    'metadata': {
                        'chunk_size': chunk['chunk_size'],
                        'chunk_strategy': chunk['strategy'],
                        'chunk_quality': chunk['quality_score'],
                        'content_type': quality_result['content_type']
                    }
                }
                
                section_id = await db_manager.insert_document_section(section_data)
                if section_id:
                    processed_section_ids.append(section_id)
            
            # Mark page as completed
            await db_manager.update_page_status(page_id, 'completed')
            logger.info(f"Successfully processed page {page_id} into {len(chunks)} sections")
            
        except Exception as e:
            # Mark as failed
            await db_manager.update_page_status(page_id, 'failed', error=str(e))
            logger.error(f"Failed to process page {page_id}: {e}")
    
    logger.info(f"Page processing completed: {len(processed_section_ids)} sections created")
    return processed_section_ids


@task(retries=2, retry_delay_seconds=60)
async def idempotent_generate_embeddings(section_ids: List[str]) -> Dict[str, Any]:
    """
    Generate embeddings for document sections
    Only processes sections that don't have embeddings yet
    """
    logger.info(f"Generating embeddings for {len(section_ids)} sections")
    
    db_manager = DatabaseManager()
    embedding_processor = EmbeddingProcessor()
    
    try:
        # Initialize Redis connection
        await embedding_processor.initialize_redis()
        
        # Get sections that need embeddings
        sections_to_process = []
        for section_id in section_ids:
            sections = await db_manager.get_sections_for_embedding(limit=1)
            for section in sections:
                if section['id'] == section_id:
                    sections_to_process.append(section)
                    break
        
        if not sections_to_process:
            logger.info("No sections need embedding processing")
            return {'processed': 0, 'successful': 0, 'failed': 0}
        
        logger.info(f"Processing embeddings for {len(sections_to_process)} sections")
        
        # Process embeddings in batches
        processed_sections = await embedding_processor.process_document_sections(sections_to_process)
        
        # Update database with embeddings
        successful = 0
        failed = 0
        
        for section in processed_sections:
            try:
                if section.get('is_embedded'):
                    success = await db_manager.update_section_embedding(
                        section['id'],
                        section['embedding'],
                        'completed'
                    )
                    if success:
                        successful += 1
                    else:
                        failed += 1
                else:
                    # Mark as failed
                    await db_manager.update_section_embedding(
                        section['id'],
                        [],
                        'failed'
                    )
                    failed += 1
                    
            except Exception as e:
                logger.error(f"Failed to update embedding for section {section['id']}: {e}")
                failed += 1
        
        logger.info(f"Embedding processing completed: {successful} successful, {failed} failed")
        
        return {
            'processed': len(processed_sections),
            'successful': successful,
            'failed': failed
        }
        
    finally:
        await embedding_processor.close()


def extract_clean_text(html_content: str) -> str:
    """Extract clean text from HTML content"""
    from bs4 import BeautifulSoup
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Remove unwanted elements
    for element in soup(['script', 'style', 'nav', 'footer', 'header', 'aside']):
        element.decompose()
    
    # Get text content
    text = soup.get_text(separator='\n', strip=True)
    
    # Clean up whitespace
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    return '\n'.join(lines)


@flow(
    name="fully-idempotent-rag-pipeline",
    task_runner=ConcurrentTaskRunner(max_workers=5),  # CRITICAL: Limited workers for connection safety
    retries=1
)
async def fully_idempotent_rag_pipeline(site_configs: List[Dict]) -> Dict[str, Any]:
    """
    Fully idempotent RAG pipeline
    Can be run multiple times safely without creating duplicates
    """
    
    logger.info("Starting fully idempotent RAG pipeline")
    start_time = datetime.now()
    
    try:
        # Step 1: Crawl websites (returns IDs of new pages only)
        new_page_ids = await idempotent_crawl_websites(site_configs)
        
        if not new_page_ids:
            logger.info("No new pages to process")
            return {
                "status": "completed",
                "message": "No new data to process",
                "new_pages": 0,
                "new_sections": 0,
                "embeddings_processed": 0,
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
        
        # Step 2: Process pages with quality validation (only new pages)
        section_ids = await idempotent_process_pages(new_page_ids)
        
        # Step 3: Generate embeddings (only for new sections)
        embedding_results = {'processed': 0, 'successful': 0, 'failed': 0}
        if section_ids:
            embedding_results = await idempotent_generate_embeddings(section_ids)
        
        # Calculate duration
        duration = (datetime.now() - start_time).total_seconds()
        
        result = {
            "status": "completed",
            "new_pages": len(new_page_ids),
            "new_sections": len(section_ids),
            "embeddings_processed": embedding_results['processed'],
            "embeddings_successful": embedding_results['successful'],
            "embeddings_failed": embedding_results['failed'],
            "duration_seconds": duration,
            "timestamp": datetime.now().isoformat()
        }
        
        logger.info(f"Pipeline completed successfully: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Pipeline failed: {e}")
        return {
            "status": "failed",
            "error": str(e),
            "duration_seconds": (datetime.now() - start_time).total_seconds(),
            "timestamp": datetime.now().isoformat()
        }


# Example usage and configuration
EXAMPLE_SITE_CONFIGS = [
    {
        "name": "example_blog",
        "start_urls": [
            "https://example.com/blog",
            "https://example.com/articles"
        ],
        "concurrent_requests": 3,
        "delay_min": 1.0,
        "delay_max": 2.0
    },
    {
        "name": "documentation_site",
        "start_urls": [
            "https://docs.example.com/getting-started",
            "https://docs.example.com/api-reference"
        ],
        "concurrent_requests": 2,
        "delay_min": 0.5,
        "delay_max": 1.0
    }
]


if __name__ == "__main__":
    # Run the pipeline
    asyncio.run(fully_idempotent_rag_pipeline(EXAMPLE_SITE_CONFIGS))

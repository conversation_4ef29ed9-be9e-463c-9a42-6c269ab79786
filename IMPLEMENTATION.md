# План за имплементация на RAG система

## Архитектура

Системата използва трислойна медалистична архитектура:
- **Бронзов слой**: Сурови данни от краулинг
- **Сребърен слой**: Почистени и структурирани данни
- **Златен слой**: Индексирани документи с ембединги за RAG

## Технологичен стек

- **Краулинг**: aiohttp, Playwright
- **Бази данни**: Supabase (PostgreSQL с pgvector)
- **Ембединги**: OpenAI API (gpt-4o-mini)
- **Кеширане**: Redis
- **Мониторинг**: Prometheus, Grafana
- **Оркестрация**: pg_cron, Prefect

## Контролен списък за имплементация

### Незабавни действия:
- [ ] Сменете IVFFLAT с HNSW индекс
- [ ] Добавете proper error handling във всички async функции
- [ ] Имплементирайте Redis кеширане
- [ ] Добавете rate limiting
- [ ] Тествайте с малко количество данни първо

### Средносрочни подобрения:
- [ ] Добавете партициониране на таблици
- [ ] Имплементирайте health checks
- [ ] Добавете comprehensive logging
- [ ] Създайте Docker compose файл за development

### Дългосрочни цели:
- [ ] Добавете A/B тестване на различни embedding модели
- [ ] Имплементирайте auto-scaling за краулъри
- [ ] Добавете data lineage tracking
- [ ] Създайте dashboard за мониторинг

## Схема на базата данни

```sql
-- Бронзов слой
CREATE TABLE raw_pages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    url TEXT NOT NULL,
    html_content TEXT,
    fetch_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB
);

CREATE TABLE raw_pdfs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    url TEXT NOT NULL,
    file_data BYTEA,
    fetch_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB
);

-- Сребърен слой
CREATE TABLE clean_pages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    raw_page_id UUID REFERENCES raw_pages(id),
    content TEXT NOT NULL,
    language TEXT,
    content_hash TEXT UNIQUE,
    processed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Златен слой
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT,
    source TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE document_sections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id),
    content TEXT NOT NULL,
    embedding VECTOR(1536),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
) PARTITION BY RANGE (created_at);

-- Създаване на HNSW индекс
CREATE INDEX ON document_sections USING hnsw (embedding vector_cosine_ops);
```

## MCP сървър имплементация

```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import redis
import asyncio
from supabase import create_client
import os

app = FastAPI()

# Инициализация на Supabase клиент
supabase = create_client(
    os.getenv("SUPABASE_URL"),
    os.getenv("SUPABASE_SERVICE_KEY")
)

# Инициализация на Redis клиент
redis_client = redis.Redis(host='localhost', port=6379, db=0)

class SearchQuery(BaseModel):
    query: str
    limit: int = 5

@app.post("/search_documents")
async def search_documents(search_query: SearchQuery):
    # Проверка за кеширани резултати
    cache_key = f"search:{search_query.query}:{search_query.limit}"
    cached_result = redis_client.get(cache_key)
    
    if cached_result:
        return {"results": cached_result, "source": "cache"}
    
    # Изчисляване на ембединг за заявката
    # Търсене в базата данни
    # Кеширане на резултата
    
    return {"results": results, "source": "database"}

@app.get("/list_programs")
async def list_programs():
    # Връщане на списък с програми от базата данни
    return {"programs": programs}
```
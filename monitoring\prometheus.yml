global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # RAG System MCP Server metrics
  - job_name: 'rag-mcp-server'
    static_configs:
      - targets: ['rag_server:9090']
    scrape_interval: 10s
    metrics_path: /metrics

  # Redis metrics (if redis_exporter is used)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  # PostgreSQL metrics (if postgres_exporter is used)
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s

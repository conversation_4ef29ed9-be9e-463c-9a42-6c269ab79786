# Обединен план за имплементация на RAG система

## 🎯 Цел и обхват
Създаване на пълнофункционална RAG (Retrieval-Augmented Generation) система с MCP (Model Context Protocol) сървър за намаляване на халюцинациите на големи езикови модели чрез динамично извличане на релевантна информация от външна база знания.

## 🏗️ Архитектурен преглед

### Medallion архитектура (Bronze/Silver/Gold)
- **🥉 Бронзов слой**: Сурови данни от краулинг (HTML, PDF, метаданни)
- **🥈 Сребърен слой**: Обработени и почистени данни (текст, структурирано съдържание)
- **🥇 Златен слой**: Готови за използване данни с ембединги за RAG

### Основни компоненти
- **Краулинг система**: Асинхронна с Scrapy, Playwright, aiohttp
- **ETL процеси**: Обработка на текст, PDF и таблици с Pydantic валидация
- **Векторна база данни**: Supabase с pgvector и HNSW индекси
- **MCP сървър**: FastAPI имплементация с Prometheus метрики
- **Оркестрация**: Prefect за управление на работни потоци

## 📋 Фаза 1: Подготовка и структура

### Задача 1.1: Създаване на проектна структура

#### Директории
```
rag-system/
├── crawlers/           # Краулинг компоненти
│   ├── spiders/       # Scrapy spiders
│   ├── base_crawler.py
│   ├── js_crawler.py  # Playwright краулър
│   └── pdf_downloader.py
├── processors/         # ETL процеси
│   ├── base_processor.py
│   ├── html_processor.py
│   └── pdf_processor.py
├── database/          # SQL схеми и скриптове
│   ├── schema.sql
│   ├── functions.sql
│   └── init_db.py
├── embeddings/        # Ембединг генериране
│   └── embedder.py
├── mcp_server/        # MCP сървър
│   └── server.py
├── security/          # Криптиране и сигурност
│   └── crypto_manager.py
├── monitoring/        # Метрики и наблюдение
│   └── metrics.py
├── config/           # Конфигурационни файлове
│   ├── settings.py
│   ├── database.py
│   ├── crawlers.py
│   └── embeddings.py
├── tests/            # Тестове
├── docs/             # Документация
├── workflows/        # Prefect работни потоци
├── main.py          # Входна точка
├── requirements.txt # Python зависимости
├── docker-compose.yml
├── .env.example
├── .gitignore
└── README.md
```

#### Основни файлове
- `main.py` - входна точка на приложението
- `requirements.txt` - Python зависимости с конкретни версии
- `.gitignore` - игнориране на временни файлове
- `setup.py` - инсталация като пакет
- `docker-compose.yml` - контейнеризация
- `__init__.py` във всяка директория

#### Зависимости (requirements.txt)
```
# Основни
scrapy==2.8.0
supabase==1.0.3
fastapi==0.95.2
uvicorn==0.22.0
python-dotenv==1.0.0

# Обработка на данни
beautifulsoup4==4.12.2
pymupdf==1.22.3
langdetect==1.0.9
fasttext==0.9.2
pydantic==1.10.8

# Асинхронни операции
aiohttp==3.8.4
playwright==1.33.0
asyncio-throttle==1.0.2

# AI и ембединги
openai==0.27.8
pgvector==0.1.8

# Мониторинг и оркестрация
prometheus-client==0.16.0
prefect==2.10.8
tenacity==8.2.2

# Сигурност
cryptography==40.0.2

# Тестване
pytest==7.3.1
pytest-asyncio==0.21.0
```

### Задача 1.2: Настройка на Supabase

#### Създаване на проект
1. Регистрация в Supabase (https://supabase.com/)
2. Създаване на проект "rag-system"
3. Запазване на URL и API ключове в `.env`
4. Криптиране на чувствителни данни

#### Активиране на разширения
```sql
-- Векторни операции
CREATE EXTENSION IF NOT EXISTS vector;

-- Планирани задачи
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Статистики за производителност
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
```

#### Схема на базата данни
```sql
-- Бронзов слой (сурови данни)
CREATE TABLE raw_pages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  url TEXT NOT NULL,
  html_content TEXT,
  fetch_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB,
  is_processed BOOLEAN DEFAULT FALSE
);

CREATE TABLE raw_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  url TEXT NOT NULL,
  file_path TEXT,  -- Път към файла в Supabase Storage
  file_type TEXT,
  storage_bucket TEXT DEFAULT 'documents',
  fetch_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB,
  is_processed BOOLEAN DEFAULT FALSE
);

-- Сребърен слой (обработени данни)
CREATE TABLE clean_pages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  raw_page_id UUID REFERENCES raw_pages(id),
  content TEXT NOT NULL,
  language TEXT,
  content_hash TEXT UNIQUE,
  processed_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);

-- Златен слой (крайни данни)
CREATE TABLE documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT,
  source TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);

CREATE TABLE document_sections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID REFERENCES documents(id),
  content TEXT NOT NULL,
  section_index INTEGER,
  section_type VARCHAR(20), -- heading/paragraph/list/table
  embedding VECTOR(1536),
  embedding_model TEXT DEFAULT 'text-embedding-3-small',
  similarity_score FLOAT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 🔴 КРИТИЧНО: Използване на HNSW индекси
```sql
-- ❌ ПРОБЛЕМ: Остарял IVFFLAT индекс
-- CREATE INDEX idx_document_sections_embedding ON document_sections 
-- USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- ✅ РЕШЕНИЕ: Оптимизиран HNSW индекс за големи набори данни
CREATE INDEX idx_document_sections_embedding ON document_sections
USING hnsw (embedding vector_cosine_ops)
WITH (m = 32, ef_construction = 128, parallel_workers = 8);
```

## 📋 Фаза 2: Краулинг система

### Задача 2.1: Базов краулър клас

```python
from abc import ABC, abstractmethod
import logging
import time
import random
from prometheus_client import Counter, Gauge

# Метрики за наблюдение
PAGES_CRAWLED = Counter('pages_crawled_total', 'Total pages crawled', ['crawler_type', 'status'])
CRAWL_TIME = Gauge('crawl_time_seconds', 'Time taken to crawl', ['crawler_type'])

class BaseCrawler(ABC):
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        ]
        self.crawler_type = self.__class__.__name__
    
    def get_random_user_agent(self):
        return random.choice(self.user_agents)
    
    def add_delay(self):
        min_delay = self.config.get('min_delay', 1)
        max_delay = self.config.get('max_delay', 5)
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
    
    @abstractmethod
    def crawl(self):
        pass
    
    @abstractmethod
    def save_data(self, data):
        pass
    
    def run(self):
        self.logger.info(f"Starting crawling with {self.__class__.__name__}")
        start_time = time.time()
        
        try:
            data = self.crawl()
            self.save_data(data)
            PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status='success').inc()
        except Exception as e:
            self.logger.error(f"Error during crawling: {str(e)}")
            PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status='error').inc()
        
        end_time = time.time()
        CRAWL_TIME.labels(crawler_type=self.crawler_type).set(end_time - start_time)
```

### Задача 2.2: Асинхронни краулъри

#### Playwright краулър за JavaScript страници
- Асинхронна обработка с semaphore за ограничаване
- Retry логика с tenacity
- Prometheus метрики
- Проверка за дубликати

#### Асинхронен PDF даунлоудър
- aiohttp за бързо изтегляне
- Запазване в Supabase Storage
- Batch обработка с ограничение на concurrent заявки

#### Scrapy интегратор
- Twisted reactor интеграция
- Асинхронно изпълнение на множество spiders
- Конфигурируеми настройки за всеки сайт

## 📋 Фаза 3: ETL система

### Задача 3.1: Pydantic конфигурация

```python
from pydantic import BaseSettings, validator, Field
from typing import List, Optional

class CrawlerConfig(BaseSettings):
    start_urls: List[str]
    allowed_domains: List[str]
    download_delay: float = 2.0
    concurrent_requests: int = 8
    max_pages: Optional[int] = None
    
    @validator('concurrent_requests')
    def validate_concurrent_requests(cls, v):
        if v < 1 or v > 32:
            raise ValueError('concurrent_requests must be between 1 and 32')
        return v
    
    class Config:
        env_file = '.env'
        env_prefix = 'CRAWLER_'

class PDFProcessorConfig(BaseSettings):
    batch_size: int = Field(default=10, ge=1, le=100)
    extract_tables: bool = True
    extract_images: bool = False
    min_text_length: int = Field(default=50, ge=10)
    section_splitter: str = Field(default="blocks", regex="^(blocks|simple|layout)$")

class EmbeddingConfig(BaseSettings):
    model: str = "text-embedding-3-small"
    batch_size: int = Field(default=20, ge=1, le=100)
    dimensions: int = 1536
    api_key: str
    max_tokens_per_minute: int = Field(default=1000000, ge=10000)

    class Config:
        env_file = '.env'
        env_prefix = 'EMBEDDING_'

class DocumentSection(BaseModel):
    content: str
    section_index: int
    section_type: Literal["heading", "paragraph", "list", "table"]
    embedding: Optional[List[float]] = None
    similarity_score: Optional[float] = Field(None, ge=0, le=1)
    content_hash: Optional[str] = None

    @validator("content")
    def validate_content_length(cls, v):
        if len(v) < 50:
            raise ValueError("Section content too short")
        return v

    @validator("section_type")
    def validate_section_type(cls, v):
        allowed_types = ["heading", "paragraph", "list", "table", "image", "code"]
        if v not in allowed_types:
            raise ValueError(f"Section type must be one of {allowed_types}")
        return v
```

### Задача 3.2: Подобрен PDF процесор

#### 🔴 КРИТИЧНО: FastText модел проблем
```python
# ❌ ПРОБЛЕМ: FastText модел не е включен в зависимостите
# self.lang_model = fasttext.load_model('lid.176.ftz')  # Липсва модел

# ✅ РЕШЕНИЕ: Използване на langdetect или Azure/Google API
from langdetect import detect

def detect_language(self, text: str) -> str:
    try:
        return detect(text)
    except:
        return "unknown"
```

#### Оптимизиран PDF процесор
```python
import fitz  # PyMuPDF
import pandas as pd
from typing import List, Dict, Any
import hashlib

class OptimizedPDFProcessor:
    def __init__(self, config: PDFProcessorConfig):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)

    def process_pdf(self, file_path: str) -> List[Dict[str, Any]]:
        """Оптимизирана обработка на PDF с таблици и метаданни"""
        with fitz.open(file_path) as doc:
            sections = []

            for page_num, page in enumerate(doc):
                # Извличане на текст по блокове за по-добра структура
                if self.config.section_splitter == "blocks":
                    blocks = page.get_text("blocks")
                    for block_idx, block in enumerate(blocks):
                        text = block[4]  # Текстово съдържание

                        if len(text.strip()) >= self.config.min_text_length:
                            section = {
                                "content": text.strip(),
                                "section_index": page_num * 1000 + block_idx,
                                "section_type": "paragraph",
                                "page": page_num + 1,
                                "content_hash": hashlib.sha256(text.encode()).hexdigest(),
                                "metadata": {
                                    "bbox": block[:4],  # Координати на блока
                                    "font_size": block[5] if len(block) > 5 else None
                                }
                            }
                            sections.append(section)

                # Извличане на таблици с подобрена обработка
                if self.config.extract_tables:
                    tables = page.find_tables()
                    for table_idx, table in enumerate(tables):
                        try:
                            # Конвертиране в DataFrame за по-добра обработка
                            df = table.to_pandas()
                            table_markdown = df.to_markdown(index=False)

                            section = {
                                "content": table_markdown,
                                "section_index": page_num * 1000 + 500 + table_idx,
                                "section_type": "table",
                                "page": page_num + 1,
                                "content_hash": hashlib.sha256(table_markdown.encode()).hexdigest(),
                                "metadata": {
                                    "table_shape": df.shape,
                                    "columns": df.columns.tolist(),
                                    "bbox": table.bbox
                                }
                            }
                            sections.append(section)
                        except Exception as e:
                            self.logger.warning(f"Failed to process table on page {page_num}: {e}")

            return sections
```

#### Кеширане на ембединги
```python
import redis
import json
import hashlib

class EmbeddingCache:
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_client = redis.from_url(redis_url)
        self.cache_ttl = 86400 * 7  # 7 дни

    def get_embedding(self, content: str) -> Optional[List[float]]:
        """Получаване на кеширан ембединг"""
        content_hash = hashlib.sha256(content.encode()).hexdigest()
        cached = self.redis_client.get(f"embedding:{content_hash}")

        if cached:
            return json.loads(cached)
        return None

    def set_embedding(self, content: str, embedding: List[float]):
        """Запазване на ембединг в кеша"""
        content_hash = hashlib.sha256(content.encode()).hexdigest()
        self.redis_client.setex(
            f"embedding:{content_hash}",
            self.cache_ttl,
            json.dumps(embedding)
        )
```

### Задача 3.3: Оптимизиран Batch Embedding процесор

```python
import asyncio
import time
from openai import AsyncOpenAI
from prometheus_client import Counter, Histogram, Gauge
from typing import List, Dict, Any, Optional
import tenacity

# Метрики
EMBEDDINGS_GENERATED = Counter('embeddings_generated_total', 'Total embeddings', ['status'])
EMBEDDING_CACHE_HITS = Counter('embedding_cache_hits_total', 'Cache hits')
EMBEDDING_API_CALLS = Counter('embedding_api_calls_total', 'API calls')
TOKEN_USAGE = Counter('openai_tokens_used_total', 'OpenAI tokens used')

class OptimizedEmbeddingProcessor:
    def __init__(self, config: EmbeddingConfig):
        self.config = config
        self.client = AsyncOpenAI(api_key=config.api_key)
        self.cache = EmbeddingCache()
        self.batch_size = config.batch_size
        self.model = config.model
        self.rate_limiter = asyncio.Semaphore(10)  # Ограничение на concurrent заявки
        self.tokens_used_this_minute = 0
        self.minute_start = time.time()

    async def check_rate_limit(self, estimated_tokens: int):
        """Проверка и спазване на rate limits"""
        current_time = time.time()

        # Нулиране на брояча всяка минута
        if current_time - self.minute_start >= 60:
            self.tokens_used_this_minute = 0
            self.minute_start = current_time

        # Изчакване ако сме близо до лимита
        if self.tokens_used_this_minute + estimated_tokens > self.config.max_tokens_per_minute:
            wait_time = 60 - (current_time - self.minute_start)
            if wait_time > 0:
                await asyncio.sleep(wait_time)
                self.tokens_used_this_minute = 0
                self.minute_start = time.time()

    @tenacity.retry(
        stop=tenacity.stop_after_attempt(3),
        wait=tenacity.wait_exponential(multiplier=1, min=4, max=10),
        retry=tenacity.retry_if_exception_type(Exception)
    )
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Генериране на ембединги с rate limiting и retry логика"""
        async with self.rate_limiter:
            # Оценка на токените (приблизително 4 символа = 1 токен)
            estimated_tokens = sum(len(text) // 4 for text in texts)
            await self.check_rate_limit(estimated_tokens)

            try:
                response = await self.client.embeddings.create(
                    model=self.model,
                    input=texts
                )

                # Актуализиране на метриките
                actual_tokens = response.usage.total_tokens
                self.tokens_used_this_minute += actual_tokens
                TOKEN_USAGE.inc(actual_tokens)
                EMBEDDING_API_CALLS.inc()
                EMBEDDINGS_GENERATED.labels(status='success').inc(len(texts))

                return [item.embedding for item in response.data]

            except Exception as e:
                EMBEDDINGS_GENERATED.labels(status='error').inc(len(texts))
                raise

    async def process_batch_with_cache(self, sections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Обработка на batch с кеширане"""
        cached_sections = []
        uncached_sections = []
        uncached_indices = []

        # Проверка за кеширани ембединги
        for i, section in enumerate(sections):
            cached_embedding = self.cache.get_embedding(section['content'])
            if cached_embedding:
                section['embedding'] = cached_embedding
                section['embedding_model'] = self.model
                cached_sections.append(section)
                EMBEDDING_CACHE_HITS.inc()
            else:
                uncached_sections.append(section)
                uncached_indices.append(i)

        # Генериране на ембединги за некеширани секции
        if uncached_sections:
            texts = [section['content'] for section in uncached_sections]
            embeddings = await self.generate_embeddings(texts)

            for i, (section, embedding) in enumerate(zip(uncached_sections, embeddings)):
                section['embedding'] = embedding
                section['embedding_model'] = self.model

                # Запазване в кеша
                self.cache.set_embedding(section['content'], embedding)

        # Обединяване на резултатите
        all_sections = cached_sections + uncached_sections
        return all_sections
```

## 📋 Фаза 4: MCP сървър

### Задача 4.1: FastAPI имплементация

#### Основни компоненти
- Pydantic модели за валидация на данни
- CORS middleware за cross-origin заявки
- Prometheus метрики за мониторинг
- Rate limiting за защита от злоупотреба
- Кеширане на резултати с Redis

#### Endpoints
```python
from fastapi import FastAPI, HTTPException, Request
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional

class SearchQuery(BaseModel):
    query: str
    limit: int = Field(default=5, ge=1, le=20)
    threshold: float = Field(default=0.7, ge=0, le=1.0)

class SearchResult(BaseModel):
    content: str
    source: str
    similarity: float
    metadata: Dict[str, Any] = {}

@app.post("/search", response_model=List[SearchResult])
async def search_documents(query: SearchQuery):
    # Кеширане на резултати
    cache_key = f"search:{hash(query.query)}:{query.limit}:{query.threshold}"
    cached_result = redis_client.get(cache_key)
    if cached_result:
        SEARCH_CACHE_HITS.inc()
        return json.loads(cached_result)

    # Генериране на ембединг за заявката
    query_embedding = generate_embedding(query.query)

    # Hybrid търсене (векторно + текстово)
    response = supabase.rpc("hybrid_search_documents", {
        "query_embedding": query_embedding,
        "query_text": query.query,
        "match_threshold": query.threshold,
        "match_count": query.limit,
        "vector_weight": 0.7,
        "text_weight": 0.3
    }).execute()

    results = format_search_results(response.data)

    # Кеширане за 5 минути
    redis_client.setex(cache_key, 300, json.dumps([r.dict() for r in results]))
    return results

@app.post("/mcp", response_model=MCPResponse)
async def mcp_endpoint(request: MCPRequest):
    # Маршрутизиране на заявката към правилния инструмент
    if request.tool == "search_documents":
        query = SearchQuery(**request.params)
        result = await search_documents(query)
        return MCPResponse(result=result)
    else:
        return MCPResponse(result=None, error=f"Unknown tool: {request.tool}")
```

#### 🔴 КРИТИЧНО: Rate limiting, кеширане и hybrid търсене

```python
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
import redis
import json
from prometheus_client import Counter

# Метрики
SEARCH_REQUESTS = Counter('search_requests_total', 'Total search requests')
SEARCH_CACHE_HITS = Counter('search_cache_hits_total', 'Search cache hits')
RATE_LIMIT_EXCEEDED = Counter('rate_limit_exceeded_total', 'Rate limit exceeded')

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
redis_client = redis.Redis(host='localhost', port=6379, db=0)

# Hybrid search stored procedure
HYBRID_SEARCH_SQL = """
CREATE OR REPLACE FUNCTION hybrid_search_documents(
    query_embedding vector(1536),
    query_text text,
    match_threshold float,
    match_count int,
    vector_weight float DEFAULT 0.7,
    text_weight float DEFAULT 0.3
)
RETURNS TABLE (
    id uuid,
    content text,
    document_id uuid,
    section_type text,
    similarity float,
    combined_score float
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        ds.id,
        ds.content,
        ds.document_id,
        ds.section_type,
        1 - (ds.embedding <=> query_embedding) as similarity,
        (vector_weight * (1 - (ds.embedding <=> query_embedding))) +
        (text_weight * ts_rank(to_tsvector('english', ds.content), plainto_tsquery('english', query_text))) as combined_score
    FROM document_sections ds
    WHERE (1 - (ds.embedding <=> query_embedding)) > match_threshold
    ORDER BY combined_score DESC
    LIMIT match_count;
END;
$$ LANGUAGE plpgsql;
"""

@app.post("/search")
@limiter.limit("30/minute")
async def search_documents(request: Request, query: SearchQuery):
    SEARCH_REQUESTS.inc()

    try:
        # Кеширане на резултати
        cache_key = f"search:{hash(query.query)}:{query.limit}:{query.threshold}"
        cached_result = redis_client.get(cache_key)
        if cached_result:
            SEARCH_CACHE_HITS.inc()
            return json.loads(cached_result)

        # Генериране на ембединг за заявката
        query_embedding = generate_embedding(query.query)

        # Hybrid търсене
        response = supabase.rpc("hybrid_search_documents", {
            "query_embedding": query_embedding,
            "query_text": query.query,
            "match_threshold": query.threshold,
            "match_count": query.limit
        }).execute()

        results = format_search_results(response.data)

        # Кеширане за 5 минути
        redis_client.setex(cache_key, 300, json.dumps([r.dict() for r in results]))
        return results

    except Exception as e:
        logger.error(f"Search error: {str(e)}")
        raise HTTPException(status_code=500, detail="Search failed")

@app.exception_handler(429)
async def rate_limit_handler(request: Request, exc):
    RATE_LIMIT_EXCEEDED.inc()
    return JSONResponse(
        status_code=429,
        content={"detail": "Rate limit exceeded. Please try again later."}
    )
```

## 📋 Фаза 5: Оркестрация и мониторинг

### Задача 5.1: Оптимизирани Prefect работни потоци

```python
from prefect import flow, task, get_run_logger
from prefect.concurrency import RateLimit
from prefect.task_runners import ConcurrentTaskRunner
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any

@task(retries=3, retry_delay_seconds=60)
async def crawl_websites_batch(site_configs: List[Dict]) -> Dict[str, Any]:
    """Краулинг на batch от сайтове с rate limiting"""
    logger = get_run_logger()
    logger.info(f"Crawling {len(site_configs)} websites")

    crawler_manager = CrawlerManager(site_configs)
    results = await crawler_manager.run_all()

    return {
        "crawled_pages": len(results),
        "success_rate": sum(1 for r in results if r.get('status') == 'success') / len(results),
        "timestamp": datetime.now().isoformat()
    }

@task(retries=2, retry_delay_seconds=30)
async def process_pdfs_batch(pdf_batch: List[str]) -> Dict[str, Any]:
    """Обработка на batch от PDF файлове"""
    logger = get_run_logger()
    logger.info(f"Processing {len(pdf_batch)} PDF files")

    pdf_processor = OptimizedPDFProcessor(config)
    processed_sections = []

    for pdf_path in pdf_batch:
        try:
            sections = pdf_processor.process_pdf(pdf_path)
            processed_sections.extend(sections)
        except Exception as e:
            logger.error(f"Failed to process {pdf_path}: {e}")

    return {
        "processed_files": len(pdf_batch),
        "extracted_sections": len(processed_sections),
        "sections": processed_sections
    }

@task(retries=3, retry_delay_seconds=120)
async def generate_embeddings_batch(sections: List[Dict]) -> Dict[str, Any]:
    """Генериране на ембединги за batch от секции"""
    logger = get_run_logger()
    logger.info(f"Generating embeddings for {len(sections)} sections")

    embedding_processor = OptimizedEmbeddingProcessor(config)
    processed_sections = await embedding_processor.process_batch_with_cache(sections)

    return {
        "processed_sections": len(processed_sections),
        "cache_hit_rate": embedding_processor.cache_hit_rate,
        "api_calls_made": embedding_processor.api_calls_count
    }

@task
async def save_to_database(sections_with_embeddings: List[Dict]) -> Dict[str, Any]:
    """Запазване на секциите с ембединги в базата данни"""
    logger = get_run_logger()

    saved_count = 0
    for section in sections_with_embeddings:
        try:
            # Проверка за дубликати по content_hash
            existing = supabase.table('document_sections') \
                .select('id') \
                .eq('content_hash', section.get('content_hash')) \
                .execute()

            if not existing.data:
                supabase.table('document_sections').insert(section).execute()
                saved_count += 1
        except Exception as e:
            logger.error(f"Failed to save section: {e}")

    return {"saved_sections": saved_count}

@flow(
    name="optimized-rag-pipeline",
    task_runner=ConcurrentTaskRunner(),
    retries=1,
    retry_delay_seconds=300
)
async def optimized_rag_pipeline():
    """Оптимизиран RAG pipeline с паралелна обработка"""
    logger = get_run_logger()
    logger.info("Starting optimized RAG pipeline")

    # Фаза 1: Паралелен краулинг на различни сайтове
    site_batches = chunk_sites_by_domain(config.crawler_sites, batch_size=3)
    crawl_results = await asyncio.gather(*[
        crawl_websites_batch(batch) for batch in site_batches
    ])

    # Фаза 2: Паралелна обработка на PDF файлове
    pdf_files = get_unprocessed_pdfs()
    pdf_batches = chunk_list(pdf_files, batch_size=5)

    pdf_results = await asyncio.gather(*[
        process_pdfs_batch(batch) for batch in pdf_batches
    ])

    # Събиране на всички секции
    all_sections = []
    for result in pdf_results:
        all_sections.extend(result.get('sections', []))

    # Фаза 3: Batch генериране на ембединги
    section_batches = chunk_list(all_sections, batch_size=20)

    embedding_results = await asyncio.gather(*[
        generate_embeddings_batch(batch) for batch in section_batches
    ])

    # Фаза 4: Запазване в базата данни
    all_sections_with_embeddings = []
    for result in embedding_results:
        all_sections_with_embeddings.extend(result.get('processed_sections', []))

    save_result = await save_to_database(all_sections_with_embeddings)

    # Обобщение на резултатите
    total_crawled = sum(r.get('crawled_pages', 0) for r in crawl_results)
    total_processed = sum(r.get('processed_files', 0) for r in pdf_results)
    total_saved = save_result.get('saved_sections', 0)

    logger.info(f"Pipeline completed: {total_crawled} pages crawled, "
               f"{total_processed} PDFs processed, {total_saved} sections saved")

    return {
        "crawled_pages": total_crawled,
        "processed_pdfs": total_processed,
        "saved_sections": total_saved,
        "completion_time": datetime.now().isoformat()
    }

# Планиране с различни честоти
@flow(name="incremental-crawl")
async def incremental_crawl_flow():
    """Инкрементален краулинг всеки час"""
    recent_sites = get_sites_for_incremental_crawl()
    return await crawl_websites_batch(recent_sites)

# Планиране на изпълнение
if __name__ == "__main__":
    # Основен pipeline - всеки ден в 3:00
    optimized_rag_pipeline.serve(
        name="daily-rag-pipeline",
        cron="0 3 * * *"
    )

    # Инкрементален краулинг - всеки час
    incremental_crawl_flow.serve(
        name="hourly-incremental-crawl",
        cron="0 * * * *"
    )
```

### Задача 5.2: Prometheus метрики

```python
from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry
import psutil

# Системни метрики
CPU_USAGE = Gauge('system_cpu_usage_percent', 'System CPU usage')
MEMORY_USAGE = Gauge('system_memory_usage_percent', 'System memory usage')
DISK_USAGE = Gauge('system_disk_usage_percent', 'System disk usage')

# Бизнес метрики
PAGES_CRAWLED = Counter('pages_crawled_total', 'Total pages crawled', ['status'])
EMBEDDINGS_GENERATED = Counter('embeddings_generated_total', 'Total embeddings', ['status'])
SEARCH_REQUESTS = Counter('search_requests_total', 'Total search requests')
SEARCH_LATENCY = Histogram('search_latency_seconds', 'Search request latency')

@app.middleware("http")
async def collect_metrics(request: Request, call_next):
    # Събиране на системни метрики
    CPU_USAGE.set(psutil.cpu_percent())
    MEMORY_USAGE.set(psutil.virtual_memory().percent)
    DISK_USAGE.set(psutil.disk_usage('/').percent)

    response = await call_next(request)
    return response

@app.get("/metrics")
async def get_metrics():
    """Endpoint за Prometheus метрики"""
    return Response(generate_latest(), media_type="text/plain")
```

## 📋 Фаза 6: Сигурност и криптиране

### Задача 6.1: Crypto Manager

```python
from cryptography.fernet import Fernet
import os

class CryptoManager:
    def __init__(self):
        key = os.getenv("ENCRYPTION_KEY")
        if not key:
            key = Fernet.generate_key().decode()
            print(f"Generated new encryption key: {key}")
            print("Add this to your .env file as ENCRYPTION_KEY")

        self.cipher = Fernet(key.encode() if isinstance(key, str) else key)

    def encrypt(self, text):
        if not text:
            return None
        return self.cipher.encrypt(text.encode()).decode()

    def decrypt(self, encrypted_text):
        if not encrypted_text:
            return None
        return self.cipher.decrypt(encrypted_text.encode()).decode()
```

## 🔴 Критични проблеми и решения

### 1. Индексиране на pgvector
```sql
-- ❌ ПРОБЛЕМ: Остарял IVFFLAT
CREATE INDEX idx_document_sections_embedding ON document_sections
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- ✅ РЕШЕНИЕ: Модерен HNSW (по-бърз и точен)
CREATE INDEX idx_document_sections_embedding ON document_sections
USING hnsw (embedding vector_cosine_ops) WITH (m = 16, ef_construction = 64);
```

### 2. Scrapy интеграция с Twisted
```python
# ❌ ПРОБЛЕМ: Твърде сложно смесване на Scrapy с asyncio
from twisted.internet import reactor, defer

# ✅ РЕШЕНИЕ: Използване на scrapy-playwright или чист aiohttp
import aiohttp
from playwright.async_api import async_playwright

class ModernCrawler:
    async def crawl_with_aiohttp(self, url):
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                return await response.text()
```

### 3. FastText модел за език
```python
# ❌ ПРОБЛЕМ: FastText модел не е включен в зависимостите
self.lang_model = fasttext.load_model('lid.176.ftz')  # Липсва модел

# ✅ РЕШЕНИЕ: Използване на langdetect или Azure/Google API
from langdetect import detect
def detect_language(self, text: str) -> str:
    try:
        return detect(text)
    except:
        return "unknown"
```

## 🟡 Структурни подобрения

### 1. Подобрена схема с партициониране
```sql
-- Добавяне на партициониране по дати
CREATE TABLE document_sections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id),
    content TEXT NOT NULL,
    section_index INTEGER,
    section_type VARCHAR(20),
    embedding VECTOR(1536),
    embedding_model TEXT DEFAULT 'text-embedding-3-small',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
) PARTITION BY RANGE (created_at);

-- Създаване на месечни партиции
CREATE TABLE document_sections_2025_01 PARTITION OF document_sections
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

### 2. Подобрена обработка на PDF файлове
```python
from unstructured.partition.pdf import partition_pdf
from pdfplumber import PDF
import pymupdf4llm

class AdvancedPDFProcessor:
    def process_pdf(self, file_path: str) -> List[Dict]:
        sections = []

        # За структурирани PDF-и
        try:
            elements = partition_pdf(file_path)
            for element in elements:
                if element.text.strip():
                    sections.append({
                        "content": element.text,
                        "type": element.category,
                        "metadata": element.metadata.to_dict()
                    })
        except:
            # Fallback към pymupdf4llm за по-добро извличане
            md_text = pymupdf4llm.to_markdown(file_path)
            sections.append({
                "content": md_text,
                "type": "markdown",
                "metadata": {"source": "pymupdf4llm"}
            })

        return sections
```

### 3. Chunking стратегия
```python
from langchain.text_splitter import RecursiveCharacterTextSplitter

class SmartChunker:
    def __init__(self):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            separators=["\n\n", "\n", ".", "!", "?", ",", " ", ""]
        )

    def chunk_document(self, content: str) -> List[str]:
        return self.text_splitter.split_text(content)
```

## 📋 Контролен списък за имплементация

### 🔴 Незабавни действия:
- [ ] Сменете IVFFLAT с HNSW индекс
- [ ] Добавете proper error handling във всички async функции
- [ ] Имплементирайте Redis кеширане
- [ ] Добавете rate limiting
- [ ] Тествайте с малко количество данни първо

### 🟡 Средносрочни подобрения:
- [ ] Добавете партициониране на таблици
- [ ] Имплементирайте health checks
- [ ] Добавете comprehensive logging
- [ ] Създайте Docker compose файл за development

### 🟢 Дългосрочни цели:
- [ ] Добавете A/B тестване на различни embedding модели
- [ ] Имплементирайте auto-scaling за краулъри
- [ ] Добавете data lineage tracking
- [ ] Създайте dashboard за мониторинг

## 🚀 Стъпки за стартиране

### 1. Подготовка на средата
```bash
# Създаване на виртуална среда
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# Инсталиране на зависимости
pip install -r requirements.txt

# Копиране на конфигурацията
cp .env.example .env
# Редактиране на .env с вашите стойности
```

### 2. Инициализация на базата данни
```bash
# Стартиране на Supabase инициализация
python database/init_db.py

# Проверка на връзката
python -c "from database.db_manager import DatabaseManager; dm = DatabaseManager(); print('Connection OK')"
```

### 3. Тестване на компонентите
```bash
# Тестване на краулърите
python -m pytest tests/test_crawlers.py

# Тестване на PDF процесора
python -m pytest tests/test_pdf_processor.py

# Тестване на MCP сървъра
python -m pytest tests/test_mcp_server.py
```

### 4. Стартиране на системата
```bash
# Стартиране на MCP сървъра
uvicorn mcp_server.server:app --host 0.0.0.0 --port 8000

# Стартиране на Prefect агент
prefect agent start

# Планиране на работни потоци
python workflows/schedule_flows.py
```

## 📊 Мониторинг и поддръжка

### Разширени Prometheus метрики
```python
from prometheus_client import Counter, Histogram, Gauge, Info

# Бизнес метрики
PAGES_CRAWLED = Counter('pages_crawled_total', 'Total pages crawled', ['domain', 'status'])
EMBEDDINGS_GENERATED = Counter('embeddings_generated_total', 'Total embeddings', ['model', 'status'])
SEARCH_REQUESTS = Counter('search_requests_total', 'Total search requests', ['endpoint'])
SEARCH_LATENCY = Histogram('search_latency_seconds', 'Search latency', ['query_type'])
EMBEDDING_CACHE_HITS = Counter('embedding_cache_hits_total', 'Embedding cache hits')
PDF_PROCESSING_TIME = Histogram('pdf_processing_seconds', 'PDF processing time', ['file_size_mb'])

# Системни метрики
SYSTEM_CPU = Gauge('system_cpu_usage_percent', 'System CPU usage')
SYSTEM_MEMORY = Gauge('system_memory_usage_percent', 'System memory usage')
SYSTEM_DISK = Gauge('system_disk_usage_percent', 'System disk usage')
DATABASE_CONNECTIONS = Gauge('database_connections_active', 'Active DB connections')

# API метрики
OPENAI_API_CALLS = Counter('openai_api_calls_total', 'OpenAI API calls', ['model', 'status'])
OPENAI_TOKENS_USED = Counter('openai_tokens_used_total', 'OpenAI tokens used', ['model'])
SUPABASE_API_CALLS = Counter('supabase_api_calls_total', 'Supabase API calls', ['operation'])

# Качество на данните
DUPLICATE_CONTENT_DETECTED = Counter('duplicate_content_detected_total', 'Duplicate content')
CONTENT_QUALITY_SCORE = Histogram('content_quality_score', 'Content quality score')

# Информация за системата
SYSTEM_INFO = Info('rag_system_info', 'RAG system information')
SYSTEM_INFO.info({
    'version': '1.0.0',
    'embedding_model': 'text-embedding-3-small',
    'vector_dimensions': '1536'
})
```

### Comprehensive Health Checks
```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Dict, Any
import asyncio
import time

class HealthStatus(BaseModel):
    status: str
    timestamp: str
    checks: Dict[str, Any]
    version: str

@app.get("/health", response_model=HealthStatus)
async def comprehensive_health_check():
    """Comprehensive health check endpoint"""
    checks = {}
    overall_status = "healthy"

    # Supabase connection check
    try:
        start_time = time.time()
        supabase.table('documents').select('id').limit(1).execute()
        checks['supabase'] = {
            'status': 'healthy',
            'response_time_ms': round((time.time() - start_time) * 1000, 2)
        }
    except Exception as e:
        checks['supabase'] = {'status': 'unhealthy', 'error': str(e)}
        overall_status = "unhealthy"

    # OpenAI API check
    try:
        start_time = time.time()
        await openai_client.embeddings.create(
            model="text-embedding-3-small",
            input=["health check"]
        )
        checks['openai'] = {
            'status': 'healthy',
            'response_time_ms': round((time.time() - start_time) * 1000, 2)
        }
    except Exception as e:
        checks['openai'] = {'status': 'unhealthy', 'error': str(e)}
        overall_status = "degraded"

    # Redis connection check
    try:
        start_time = time.time()
        redis_client.ping()
        checks['redis'] = {
            'status': 'healthy',
            'response_time_ms': round((time.time() - start_time) * 1000, 2)
        }
    except Exception as e:
        checks['redis'] = {'status': 'unhealthy', 'error': str(e)}
        overall_status = "degraded"

    # System resources check
    import psutil
    cpu_percent = psutil.cpu_percent()
    memory_percent = psutil.virtual_memory().percent
    disk_percent = psutil.disk_usage('/').percent

    checks['system_resources'] = {
        'cpu_percent': cpu_percent,
        'memory_percent': memory_percent,
        'disk_percent': disk_percent,
        'status': 'healthy' if all([
            cpu_percent < 80,
            memory_percent < 85,
            disk_percent < 90
        ]) else 'warning'
    }

    if checks['system_resources']['status'] == 'warning':
        overall_status = "degraded"

    return HealthStatus(
        status=overall_status,
        timestamp=datetime.now().isoformat(),
        checks=checks,
        version="1.0.0"
    )

@app.get("/metrics/summary")
async def metrics_summary():
    """Summary of key metrics"""
    return {
        "total_documents": get_metric_value('documents_total'),
        "total_embeddings": get_metric_value('embeddings_generated_total'),
        "search_requests_24h": get_metric_value('search_requests_total', '24h'),
        "cache_hit_rate": calculate_cache_hit_rate(),
        "avg_search_latency_ms": get_metric_value('search_latency_seconds') * 1000,
        "system_health": "healthy"  # Based on health check
    }
```

### Structured Logging
```python
import logging
import json
from datetime import datetime
from typing import Any, Dict

class StructuredLogger:
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)

        # JSON formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

        # File handler with rotation
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            'logs/rag_system.log',
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)

    def log_structured(self, level: str, message: str, **kwargs):
        """Log structured data as JSON"""
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'message': message,
            'level': level,
            **kwargs
        }

        if level == 'info':
            self.logger.info(json.dumps(log_data))
        elif level == 'error':
            self.logger.error(json.dumps(log_data))
        elif level == 'warning':
            self.logger.warning(json.dumps(log_data))

    def log_crawl_event(self, url: str, status: str, duration: float, **metadata):
        """Log crawling events"""
        self.log_structured(
            'info',
            'Crawl completed',
            event_type='crawl',
            url=url,
            status=status,
            duration_seconds=duration,
            metadata=metadata
        )

    def log_search_event(self, query: str, results_count: int, latency: float):
        """Log search events"""
        self.log_structured(
            'info',
            'Search completed',
            event_type='search',
            query_hash=hash(query),
            results_count=results_count,
            latency_ms=latency * 1000
        )
```

### Performance Monitoring Dashboard
```python
# Grafana dashboard configuration (JSON)
GRAFANA_DASHBOARD = {
    "dashboard": {
        "title": "RAG System Monitoring",
        "panels": [
            {
                "title": "Search Requests per Minute",
                "type": "graph",
                "targets": [
                    {
                        "expr": "rate(search_requests_total[1m])",
                        "legendFormat": "Requests/min"
                    }
                ]
            },
            {
                "title": "Embedding Generation Rate",
                "type": "graph",
                "targets": [
                    {
                        "expr": "rate(embeddings_generated_total[5m])",
                        "legendFormat": "Embeddings/5min"
                    }
                ]
            },
            {
                "title": "Cache Hit Rate",
                "type": "stat",
                "targets": [
                    {
                        "expr": "rate(embedding_cache_hits_total[5m]) / rate(embeddings_generated_total[5m])",
                        "legendFormat": "Cache Hit Rate"
                    }
                ]
            },
            {
                "title": "System Resources",
                "type": "graph",
                "targets": [
                    {"expr": "system_cpu_usage_percent", "legendFormat": "CPU %"},
                    {"expr": "system_memory_usage_percent", "legendFormat": "Memory %"},
                    {"expr": "system_disk_usage_percent", "legendFormat": "Disk %"}
                ]
            }
        ]
    }
}
```

## 🎯 Метрики за успех и KPI

### Технически KPI
| Метрика | Целева стойност | Критична стойност |
|---------|-----------------|-------------------|
| Точност на търсенето (MRR) | >0.85 | <0.70 |
| Време за обработка на PDF | <100ms/страница | >500ms/страница |
| Време за отговор на MCP | <500ms | >2000ms |
| Успешни краулинг заявки | >99.5% | <95% |
| Cache hit rate за ембединги | >70% | <40% |
| Uptime на системата | >99.9% | <99% |

### Бизнес KPI
| Метрика | Целева стойност |
|---------|-----------------|
| Намаляване на халюцинации | >60% |
| Релевантност на отговорите | >90% |
| Време за отговор на потребителски заявки | <2s |
| Покритие на домейни | >95% от целевите сайтове |

## 🚨 Критични предупреждения и ограничения

### pgvector ограничения
- **Максимална размерност**: 2000 (използваме 1536 за text-embedding-3-small)
- **Индекс размер**: HNSW индексите заемат значителна памет
- **Concurrent операции**: Заключване на таблици при паралелна обработка

### OpenAI API ограничения
- **Rate limits**: 1M токена/минута за tier 1
- **Квоти**: Мониторинг на използваните токени
- **Fallback стратегия**: Локални модели при достигане на лимит

### Supabase ограничения
- **Безплатен план**: 500MB база данни, 2GB bandwidth
- **Concurrent connections**: Максимум 60 за безплатен план
- **Storage**: 1GB за безплатен план

## 🔄 Миграционна стратегия

### Фаза 1: MVP (2-3 седмици)
- [ ] Основна структура на проекта
- [ ] Базов Scrapy краулър
- [ ] Проста PDF обработка
- [ ] Основен MCP сървър
- [ ] Базови тестове

### Фаза 2: Оптимизация (3-4 седмици)
- [ ] Асинхронни краулъри
- [ ] HNSW индекси
- [ ] Кеширане на ембединги
- [ ] Rate limiting
- [ ] Prometheus метрики

### Фаза 3: Production Ready (2-3 седмици)
- [ ] Hybrid търсене
- [ ] Comprehensive мониторинг
- [ ] Автоматизирани тестове
- [ ] CI/CD pipeline
- [ ] Документация

## 📚 Допълнителни ресурси

### Документация
- [Supabase pgvector Guide](https://supabase.com/docs/guides/database/extensions/pgvector)
- [OpenAI Embeddings API](https://platform.openai.com/docs/guides/embeddings)
- [Prefect Documentation](https://docs.prefect.io/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)

### Полезни библиотеки
```python
# requirements-extended.txt
unstructured[pdf]==0.10.30  # Подобрена PDF обработка
langchain==0.0.350          # Text splitting и chunking
sentence-transformers==2.2.2  # Локални embedding модели
faiss-cpu==1.7.4           # Алтернатива за векторно търсене
redis==5.0.1               # Кеширане
celery==5.3.4              # Алтернатива за task queue
```

### Мониторинг стек
```yaml
# docker-compose.monitoring.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
```

---

**Този обединен план интегрира всички критични подобрения, оптимизации и best practices за създаване на production-ready RAG система с високо качество, надеждност и производителност.**
```

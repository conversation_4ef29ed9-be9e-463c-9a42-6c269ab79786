# Обединен план за имплементация на RAG система

## 🎯 Цел и обхват
Създаване на пълнофункционална RAG (Retrieval-Augmented Generation) система с MCP (Model Context Protocol) сървър за намаляване на халюцинациите на големи езикови модели чрез динамично извличане на релевантна информация от външна база знания.

## 🏗️ Архитектурен преглед

### Medallion архитектура (Bronze/Silver/Gold)
- **🥉 Бронзов слой**: Сурови данни от краулинг (HTML, PDF, метаданни)
- **🥈 Сребърен слой**: Обработени и почистени данни (текст, структурирано съдържание)
- **🥇 Златен слой**: Готови за използване данни с ембединги за RAG

### Основни компоненти
- **Краулинг система**: Асинхронна с Scrapy, Playwright, aiohttp
- **ETL процеси**: Обработка на текст, PDF и таблици с Pydantic валидация
- **Векторна база данни**: Supabase с pgvector и HNSW индекси
- **MCP сървър**: FastAPI имплементация с Prometheus метрики
- **Оркестрация**: Prefect за управление на работни потоци

## 📋 Фаза 1: Подготовка и структура

### Задача 1.1: Създаване на проектна структура

#### Директории
```
rag-system/
├── crawlers/           # Краулинг компоненти
│   ├── spiders/       # Scrapy spiders
│   ├── base_crawler.py
│   ├── js_crawler.py  # Playwright краулър
│   └── pdf_downloader.py
├── processors/         # ETL процеси
│   ├── base_processor.py
│   ├── html_processor.py
│   └── pdf_processor.py
├── database/          # SQL схеми и скриптове
│   ├── schema.sql
│   ├── functions.sql
│   └── init_db.py
├── embeddings/        # Ембединг генериране
│   └── embedder.py
├── mcp_server/        # MCP сървър
│   └── server.py
├── security/          # Криптиране и сигурност
│   └── crypto_manager.py
├── monitoring/        # Метрики и наблюдение
│   └── metrics.py
├── config/           # Конфигурационни файлове
│   ├── settings.py
│   ├── database.py
│   ├── crawlers.py
│   └── embeddings.py
├── tests/            # Тестове
├── docs/             # Документация
├── workflows/        # Prefect работни потоци
├── main.py          # Входна точка
├── requirements.txt # Python зависимости
├── docker-compose.yml
├── .env.example
├── .gitignore
└── README.md
```

#### Основни файлове
- `main.py` - входна точка на приложението
- `requirements.txt` - Python зависимости с конкретни версии
- `.gitignore` - игнориране на временни файлове
- `setup.py` - инсталация като пакет
- `docker-compose.yml` - контейнеризация
- `__init__.py` във всяка директория

#### Зависимости (requirements.txt)
```
# Основни
scrapy==2.8.0
supabase==1.0.3
fastapi==0.95.2
uvicorn==0.22.0
python-dotenv==1.0.0

# Обработка на данни
beautifulsoup4==4.12.2
pymupdf==1.22.3
langdetect==1.0.9
fasttext==0.9.2
pydantic==1.10.8

# Асинхронни операции
aiohttp==3.8.4
playwright==1.33.0
asyncio-throttle==1.0.2

# AI и ембединги
openai==0.27.8
pgvector==0.1.8

# Мониторинг и оркестрация
prometheus-client==0.16.0
prefect==2.10.8
tenacity==8.2.2

# Сигурност
cryptography==40.0.2

# Тестване
pytest==7.3.1
pytest-asyncio==0.21.0
```

### Задача 1.2: Настройка на Supabase

#### Създаване на проект
1. Регистрация в Supabase (https://supabase.com/)
2. Създаване на проект "rag-system"
3. Запазване на URL и API ключове в `.env`
4. Криптиране на чувствителни данни

#### Активиране на разширения
```sql
-- Векторни операции
CREATE EXTENSION IF NOT EXISTS vector;

-- Планирани задачи
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Статистики за производителност
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
```

#### Схема на базата данни
```sql
-- Бронзов слой (сурови данни)
CREATE TABLE raw_pages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  url TEXT NOT NULL,
  html_content TEXT,
  fetch_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB,
  is_processed BOOLEAN DEFAULT FALSE
);

CREATE TABLE raw_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  url TEXT NOT NULL,
  file_path TEXT,  -- Път към файла в Supabase Storage
  file_type TEXT,
  storage_bucket TEXT DEFAULT 'documents',
  fetch_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB,
  is_processed BOOLEAN DEFAULT FALSE
);

-- Сребърен слой (обработени данни)
CREATE TABLE clean_pages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  raw_page_id UUID REFERENCES raw_pages(id),
  content TEXT NOT NULL,
  language TEXT,
  content_hash TEXT UNIQUE,
  processed_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);

-- Златен слой (крайни данни)
CREATE TABLE documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT,
  source TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);

CREATE TABLE document_sections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID REFERENCES documents(id),
  content TEXT NOT NULL,
  section_index INTEGER,
  section_type VARCHAR(20), -- heading/paragraph/list/table
  embedding VECTOR(1536),
  embedding_model TEXT DEFAULT 'text-embedding-3-small',
  similarity_score FLOAT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 🔴 КРИТИЧНО: Използване на HNSW индекси
```sql
-- ❌ ПРОБЛЕМ: Остарял IVFFLAT индекс
-- CREATE INDEX idx_document_sections_embedding ON document_sections 
-- USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- ✅ РЕШЕНИЕ: Модерен HNSW индекс (по-бърз и точен)
CREATE INDEX idx_document_sections_embedding ON document_sections 
USING hnsw (embedding vector_cosine_ops) WITH (m = 16, ef_construction = 64);
```

## 📋 Фаза 2: Краулинг система

### Задача 2.1: Базов краулър клас

```python
from abc import ABC, abstractmethod
import logging
import time
import random
from prometheus_client import Counter, Gauge

# Метрики за наблюдение
PAGES_CRAWLED = Counter('pages_crawled_total', 'Total pages crawled', ['crawler_type', 'status'])
CRAWL_TIME = Gauge('crawl_time_seconds', 'Time taken to crawl', ['crawler_type'])

class BaseCrawler(ABC):
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        ]
        self.crawler_type = self.__class__.__name__
    
    def get_random_user_agent(self):
        return random.choice(self.user_agents)
    
    def add_delay(self):
        min_delay = self.config.get('min_delay', 1)
        max_delay = self.config.get('max_delay', 5)
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
    
    @abstractmethod
    def crawl(self):
        pass
    
    @abstractmethod
    def save_data(self, data):
        pass
    
    def run(self):
        self.logger.info(f"Starting crawling with {self.__class__.__name__}")
        start_time = time.time()
        
        try:
            data = self.crawl()
            self.save_data(data)
            PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status='success').inc()
        except Exception as e:
            self.logger.error(f"Error during crawling: {str(e)}")
            PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status='error').inc()
        
        end_time = time.time()
        CRAWL_TIME.labels(crawler_type=self.crawler_type).set(end_time - start_time)
```

### Задача 2.2: Асинхронни краулъри

#### Playwright краулър за JavaScript страници
- Асинхронна обработка с semaphore за ограничаване
- Retry логика с tenacity
- Prometheus метрики
- Проверка за дубликати

#### Асинхронен PDF даунлоудър
- aiohttp за бързо изтегляне
- Запазване в Supabase Storage
- Batch обработка с ограничение на concurrent заявки

#### Scrapy интегратор
- Twisted reactor интеграция
- Асинхронно изпълнение на множество spiders
- Конфигурируеми настройки за всеки сайт

## 📋 Фаза 3: ETL система

### Задача 3.1: Pydantic конфигурация

```python
from pydantic import BaseSettings, validator, Field
from typing import List, Optional

class CrawlerConfig(BaseSettings):
    start_urls: List[str]
    allowed_domains: List[str]
    download_delay: float = 2.0
    concurrent_requests: int = 8
    max_pages: Optional[int] = None
    
    @validator('concurrent_requests')
    def validate_concurrent_requests(cls, v):
        if v < 1 or v > 32:
            raise ValueError('concurrent_requests must be between 1 and 32')
        return v
    
    class Config:
        env_file = '.env'
        env_prefix = 'CRAWLER_'

class PDFProcessorConfig(BaseSettings):
    batch_size: int = Field(default=10, ge=1, le=100)
    extract_tables: bool = True
    extract_images: bool = False
    min_text_length: int = Field(default=50, ge=10)
    section_splitter: str = Field(default="blocks", regex="^(blocks|simple|layout)$")

class EmbeddingConfig(BaseSettings):
    model: str = "text-embedding-3-small"
    batch_size: int = Field(default=20, ge=1, le=100)
    dimensions: int = 1536
    api_key: str
```

### Задача 3.2: Подобрен PDF процесор

#### 🔴 КРИТИЧНО: FastText модел проблем
```python
# ❌ ПРОБЛЕМ: FastText модел не е включен в зависимостите
# self.lang_model = fasttext.load_model('lid.176.ftz')  # Липсва модел

# ✅ РЕШЕНИЕ: Използване на langdetect или Azure/Google API
from langdetect import detect

def detect_language(self, text: str) -> str:
    try:
        return detect(text)
    except:
        return "unknown"
```

#### PDF процесор с подобрения
- PyMuPDF за извличане на текст по блокове
- Извличане на таблици с find_tables()
- Retry логика с tenacity
- Prometheus метрики за производителност
- Хеширане на съдържание за дедупликация

### Задача 3.3: Batch Embedding процесор

```python
import asyncio
from openai import AsyncOpenAI
from prometheus_client import Counter, Histogram, Gauge

class EmbeddingProcessor:
    def __init__(self, config: EmbeddingConfig):
        self.config = config
        self.client = AsyncOpenAI(api_key=config.api_key)
        self.batch_size = config.batch_size
        self.model = config.model

    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        response = await self.client.embeddings.create(
            model=self.model,
            input=texts
        )
        return [item.embedding for item in response.data]

    async def process_batch(self, sections: List[Dict]) -> List[Dict]:
        texts = [section['content'] for section in sections]
        embeddings = await self.generate_embeddings(texts)

        for i, section in enumerate(sections):
            section['embedding'] = embeddings[i]
            section['embedding_model'] = self.model

        return sections
```

## 📋 Фаза 4: MCP сървър

### Задача 4.1: FastAPI имплементация

#### Основни компоненти
- Pydantic модели за валидация на данни
- CORS middleware за cross-origin заявки
- Prometheus метрики за мониторинг
- Rate limiting за защита от злоупотреба
- Кеширане на резултати с Redis

#### Endpoints
```python
from fastapi import FastAPI, HTTPException, Request
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional

class SearchQuery(BaseModel):
    query: str
    limit: int = Field(default=5, ge=1, le=20)
    threshold: float = Field(default=0.7, ge=0, le=1.0)

class SearchResult(BaseModel):
    content: str
    source: str
    similarity: float
    metadata: Dict[str, Any] = {}

@app.post("/search", response_model=List[SearchResult])
async def search_documents(query: SearchQuery):
    # Генериране на ембединг за заявката
    query_embedding = generate_embedding(query.query)

    # Векторно търсене в Supabase
    response = supabase.rpc("match_documents", {
        "query_embedding": query_embedding,
        "match_threshold": query.threshold,
        "match_count": query.limit
    }).execute()

    return format_search_results(response.data)

@app.post("/mcp", response_model=MCPResponse)
async def mcp_endpoint(request: MCPRequest):
    # Маршрутизиране на заявката към правилния инструмент
    if request.tool == "search_documents":
        query = SearchQuery(**request.params)
        result = await search_documents(query)
        return MCPResponse(result=result)
    else:
        return MCPResponse(result=None, error=f"Unknown tool: {request.tool}")
```

#### 🔴 КРИТИЧНО: Rate limiting и кеширане
```python
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
import redis
import json

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
redis_client = redis.Redis(host='localhost', port=6379, db=0)

@app.post("/search")
@limiter.limit("30/minute")
async def search_documents(request: Request, query: SearchQuery):
    # Кеширане на резултати
    cache_key = f"search:{hash(query.query)}:{query.limit}"
    cached_result = redis_client.get(cache_key)
    if cached_result:
        return json.loads(cached_result)

    # Търсене...
    results = await perform_search(query)

    # Кеширане за 5 минути
    redis_client.setex(cache_key, 300, json.dumps(results))
    return results
```

## 📋 Фаза 5: Оркестрация и мониторинг

### Задача 5.1: Prefect работни потоци

```python
from prefect import flow, task
import asyncio
from datetime import datetime

@task
async def crawl_websites():
    """Краулинг на уеб сайтове"""
    crawler_manager = CrawlerManager(config)
    await crawler_manager.run_all()

@task
async def process_pdfs():
    """Обработка на PDF файлове"""
    pdf_processor = PDFProcessor(config)
    await pdf_processor.process_all()

@task
async def generate_embeddings():
    """Генериране на ембединги"""
    embedding_processor = EmbeddingProcessor(config)
    await embedding_processor.process_all()

@flow
async def daily_rag_pipeline():
    """Ежедневен RAG pipeline"""
    logger.info("Starting daily RAG pipeline")

    # Паралелно изпълнение на краулинг
    await crawl_websites()

    # Последователна обработка
    await process_pdfs()
    await generate_embeddings()

    logger.info("Daily RAG pipeline completed")

# Планиране на изпълнение
if __name__ == "__main__":
    daily_rag_pipeline.serve(
        name="rag-pipeline",
        cron="0 3 * * *"  # Всеки ден в 3:00 сутринта
    )
```

### Задача 5.2: Prometheus метрики

```python
from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry
import psutil

# Системни метрики
CPU_USAGE = Gauge('system_cpu_usage_percent', 'System CPU usage')
MEMORY_USAGE = Gauge('system_memory_usage_percent', 'System memory usage')
DISK_USAGE = Gauge('system_disk_usage_percent', 'System disk usage')

# Бизнес метрики
PAGES_CRAWLED = Counter('pages_crawled_total', 'Total pages crawled', ['status'])
EMBEDDINGS_GENERATED = Counter('embeddings_generated_total', 'Total embeddings', ['status'])
SEARCH_REQUESTS = Counter('search_requests_total', 'Total search requests')
SEARCH_LATENCY = Histogram('search_latency_seconds', 'Search request latency')

@app.middleware("http")
async def collect_metrics(request: Request, call_next):
    # Събиране на системни метрики
    CPU_USAGE.set(psutil.cpu_percent())
    MEMORY_USAGE.set(psutil.virtual_memory().percent)
    DISK_USAGE.set(psutil.disk_usage('/').percent)

    response = await call_next(request)
    return response

@app.get("/metrics")
async def get_metrics():
    """Endpoint за Prometheus метрики"""
    return Response(generate_latest(), media_type="text/plain")
```

## 📋 Фаза 6: Сигурност и криптиране

### Задача 6.1: Crypto Manager

```python
from cryptography.fernet import Fernet
import os

class CryptoManager:
    def __init__(self):
        key = os.getenv("ENCRYPTION_KEY")
        if not key:
            key = Fernet.generate_key().decode()
            print(f"Generated new encryption key: {key}")
            print("Add this to your .env file as ENCRYPTION_KEY")

        self.cipher = Fernet(key.encode() if isinstance(key, str) else key)

    def encrypt(self, text):
        if not text:
            return None
        return self.cipher.encrypt(text.encode()).decode()

    def decrypt(self, encrypted_text):
        if not encrypted_text:
            return None
        return self.cipher.decrypt(encrypted_text.encode()).decode()
```

## 🔴 Критични проблеми и решения

### 1. Индексиране на pgvector
```sql
-- ❌ ПРОБЛЕМ: Остарял IVFFLAT
CREATE INDEX idx_document_sections_embedding ON document_sections
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- ✅ РЕШЕНИЕ: Модерен HNSW (по-бърз и точен)
CREATE INDEX idx_document_sections_embedding ON document_sections
USING hnsw (embedding vector_cosine_ops) WITH (m = 16, ef_construction = 64);
```

### 2. Scrapy интеграция с Twisted
```python
# ❌ ПРОБЛЕМ: Твърде сложно смесване на Scrapy с asyncio
from twisted.internet import reactor, defer

# ✅ РЕШЕНИЕ: Използване на scrapy-playwright или чист aiohttp
import aiohttp
from playwright.async_api import async_playwright

class ModernCrawler:
    async def crawl_with_aiohttp(self, url):
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                return await response.text()
```

### 3. FastText модел за език
```python
# ❌ ПРОБЛЕМ: FastText модел не е включен в зависимостите
self.lang_model = fasttext.load_model('lid.176.ftz')  # Липсва модел

# ✅ РЕШЕНИЕ: Използване на langdetect или Azure/Google API
from langdetect import detect
def detect_language(self, text: str) -> str:
    try:
        return detect(text)
    except:
        return "unknown"
```

## 🟡 Структурни подобрения

### 1. Подобрена схема с партициониране
```sql
-- Добавяне на партициониране по дати
CREATE TABLE document_sections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id),
    content TEXT NOT NULL,
    section_index INTEGER,
    section_type VARCHAR(20),
    embedding VECTOR(1536),
    embedding_model TEXT DEFAULT 'text-embedding-3-small',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
) PARTITION BY RANGE (created_at);

-- Създаване на месечни партиции
CREATE TABLE document_sections_2025_01 PARTITION OF document_sections
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

### 2. Подобрена обработка на PDF файлове
```python
from unstructured.partition.pdf import partition_pdf
from pdfplumber import PDF
import pymupdf4llm

class AdvancedPDFProcessor:
    def process_pdf(self, file_path: str) -> List[Dict]:
        sections = []

        # За структурирани PDF-и
        try:
            elements = partition_pdf(file_path)
            for element in elements:
                if element.text.strip():
                    sections.append({
                        "content": element.text,
                        "type": element.category,
                        "metadata": element.metadata.to_dict()
                    })
        except:
            # Fallback към pymupdf4llm за по-добро извличане
            md_text = pymupdf4llm.to_markdown(file_path)
            sections.append({
                "content": md_text,
                "type": "markdown",
                "metadata": {"source": "pymupdf4llm"}
            })

        return sections
```

### 3. Chunking стратегия
```python
from langchain.text_splitter import RecursiveCharacterTextSplitter

class SmartChunker:
    def __init__(self):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            separators=["\n\n", "\n", ".", "!", "?", ",", " ", ""]
        )

    def chunk_document(self, content: str) -> List[str]:
        return self.text_splitter.split_text(content)
```

## 📋 Контролен списък за имплементация

### 🔴 Незабавни действия:
- [ ] Сменете IVFFLAT с HNSW индекс
- [ ] Добавете proper error handling във всички async функции
- [ ] Имплементирайте Redis кеширане
- [ ] Добавете rate limiting
- [ ] Тествайте с малко количество данни първо

### 🟡 Средносрочни подобрения:
- [ ] Добавете партициониране на таблици
- [ ] Имплементирайте health checks
- [ ] Добавете comprehensive logging
- [ ] Създайте Docker compose файл за development

### 🟢 Дългосрочни цели:
- [ ] Добавете A/B тестване на различни embedding модели
- [ ] Имплементирайте auto-scaling за краулъри
- [ ] Добавете data lineage tracking
- [ ] Създайте dashboard за мониторинг

## 🚀 Стъпки за стартиране

### 1. Подготовка на средата
```bash
# Създаване на виртуална среда
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# Инсталиране на зависимости
pip install -r requirements.txt

# Копиране на конфигурацията
cp .env.example .env
# Редактиране на .env с вашите стойности
```

### 2. Инициализация на базата данни
```bash
# Стартиране на Supabase инициализация
python database/init_db.py

# Проверка на връзката
python -c "from database.db_manager import DatabaseManager; dm = DatabaseManager(); print('Connection OK')"
```

### 3. Тестване на компонентите
```bash
# Тестване на краулърите
python -m pytest tests/test_crawlers.py

# Тестване на PDF процесора
python -m pytest tests/test_pdf_processor.py

# Тестване на MCP сървъра
python -m pytest tests/test_mcp_server.py
```

### 4. Стартиране на системата
```bash
# Стартиране на MCP сървъра
uvicorn mcp_server.server:app --host 0.0.0.0 --port 8000

# Стартиране на Prefect агент
prefect agent start

# Планиране на работни потоци
python workflows/schedule_flows.py
```

## 📊 Мониторинг и поддръжка

### Prometheus метрики
- `pages_crawled_total` - общо краулнати страници
- `embeddings_generated_total` - общо генерирани ембединги
- `search_requests_total` - общо search заявки
- `search_latency_seconds` - латентност на търсенето
- `system_cpu_usage_percent` - CPU използване
- `system_memory_usage_percent` - памет използване

### Логове
- Структурирани JSON логове
- Ротация на логове по размер и дата
- Централизирано логиране с ELK stack (опционално)

### Health checks
- `/health` endpoint за проверка на състоянието
- Проверка на връзката с Supabase
- Проверка на OpenAI API
- Проверка на Redis връзката

---

**Този план обединява всичката налична информация и предоставя пълна roadmap за имплементация на RAG системата с всички критични подобрения и best practices.**
```

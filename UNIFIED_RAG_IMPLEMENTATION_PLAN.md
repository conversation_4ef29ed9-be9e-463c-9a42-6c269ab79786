# Обединен план за имплементация на RAG система

## 🚨 КРИТИЧНИ КОРЕКЦИИ (ЗАДЪЛЖИТЕЛНО ЗА ПРОЧИТАНЕ)

### ⚠️ Версии на библиотеки - АКТУАЛИЗИРАНО
- **OpenAI**: Актуализир<PERSON>но от `0.27.8` → `>=1.6.0` (за AsyncOpenAI поддръжка)
- **Pydantic**: Мигрирано към V2 (`>=2.5.0`) за 50x по-добра производителност
- **FastText**: Премахнато - заменено с `langdetect` за по-лесна употреба

### ⚠️ Scrapy интеграция - РЕШЕНО
- Добавени 3 решения за Scrapy + asyncio интеграция
- Препоръчва се хибриден подход: aiohttp за прости + Scrapy за сложни сайтове

### ⚠️ Pydantic V2 миграция - ЗАВЪРШЕНА
- Всички модели актуализирани към новия синтаксис
- `BaseSettings` → `pydantic_settings.BaseSettings`
- `@validator` → `@field_validator`

## 🎯 Цел и обхват
Създаване на пълнофункционална RAG (Retrieval-Augmented Generation) система с MCP (Model Context Protocol) сървър за намаляване на халюцинациите на големи езикови модели чрез динамично извличане на релевантна информация от външна база знания.

## 🏗️ Архитектурен преглед

### Medallion архитектура (Bronze/Silver/Gold)
- **🥉 Бронзов слой**: Сурови данни от краулинг (HTML, PDF, метаданни)
- **🥈 Сребърен слой**: Обработени и почистени данни (текст, структурирано съдържание)
- **🥇 Златен слой**: Готови за използване данни с ембединги за RAG

### Основни компоненти
- **Краулинг система**: Асинхронна с Scrapy, Playwright, aiohttp
- **ETL процеси**: Обработка на текст, PDF и таблици с Pydantic валидация
- **Векторна база данни**: Supabase с pgvector и HNSW индекси
- **MCP сървър**: FastAPI имплементация с Prometheus метрики
- **Оркестрация**: Prefect за управление на работни потоци

## 📋 Фаза 1: Подготовка и структура

### Задача 1.1: Създаване на проектна структура

#### Директории
```
rag-system/
├── crawlers/           # Краулинг компоненти
│   ├── spiders/       # Scrapy spiders
│   ├── base_crawler.py
│   ├── js_crawler.py  # Playwright краулър
│   └── pdf_downloader.py
├── processors/         # ETL процеси
│   ├── base_processor.py
│   ├── html_processor.py
│   └── pdf_processor.py
├── database/          # SQL схеми и скриптове
│   ├── schema.sql
│   ├── functions.sql
│   └── init_db.py
├── embeddings/        # Ембединг генериране
│   └── embedder.py
├── mcp_server/        # MCP сървър
│   └── server.py
├── security/          # Криптиране и сигурност
│   └── crypto_manager.py
├── monitoring/        # Метрики и наблюдение
│   └── metrics.py
├── config/           # Конфигурационни файлове
│   ├── settings.py
│   ├── database.py
│   ├── crawlers.py
│   └── embeddings.py
├── tests/            # Тестове
├── docs/             # Документация
├── workflows/        # Prefect работни потоци
├── main.py          # Входна точка
├── requirements.txt # Python зависимости
├── docker-compose.yml
├── .env.example
├── .gitignore
└── README.md
```

#### Основни файлове
- `main.py` - входна точка на приложението
- `requirements.txt` - Python зависимости с конкретни версии
- `.gitignore` - игнориране на временни файлове
- `setup.py` - инсталация като пакет
- `docker-compose.yml` - контейнеризация
- `__init__.py` във всяка директория

#### Зависимости (requirements.txt)
```
# Основни
scrapy==2.11.0
supabase==2.3.0
fastapi==0.104.1
uvicorn==0.24.0
python-dotenv==1.0.0

# Обработка на данни
beautifulsoup4==4.12.2
pymupdf==1.23.8
langdetect==1.0.9
# fasttext==0.9.2  # Премахнато - използваме langdetect
pydantic>=2.5.0
pydantic-settings>=2.1.0  # Нужно за BaseSettings в Pydantic V2

# Асинхронни операции
aiohttp==3.9.1
playwright==1.40.0
asyncio-throttle==1.0.2
scrapy-asyncer==0.1.0  # За интеграция на Scrapy с asyncio

# AI и ембединги
openai>=1.6.0  # Нова версия с AsyncOpenAI поддръжка
pgvector==0.2.4

# Мониторинг и оркестрация
prometheus-client==0.16.0
prefect==2.10.8
tenacity==8.2.2

# Сигурност
cryptography==40.0.2

# Тестване
pytest==7.3.1
pytest-asyncio==0.21.0
```

### Задача 1.2: Настройка на Supabase

#### Създаване на проект
1. Регистрация в Supabase (https://supabase.com/)
2. Създаване на проект "rag-system"
3. Запазване на URL и API ключове в `.env`
4. Криптиране на чувствителни данни

#### Активиране на разширения
```sql
-- Векторни операции
CREATE EXTENSION IF NOT EXISTS vector;

-- Планирани задачи
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Статистики за производителност
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;
```

#### Схема на базата данни
```sql
-- Бронзов слой (сурови данни)
CREATE TABLE raw_pages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  url TEXT NOT NULL,
  html_content TEXT,
  fetch_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB,
  is_processed BOOLEAN DEFAULT FALSE
);

CREATE TABLE raw_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  url TEXT NOT NULL,
  file_path TEXT,  -- Път към файла в Supabase Storage
  file_type TEXT,
  storage_bucket TEXT DEFAULT 'documents',
  fetch_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB,
  is_processed BOOLEAN DEFAULT FALSE
);

-- Сребърен слой (обработени данни)
CREATE TABLE clean_pages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  raw_page_id UUID REFERENCES raw_pages(id),
  content TEXT NOT NULL,
  language TEXT,
  content_hash TEXT UNIQUE,
  processed_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);

-- Златен слой (крайни данни)
CREATE TABLE documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT,
  source TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);

CREATE TABLE document_sections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID REFERENCES documents(id),
  content TEXT NOT NULL,
  content_tsv TSVECTOR,  -- Предварително изчислен tsvector за бързо текстово търсене
  content_hash TEXT UNIQUE,  -- За дедупликация
  section_index INTEGER,
  section_type VARCHAR(20), -- heading/paragraph/list/table
  embedding VECTOR(1536),
  embedding_model TEXT DEFAULT 'text-embedding-3-small',
  similarity_score FLOAT,
  language TEXT DEFAULT 'en',  -- Език на съдържанието
  is_embedded BOOLEAN DEFAULT FALSE,  -- Статус на embedding генериране
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 🔴 КРИТИЧНО: Използване на HNSW индекси
```sql
-- ❌ ПРОБЛЕМ: Остарял IVFFLAT индекс
-- CREATE INDEX idx_document_sections_embedding ON document_sections 
-- USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- ✅ РЕШЕНИЕ: Оптимизирани индекси за hybrid търсене
-- HNSW индекс за векторно търсене
CREATE INDEX idx_document_sections_embedding ON document_sections
USING hnsw (embedding vector_cosine_ops)
WITH (m = 32, ef_construction = 128, parallel_workers = 8);

-- GIN индекс за бързо текстово търсене
CREATE INDEX idx_document_sections_tsv ON document_sections USING GIN(content_tsv);

-- Индекси за статус колони
CREATE INDEX idx_document_sections_embedded ON document_sections(is_embedded);
CREATE INDEX idx_document_sections_language ON document_sections(language);
CREATE INDEX idx_document_sections_content_hash ON document_sections(content_hash);
```

## 📋 Фаза 2: Краулинг система

### Задача 2.1: Базов краулър клас

```python
from abc import ABC, abstractmethod
import logging
import time
import random
from prometheus_client import Counter, Gauge

# Метрики за наблюдение
PAGES_CRAWLED = Counter('pages_crawled_total', 'Total pages crawled', ['crawler_type', 'status'])
CRAWL_TIME = Gauge('crawl_time_seconds', 'Time taken to crawl', ['crawler_type'])

class BaseCrawler(ABC):
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        ]
        self.crawler_type = self.__class__.__name__
    
    def get_random_user_agent(self):
        return random.choice(self.user_agents)
    
    def add_delay(self):
        min_delay = self.config.get('min_delay', 1)
        max_delay = self.config.get('max_delay', 5)
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
    
    @abstractmethod
    def crawl(self):
        pass
    
    @abstractmethod
    def save_data(self, data):
        pass
    
    def run(self):
        self.logger.info(f"Starting crawling with {self.__class__.__name__}")
        start_time = time.time()
        
        try:
            data = self.crawl()
            self.save_data(data)
            PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status='success').inc()
        except Exception as e:
            self.logger.error(f"Error during crawling: {str(e)}")
            PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status='error').inc()
        
        end_time = time.time()
        CRAWL_TIME.labels(crawler_type=self.crawler_type).set(end_time - start_time)
```

### Задача 2.2: Асинхронни краулъри

#### Playwright краулър за JavaScript страници
- Асинхронна обработка с semaphore за ограничаване
- Retry логика с tenacity
- Prometheus метрики
- Проверка за дубликати

#### Асинхронен PDF даунлоудър
- aiohttp за бързо изтегляне
- Запазване в Supabase Storage
- Batch обработка с ограничение на concurrent заявки

#### Scrapy интегратор
- Twisted reactor интеграция
- Асинхронно изпълнение на множество spiders
- Конфигурируеми настройки за всеки сайт

## 📋 Фаза 3: ETL система

### Задача 3.1: Pydantic конфигурация

```python
# Pydantic V2 синтаксис
from pydantic import BaseModel, Field, field_validator, ConfigDict
from pydantic_settings import BaseSettings
from typing import List, Optional, Literal

class CrawlerConfig(BaseSettings):
    model_config = ConfigDict(env_file='.env', env_prefix='CRAWLER_')

    start_urls: List[str]
    allowed_domains: List[str]
    download_delay: float = 2.0
    concurrent_requests: int = 8
    max_pages: Optional[int] = None

    @field_validator('concurrent_requests')
    @classmethod
    def validate_concurrent_requests(cls, v):
        if v < 1 or v > 32:
            raise ValueError('concurrent_requests must be between 1 and 32')
        return v

class PDFProcessorConfig(BaseSettings):
    model_config = ConfigDict(env_file='.env', env_prefix='PDF_')

    batch_size: int = Field(default=10, ge=1, le=100)
    extract_tables: bool = True
    extract_images: bool = False
    min_text_length: int = Field(default=50, ge=10)
    section_splitter: str = Field(default="blocks", pattern=r"^(blocks|simple|layout)$")

class EmbeddingConfig(BaseSettings):
    model_config = ConfigDict(env_file='.env', env_prefix='EMBEDDING_')

    model: str = "text-embedding-3-small"
    batch_size: int = Field(default=20, ge=1, le=100)
    dimensions: int = 1536
    api_key: str
    max_tokens_per_minute: int = Field(default=1000000, ge=10000)

class DocumentSection(BaseModel):
    content: str
    section_index: int
    section_type: Literal["heading", "paragraph", "list", "table", "image", "code"]
    embedding: Optional[List[float]] = None
    similarity_score: Optional[float] = Field(None, ge=0, le=1)
    content_hash: Optional[str] = None

    @field_validator("content")
    @classmethod
    def validate_content_length(cls, v):
        if len(v) < 50:
            raise ValueError("Section content too short")
        return v
```

### Задача 3.2: Подобрен PDF процесор

#### 🔴 КРИТИЧНО: Подобрена детекция на език
```python
# ✅ РЕШЕНИЕ: Използване на langdetect с fallback опции
from langdetect import detect, DetectorFactory
from langdetect.lang_detect_exception import LangDetectException
import logging

# Задаване на seed за консистентни резултати
DetectorFactory.seed = 0

class LanguageDetector:
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.min_text_length = 20
        self.supported_languages = {'bg', 'en', 'de', 'fr', 'es', 'it', 'ru'}

    def detect_language(self, text: str) -> str:
        """
        Подобрена детекция на език с fallback стратегии
        За по-висока точност може да се използва Google Translate API или Azure Text Analytics
        """
        if not text or len(text.strip()) < self.min_text_length:
            return "unknown"

        # Почистване на текста
        cleaned_text = text.strip()[:1000]  # Първите 1000 символа за по-бърза обработка

        try:
            detected_lang = detect(cleaned_text)

            # Проверка дали езикът е в поддържаните
            if detected_lang in self.supported_languages:
                return detected_lang
            else:
                self.logger.warning(f"Detected unsupported language: {detected_lang}")
                return "other"

        except LangDetectException as e:
            self.logger.warning(f"Language detection failed: {e}")

            # Fallback стратегия - проверка за кирилица (български/руски)
            if self._contains_cyrillic(cleaned_text):
                return "bg"  # Предполагаме български за кирилица

            return "unknown"

    def _contains_cyrillic(self, text: str) -> bool:
        """Проверка за кирилски символи"""
        cyrillic_chars = sum(1 for char in text if '\u0400' <= char <= '\u04FF')
        return cyrillic_chars > len(text) * 0.1  # Поне 10% кирилски символи

# Алтернативно решение с облачни услуги (за production)
class CloudLanguageDetector:
    def __init__(self, service_type: str = "google"):
        self.service_type = service_type
        if service_type == "google":
            from google.cloud import translate_v2 as translate
            self.client = translate.Client()
        elif service_type == "azure":
            from azure.ai.textanalytics import TextAnalyticsClient
            from azure.core.credentials import AzureKeyCredential
            self.client = TextAnalyticsClient(
                endpoint=os.getenv("AZURE_TEXT_ENDPOINT"),
                credential=AzureKeyCredential(os.getenv("AZURE_TEXT_KEY"))
            )

    def detect_language(self, text: str) -> str:
        """Облачна детекция на език с висока точност"""
        try:
            if self.service_type == "google":
                result = self.client.detect_language(text)
                return result['language']
            elif self.service_type == "azure":
                response = self.client.detect_language(documents=[text])[0]
                return response.primary_language.iso6391_name
        except Exception as e:
            self.logger.error(f"Cloud language detection failed: {e}")
            # Fallback към локална детекция
            local_detector = LanguageDetector()
            return local_detector.detect_language(text)
```

#### Оптимизиран PDF процесор
```python
import fitz  # PyMuPDF
import pandas as pd
from typing import List, Dict, Any
import hashlib

class OptimizedPDFProcessor:
    def __init__(self, config: PDFProcessorConfig):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)

    def process_pdf(self, file_path: str) -> List[Dict[str, Any]]:
        """Оптимизирана обработка на PDF с таблици и метаданни"""
        with fitz.open(file_path) as doc:
            sections = []

            for page_num, page in enumerate(doc):
                # Извличане на текст по блокове за по-добра структура
                if self.config.section_splitter == "blocks":
                    blocks = page.get_text("blocks")
                    for block_idx, block in enumerate(blocks):
                        text = block[4]  # Текстово съдържание

                        if len(text.strip()) >= self.config.min_text_length:
                            section = {
                                "content": text.strip(),
                                "section_index": page_num * 1000 + block_idx,
                                "section_type": "paragraph",
                                "page": page_num + 1,
                                "content_hash": hashlib.sha256(text.encode()).hexdigest(),
                                "metadata": {
                                    "bbox": block[:4],  # Координати на блока
                                    "font_size": block[5] if len(block) > 5 else None
                                }
                            }
                            sections.append(section)

                # Извличане на таблици с подобрена обработка
                if self.config.extract_tables:
                    tables = page.find_tables()
                    for table_idx, table in enumerate(tables):
                        try:
                            # Конвертиране в DataFrame за по-добра обработка
                            df = table.to_pandas()
                            table_markdown = df.to_markdown(index=False)

                            section = {
                                "content": table_markdown,
                                "section_index": page_num * 1000 + 500 + table_idx,
                                "section_type": "table",
                                "page": page_num + 1,
                                "content_hash": hashlib.sha256(table_markdown.encode()).hexdigest(),
                                "metadata": {
                                    "table_shape": df.shape,
                                    "columns": df.columns.tolist(),
                                    "bbox": table.bbox
                                }
                            }
                            sections.append(section)
                        except Exception as e:
                            self.logger.warning(f"Failed to process table on page {page_num}: {e}")

            return sections
```

#### Кеширане на ембединги
```python
import redis
import json
import hashlib

class EmbeddingCache:
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_client = redis.from_url(redis_url)
        self.cache_ttl = 86400 * 7  # 7 дни

    def get_embedding(self, content: str) -> Optional[List[float]]:
        """Получаване на кеширан ембединг"""
        content_hash = hashlib.sha256(content.encode()).hexdigest()
        cached = self.redis_client.get(f"embedding:{content_hash}")

        if cached:
            return json.loads(cached)
        return None

    def set_embedding(self, content: str, embedding: List[float]):
        """Запазване на ембединг в кеша"""
        content_hash = hashlib.sha256(content.encode()).hexdigest()
        self.redis_client.setex(
            f"embedding:{content_hash}",
            self.cache_ttl,
            json.dumps(embedding)
        )
```

### Задача 3.3: Оптимизиран Batch Embedding процесор

```python
import asyncio
import time
from openai import AsyncOpenAI
from prometheus_client import Counter, Histogram, Gauge
from typing import List, Dict, Any, Optional
import tenacity

# Метрики
EMBEDDINGS_GENERATED = Counter('embeddings_generated_total', 'Total embeddings', ['status'])
EMBEDDING_CACHE_HITS = Counter('embedding_cache_hits_total', 'Cache hits')
EMBEDDING_API_CALLS = Counter('embedding_api_calls_total', 'API calls')
TOKEN_USAGE = Counter('openai_tokens_used_total', 'OpenAI tokens used')

class OptimizedEmbeddingProcessor:
    def __init__(self, config: EmbeddingConfig):
        self.config = config
        self.client = AsyncOpenAI(api_key=config.api_key)
        self.cache = EmbeddingCache()
        self.batch_size = config.batch_size
        self.model = config.model
        self.rate_limiter = asyncio.Semaphore(10)  # Ограничение на concurrent заявки
        self.tokens_used_this_minute = 0
        self.minute_start = time.time()

    async def check_rate_limit(self, estimated_tokens: int):
        """Проверка и спазване на rate limits"""
        current_time = time.time()

        # Нулиране на брояча всяка минута
        if current_time - self.minute_start >= 60:
            self.tokens_used_this_minute = 0
            self.minute_start = current_time

        # Изчакване ако сме близо до лимита
        if self.tokens_used_this_minute + estimated_tokens > self.config.max_tokens_per_minute:
            wait_time = 60 - (current_time - self.minute_start)
            if wait_time > 0:
                await asyncio.sleep(wait_time)
                self.tokens_used_this_minute = 0
                self.minute_start = time.time()

    @tenacity.retry(
        stop=tenacity.stop_after_attempt(3),
        wait=tenacity.wait_exponential(multiplier=1, min=4, max=10),
        retry=tenacity.retry_if_exception_type(Exception)
    )
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Генериране на ембединги с rate limiting и retry логика"""
        async with self.rate_limiter:
            # Оценка на токените (приблизително 4 символа = 1 токен)
            estimated_tokens = sum(len(text) // 4 for text in texts)
            await self.check_rate_limit(estimated_tokens)

            try:
                response = await self.client.embeddings.create(
                    model=self.model,
                    input=texts
                )

                # Актуализиране на метриките
                actual_tokens = response.usage.total_tokens
                self.tokens_used_this_minute += actual_tokens
                TOKEN_USAGE.inc(actual_tokens)
                EMBEDDING_API_CALLS.inc()
                EMBEDDINGS_GENERATED.labels(status='success').inc(len(texts))

                return [item.embedding for item in response.data]

            except Exception as e:
                EMBEDDINGS_GENERATED.labels(status='error').inc(len(texts))
                raise

    async def process_batch_with_cache(self, sections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Обработка на batch с кеширане"""
        cached_sections = []
        uncached_sections = []
        uncached_indices = []

        # Проверка за кеширани ембединги
        for i, section in enumerate(sections):
            cached_embedding = self.cache.get_embedding(section['content'])
            if cached_embedding:
                section['embedding'] = cached_embedding
                section['embedding_model'] = self.model
                cached_sections.append(section)
                EMBEDDING_CACHE_HITS.inc()
            else:
                uncached_sections.append(section)
                uncached_indices.append(i)

        # Генериране на ембединги за некеширани секции
        if uncached_sections:
            texts = [section['content'] for section in uncached_sections]
            embeddings = await self.generate_embeddings(texts)

            for i, (section, embedding) in enumerate(zip(uncached_sections, embeddings)):
                section['embedding'] = embedding
                section['embedding_model'] = self.model

                # Запазване в кеша
                self.cache.set_embedding(section['content'], embedding)

        # Обединяване на резултатите
        all_sections = cached_sections + uncached_sections
        return all_sections
```

## 📋 Фаза 4: MCP сървър

### Задача 4.1: FastAPI имплементация

#### Основни компоненти
- Pydantic модели за валидация на данни
- CORS middleware за cross-origin заявки
- Prometheus метрики за мониторинг
- Rate limiting за защита от злоупотреба
- Кеширане на резултати с Redis

#### Endpoints
```python
from fastapi import FastAPI, HTTPException, Request
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional

class SearchQuery(BaseModel):
    query: str
    limit: int = Field(default=5, ge=1, le=20)
    threshold: float = Field(default=0.7, ge=0, le=1.0)

class SearchResult(BaseModel):
    content: str
    source: str
    similarity: float
    metadata: Dict[str, Any] = {}

@app.post("/search", response_model=List[SearchResult])
async def search_documents(query: SearchQuery):
    # Кеширане на резултати
    cache_key = f"search:{hash(query.query)}:{query.limit}:{query.threshold}"
    cached_result = redis_client.get(cache_key)
    if cached_result:
        SEARCH_CACHE_HITS.inc()
        return json.loads(cached_result)

    # Генериране на ембединг за заявката
    query_embedding = generate_embedding(query.query)

    # Hybrid търсене (векторно + текстово)
    response = supabase.rpc("hybrid_search_documents", {
        "query_embedding": query_embedding,
        "query_text": query.query,
        "match_threshold": query.threshold,
        "match_count": query.limit,
        "vector_weight": 0.7,
        "text_weight": 0.3
    }).execute()

    results = format_search_results(response.data)

    # Кеширане за 5 минути
    redis_client.setex(cache_key, 300, json.dumps([r.dict() for r in results]))
    return results

@app.post("/mcp", response_model=MCPResponse)
async def mcp_endpoint(request: MCPRequest):
    # Маршрутизиране на заявката към правилния инструмент
    if request.tool == "search_documents":
        query = SearchQuery(**request.params)
        result = await search_documents(query)
        return MCPResponse(result=result)
    else:
        return MCPResponse(result=None, error=f"Unknown tool: {request.tool}")
```

#### 🔴 КРИТИЧНО: Rate limiting, кеширане и hybrid търсене

```python
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
import redis
import json
from prometheus_client import Counter

# Метрики
SEARCH_REQUESTS = Counter('search_requests_total', 'Total search requests')
SEARCH_CACHE_HITS = Counter('search_cache_hits_total', 'Search cache hits')
RATE_LIMIT_EXCEEDED = Counter('rate_limit_exceeded_total', 'Rate limit exceeded')

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
redis_client = redis.Redis(host='localhost', port=6379, db=0)

# 🔴 КРИТИЧНО: Оптимизирана Hybrid Search функция
HYBRID_SEARCH_SQL = """
-- Тригер за автоматично обновяване на tsvector колоната
CREATE OR REPLACE FUNCTION update_content_tsv()
RETURNS TRIGGER AS $$
BEGIN
    -- Динамично избиране на език за tsvector
    CASE NEW.language
        WHEN 'bg' THEN NEW.content_tsv := to_tsvector('simple', NEW.content);
        WHEN 'en' THEN NEW.content_tsv := to_tsvector('english', NEW.content);
        WHEN 'de' THEN NEW.content_tsv := to_tsvector('german', NEW.content);
        WHEN 'fr' THEN NEW.content_tsv := to_tsvector('french', NEW.content);
        ELSE NEW.content_tsv := to_tsvector('simple', NEW.content);
    END CASE;

    -- Генериране на content_hash
    NEW.content_hash := encode(sha256(NEW.content::bytea), 'hex');

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trg_update_content_tsv
BEFORE INSERT OR UPDATE ON document_sections
FOR EACH ROW
EXECUTE FUNCTION update_content_tsv();

-- Оптимизирана hybrid search функция
CREATE OR REPLACE FUNCTION hybrid_search_documents(
    query_embedding vector(1536),
    query_text text,
    query_language text DEFAULT 'english',
    match_threshold float DEFAULT 0.7,
    match_count int DEFAULT 10,
    vector_weight float DEFAULT 0.7,
    text_weight float DEFAULT 0.3
)
RETURNS TABLE (
    id uuid,
    content text,
    document_id uuid,
    section_type text,
    similarity float,
    text_rank float,
    combined_score float
) AS $$
DECLARE
    query_tsquery tsquery;
BEGIN
    -- Създаване на tsquery според езика
    CASE query_language
        WHEN 'english' THEN query_tsquery := plainto_tsquery('english', query_text);
        WHEN 'german' THEN query_tsquery := plainto_tsquery('german', query_text);
        WHEN 'french' THEN query_tsquery := plainto_tsquery('french', query_text);
        ELSE query_tsquery := plainto_tsquery('simple', query_text);
    END CASE;

    RETURN QUERY
    SELECT
        ds.id,
        ds.content,
        ds.document_id,
        ds.section_type,
        (1 - (ds.embedding <=> query_embedding))::float as similarity,
        ts_rank(ds.content_tsv, query_tsquery)::float as text_rank,
        (
            vector_weight * (1 - (ds.embedding <=> query_embedding)) +
            text_weight * ts_rank(ds.content_tsv, query_tsquery)
        )::float as combined_score
    FROM document_sections ds
    WHERE
        ds.content_tsv @@ query_tsquery  -- Бързо текстово търсене с GIN индекс
        AND (1 - (ds.embedding <=> query_embedding)) > match_threshold
        AND ds.is_embedded = true  -- Само секции с ембединги
    ORDER BY combined_score DESC
    LIMIT match_count;
END;
$$ LANGUAGE plpgsql;
"""

@app.post("/search")
@limiter.limit("30/minute")
async def search_documents(request: Request, query: SearchQuery):
    SEARCH_REQUESTS.inc()

    try:
        # Кеширане на резултати
        cache_key = f"search:{hash(query.query)}:{query.limit}:{query.threshold}"
        cached_result = redis_client.get(cache_key)
        if cached_result:
            SEARCH_CACHE_HITS.inc()
            return json.loads(cached_result)

        # Генериране на ембединг за заявката
        query_embedding = generate_embedding(query.query)

        # Hybrid търсене
        response = supabase.rpc("hybrid_search_documents", {
            "query_embedding": query_embedding,
            "query_text": query.query,
            "match_threshold": query.threshold,
            "match_count": query.limit
        }).execute()

        results = format_search_results(response.data)

        # Кеширане за 5 минути
        redis_client.setex(cache_key, 300, json.dumps([r.dict() for r in results]))
        return results

    except Exception as e:
        logger.error(f"Search error: {str(e)}")
        raise HTTPException(status_code=500, detail="Search failed")

@app.exception_handler(429)
async def rate_limit_handler(request: Request, exc):
    RATE_LIMIT_EXCEEDED.inc()
    return JSONResponse(
        status_code=429,
        content={"detail": "Rate limit exceeded. Please try again later."}
    )
```

## 📋 Фаза 5: CI/CD Pipeline и RAG Evaluation

### Задача 5.1: GitHub Actions CI/CD Pipeline

#### `.github/workflows/ci.yml`
```yaml
name: RAG System CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  PYTHON_VERSION: "3.11"
  POETRY_VERSION: "1.7.1"

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: pgvector/pgvector:pg15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_rag
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: ${{ env.POETRY_VERSION }}

    - name: Install dependencies
      run: |
        poetry install --with dev
        poetry run playwright install

    - name: Lint with flake8
      run: |
        poetry run flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        poetry run flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

    - name: Format check with black
      run: poetry run black --check .

    - name: Type check with mypy
      run: poetry run mypy .

    - name: Security check with bandit
      run: poetry run bandit -r . -x tests/

    - name: Run tests
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_rag
        REDIS_URL: redis://localhost:6379
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_SERVICE_KEY: ${{ secrets.SUPABASE_SERVICE_KEY }}
      run: |
        poetry run pytest tests/ -v --cov=. --cov-report=xml --cov-report=html

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: |
          ghcr.io/${{ github.repository }}:latest
          ghcr.io/${{ github.repository }}:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: staging

    steps:
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment"
        # Kubernetes deployment commands
        # kubectl apply -f k8s/staging/
        # helm upgrade --install rag-system ./helm-chart --namespace staging

  deploy-production:
    needs: deploy-staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - name: Deploy to production
      run: |
        echo "Deploying to production environment"
        # Production deployment with approval
```

### Задача 5.2: RAG Evaluation Framework

```python
import asyncio
import json
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
import pandas as pd
from ragas import evaluate
from ragas.metrics import (
    faithfulness,
    answer_relevancy,
    context_recall,
    context_precision,
    answer_similarity,
    answer_correctness
)
from datasets import Dataset

@dataclass
class EvaluationResult:
    query: str
    expected_answer: str
    retrieved_contexts: List[str]
    generated_answer: str
    faithfulness_score: float
    relevancy_score: float
    context_recall_score: float
    context_precision_score: float
    similarity_score: float
    correctness_score: float
    timestamp: datetime

class RAGEvaluator:
    def __init__(self, mcp_client, evaluation_dataset_path: str):
        self.mcp_client = mcp_client
        self.evaluation_dataset = self.load_evaluation_dataset(evaluation_dataset_path)
        self.results = []

    def load_evaluation_dataset(self, path: str) -> List[Dict[str, Any]]:
        """Зареждане на evaluation dataset"""
        with open(path, 'r', encoding='utf-8') as f:
            return json.load(f)

    async def evaluate_single_query(self, test_case: Dict[str, Any]) -> EvaluationResult:
        """Оценка на една заявка"""
        query = test_case['query']
        expected_answer = test_case['expected_answer']
        expected_contexts = test_case.get('expected_contexts', [])

        # Извличане на контекст от RAG системата
        search_results = await self.mcp_client.search_documents({
            'query': query,
            'limit': 5,
            'threshold': 0.7
        })

        retrieved_contexts = [result['content'] for result in search_results]

        # Генериране на отговор (тук трябва да интегрирате вашия LLM)
        generated_answer = await self.generate_answer(query, retrieved_contexts)

        # Подготовка на данни за RAGAS
        dataset = Dataset.from_dict({
            'question': [query],
            'answer': [generated_answer],
            'contexts': [retrieved_contexts],
            'ground_truth': [expected_answer]
        })

        # Оценка с RAGAS метрики
        result = evaluate(
            dataset,
            metrics=[
                faithfulness,
                answer_relevancy,
                context_recall,
                context_precision,
                answer_similarity,
                answer_correctness
            ]
        )

        return EvaluationResult(
            query=query,
            expected_answer=expected_answer,
            retrieved_contexts=retrieved_contexts,
            generated_answer=generated_answer,
            faithfulness_score=result['faithfulness'][0],
            relevancy_score=result['answer_relevancy'][0],
            context_recall_score=result['context_recall'][0],
            context_precision_score=result['context_precision'][0],
            similarity_score=result['answer_similarity'][0],
            correctness_score=result['answer_correctness'][0],
            timestamp=datetime.now()
        )

    async def generate_answer(self, query: str, contexts: List[str]) -> str:
        """Генериране на отговор от LLM (заместете с вашия LLM)"""
        # Примерна имплементация с OpenAI
        from openai import AsyncOpenAI
        client = AsyncOpenAI()

        context_text = "\n\n".join(contexts)
        prompt = f"""
        Въз основа на следния контекст, отговори на въпроса:

        Контекст:
        {context_text}

        Въпрос: {query}

        Отговор:
        """

        response = await client.chat.completions.create(
            model="gpt-4",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=500,
            temperature=0.1
        )

        return response.choices[0].message.content

    async def run_full_evaluation(self) -> Dict[str, Any]:
        """Пълна оценка на RAG системата"""
        print(f"Starting evaluation with {len(self.evaluation_dataset)} test cases...")

        tasks = [
            self.evaluate_single_query(test_case)
            for test_case in self.evaluation_dataset
        ]

        results = await asyncio.gather(*tasks)
        self.results = results

        # Изчисляване на средни стойности
        avg_metrics = {
            'avg_faithfulness': sum(r.faithfulness_score for r in results) / len(results),
            'avg_relevancy': sum(r.relevancy_score for r in results) / len(results),
            'avg_context_recall': sum(r.context_recall_score for r in results) / len(results),
            'avg_context_precision': sum(r.context_precision_score for r in results) / len(results),
            'avg_similarity': sum(r.similarity_score for r in results) / len(results),
            'avg_correctness': sum(r.correctness_score for r in results) / len(results),
            'total_queries': len(results),
            'evaluation_date': datetime.now().isoformat()
        }

        return avg_metrics

    def save_results(self, output_path: str):
        """Запазване на резултатите"""
        results_data = []
        for result in self.results:
            results_data.append({
                'query': result.query,
                'expected_answer': result.expected_answer,
                'generated_answer': result.generated_answer,
                'faithfulness': result.faithfulness_score,
                'relevancy': result.relevancy_score,
                'context_recall': result.context_recall_score,
                'context_precision': result.context_precision_score,
                'similarity': result.similarity_score,
                'correctness': result.correctness_score,
                'timestamp': result.timestamp.isoformat()
            })

        df = pd.DataFrame(results_data)
        df.to_csv(output_path, index=False)
        print(f"Results saved to {output_path}")

# Автоматизирана оценка в CI/CD
class ContinuousEvaluation:
    def __init__(self, evaluator: RAGEvaluator, thresholds: Dict[str, float]):
        self.evaluator = evaluator
        self.thresholds = thresholds

    async def run_ci_evaluation(self) -> bool:
        """Оценка за CI/CD pipeline"""
        metrics = await self.evaluator.run_full_evaluation()

        # Проверка на прагове
        failed_metrics = []
        for metric, threshold in self.thresholds.items():
            if metrics.get(f'avg_{metric}', 0) < threshold:
                failed_metrics.append(f"{metric}: {metrics.get(f'avg_{metric}', 0):.3f} < {threshold}")

        if failed_metrics:
            print("❌ Evaluation failed:")
            for failure in failed_metrics:
                print(f"  - {failure}")
            return False
        else:
            print("✅ Evaluation passed all thresholds")
            return True

# Примерен evaluation dataset (evaluation_dataset.json)
EXAMPLE_EVALUATION_DATASET = [
    {
        "query": "Какви са изискванията за кандидатстване по програма Хоризонт Европа?",
        "expected_answer": "Изискванията включват регистрация в Participant Portal, валиден ПИК номер, финансова стабилност и съответствие с критериите за допустимост.",
        "expected_contexts": [
            "Програма Хоризонт Европа изисква от кандидатите да имат валидна регистрация...",
            "Критериите за допустимост включват финансова стабилност и технически капацитет..."
        ]
    },
    {
        "query": "Какъв е максималният размер на финансирането за МСП?",
        "expected_answer": "Максималният размер на финансирането за МСП варира според програмата, но обикновено е между 50,000 и 2,500,000 евро.",
        "expected_contexts": [
            "За малки и средни предприятия максималното финансиране може да достигне до 2.5 милиона евро...",
            "Размерът на финансирането зависи от типа проект и програмата..."
        ]
    }
]

# Използване в тестове
async def test_rag_evaluation():
    # Запазване на примерен dataset
    with open('tests/evaluation_dataset.json', 'w', encoding='utf-8') as f:
        json.dump(EXAMPLE_EVALUATION_DATASET, f, ensure_ascii=False, indent=2)

    # Инициализиране на evaluator
    evaluator = RAGEvaluator(
        mcp_client=None,  # Заместете с реален клиент
        evaluation_dataset_path='tests/evaluation_dataset.json'
    )

    # Дефиниране на прагове
    thresholds = {
        'faithfulness': 0.8,
        'relevancy': 0.75,
        'context_recall': 0.7,
        'context_precision': 0.7,
        'similarity': 0.8,
        'correctness': 0.75
    }

    # Непрекъсната оценка
    continuous_eval = ContinuousEvaluation(evaluator, thresholds)
    success = await continuous_eval.run_ci_evaluation()

    assert success, "RAG evaluation failed to meet thresholds"
```

## 📋 Фаза 6: Оркестрация и мониторинг

### Задача 5.1: Оптимизирани Prefect работни потоци

```python
from prefect import flow, task, get_run_logger
from prefect.concurrency import RateLimit
from prefect.task_runners import ConcurrentTaskRunner
import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any

@task(
    retries=3,
    retry_delay_seconds=60,
    cache_key_fn=lambda context, parameters: f"crawl_{hash(str(parameters['site_configs']))}"
)
async def crawl_websites_batch(site_configs: List[Dict]) -> Dict[str, Any]:
    """Идемпотентен краулинг на batch от сайтове"""
    logger = get_run_logger()
    logger.info(f"Crawling {len(site_configs)} websites")

    # Проверка за вече краулнати страници в последните 24 часа
    from datetime import datetime, timedelta
    cutoff_time = datetime.now() - timedelta(hours=24)

    new_site_configs = []
    for config in site_configs:
        for url in config.get('start_urls', []):
            # Проверка дали URL-ът е краулван наскоро
            existing = supabase.table('raw_pages') \
                .select('id, fetch_time') \
                .eq('url', url) \
                .gte('fetch_time', cutoff_time.isoformat()) \
                .execute()

            if not existing.data:
                new_site_configs.append(config)
                break

    if not new_site_configs:
        logger.info("All sites already crawled recently, skipping")
        return {
            "crawled_pages": 0,
            "success_rate": 1.0,
            "skipped": len(site_configs),
            "timestamp": datetime.now().isoformat()
        }

    crawler_manager = CrawlerManager(new_site_configs)
    results = await crawler_manager.run_all()

    return {
        "crawled_pages": len(results),
        "success_rate": sum(1 for r in results if r.get('status') == 'success') / len(results) if results else 1.0,
        "skipped": len(site_configs) - len(new_site_configs),
        "timestamp": datetime.now().isoformat()
    }

@task(retries=2, retry_delay_seconds=30)
async def process_pdfs_batch(pdf_batch: List[str]) -> Dict[str, Any]:
    """Обработка на batch от PDF файлове"""
    logger = get_run_logger()
    logger.info(f"Processing {len(pdf_batch)} PDF files")

    pdf_processor = OptimizedPDFProcessor(config)
    processed_sections = []

    for pdf_path in pdf_batch:
        try:
            sections = pdf_processor.process_pdf(pdf_path)
            processed_sections.extend(sections)
        except Exception as e:
            logger.error(f"Failed to process {pdf_path}: {e}")

    return {
        "processed_files": len(pdf_batch),
        "extracted_sections": len(processed_sections),
        "sections": processed_sections
    }

@task(retries=3, retry_delay_seconds=120)
async def generate_embeddings_batch(sections: List[Dict]) -> Dict[str, Any]:
    """Генериране на ембединги за batch от секции"""
    logger = get_run_logger()
    logger.info(f"Generating embeddings for {len(sections)} sections")

    embedding_processor = OptimizedEmbeddingProcessor(config)
    processed_sections = await embedding_processor.process_batch_with_cache(sections)

    return {
        "processed_sections": len(processed_sections),
        "cache_hit_rate": embedding_processor.cache_hit_rate,
        "api_calls_made": embedding_processor.api_calls_count
    }

@task(cache_key_fn=lambda context, parameters: f"save_db_{hash(str(parameters['sections_with_embeddings']))}")
async def save_to_database(sections_with_embeddings: List[Dict]) -> Dict[str, Any]:
    """Идемпотентно запазване на секциите с ембединги в базата данни"""
    logger = get_run_logger()

    saved_count = 0
    updated_count = 0
    skipped_count = 0

    for section in sections_with_embeddings:
        try:
            content_hash = section.get('content_hash')
            if not content_hash:
                # Генериране на hash ако липсва
                import hashlib
                content_hash = hashlib.sha256(section['content'].encode()).hexdigest()
                section['content_hash'] = content_hash

            # Проверка за съществуващи записи
            existing = supabase.table('document_sections') \
                .select('id, is_embedded') \
                .eq('content_hash', content_hash) \
                .execute()

            if not existing.data:
                # Нов запис
                section['is_embedded'] = True
                supabase.table('document_sections').insert(section).execute()
                saved_count += 1
            elif not existing.data[0].get('is_embedded', False):
                # Актуализиране на съществуващ запис без ембединг
                supabase.table('document_sections') \
                    .update({
                        'embedding': section['embedding'],
                        'embedding_model': section['embedding_model'],
                        'is_embedded': True
                    }) \
                    .eq('id', existing.data[0]['id']) \
                    .execute()
                updated_count += 1
            else:
                # Записът вече съществува с ембединг
                skipped_count += 1

        except Exception as e:
            logger.error(f"Failed to save section: {e}")

    return {
        "saved_sections": saved_count,
        "updated_sections": updated_count,
        "skipped_sections": skipped_count
    }

@flow(
    name="optimized-rag-pipeline",
    task_runner=ConcurrentTaskRunner(),
    retries=1,
    retry_delay_seconds=300
)
async def optimized_rag_pipeline():
    """Оптимизиран RAG pipeline с паралелна обработка"""
    logger = get_run_logger()
    logger.info("Starting optimized RAG pipeline")

    # Фаза 1: Паралелен краулинг на различни сайтове
    site_batches = chunk_sites_by_domain(config.crawler_sites, batch_size=3)
    crawl_results = await asyncio.gather(*[
        crawl_websites_batch(batch) for batch in site_batches
    ])

    # Фаза 2: Паралелна обработка на PDF файлове
    pdf_files = get_unprocessed_pdfs()
    pdf_batches = chunk_list(pdf_files, batch_size=5)

    pdf_results = await asyncio.gather(*[
        process_pdfs_batch(batch) for batch in pdf_batches
    ])

    # Събиране на всички секции
    all_sections = []
    for result in pdf_results:
        all_sections.extend(result.get('sections', []))

    # Фаза 3: Batch генериране на ембединги
    section_batches = chunk_list(all_sections, batch_size=20)

    embedding_results = await asyncio.gather(*[
        generate_embeddings_batch(batch) for batch in section_batches
    ])

    # Фаза 4: Запазване в базата данни
    all_sections_with_embeddings = []
    for result in embedding_results:
        all_sections_with_embeddings.extend(result.get('processed_sections', []))

    save_result = await save_to_database(all_sections_with_embeddings)

    # Обобщение на резултатите
    total_crawled = sum(r.get('crawled_pages', 0) for r in crawl_results)
    total_processed = sum(r.get('processed_files', 0) for r in pdf_results)
    total_saved = save_result.get('saved_sections', 0)

    logger.info(f"Pipeline completed: {total_crawled} pages crawled, "
               f"{total_processed} PDFs processed, {total_saved} sections saved")

    return {
        "crawled_pages": total_crawled,
        "processed_pdfs": total_processed,
        "saved_sections": total_saved,
        "completion_time": datetime.now().isoformat()
    }

# Планиране с различни честоти
@flow(name="incremental-crawl")
async def incremental_crawl_flow():
    """Инкрементален краулинг всеки час"""
    recent_sites = get_sites_for_incremental_crawl()
    return await crawl_websites_batch(recent_sites)

# Планиране на изпълнение
if __name__ == "__main__":
    # Основен pipeline - всеки ден в 3:00
    optimized_rag_pipeline.serve(
        name="daily-rag-pipeline",
        cron="0 3 * * *"
    )

    # Инкрементален краулинг - всеки час
    incremental_crawl_flow.serve(
        name="hourly-incremental-crawl",
        cron="0 * * * *"
    )
```

### Задача 5.2: Prometheus метрики

```python
from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry
import psutil

# Системни метрики
CPU_USAGE = Gauge('system_cpu_usage_percent', 'System CPU usage')
MEMORY_USAGE = Gauge('system_memory_usage_percent', 'System memory usage')
DISK_USAGE = Gauge('system_disk_usage_percent', 'System disk usage')

# Бизнес метрики
PAGES_CRAWLED = Counter('pages_crawled_total', 'Total pages crawled', ['status'])
EMBEDDINGS_GENERATED = Counter('embeddings_generated_total', 'Total embeddings', ['status'])
SEARCH_REQUESTS = Counter('search_requests_total', 'Total search requests')
SEARCH_LATENCY = Histogram('search_latency_seconds', 'Search request latency')

@app.middleware("http")
async def collect_metrics(request: Request, call_next):
    # Събиране на системни метрики
    CPU_USAGE.set(psutil.cpu_percent())
    MEMORY_USAGE.set(psutil.virtual_memory().percent)
    DISK_USAGE.set(psutil.disk_usage('/').percent)

    response = await call_next(request)
    return response

@app.get("/metrics")
async def get_metrics():
    """Endpoint за Prometheus метрики"""
    return Response(generate_latest(), media_type="text/plain")
```

## 📋 Фаза 6: Сигурност и управление на тайни

### Задача 6.1: Production-ready Secrets Management

#### Development Crypto Manager
```python
from cryptography.fernet import Fernet
import os

class CryptoManager:
    def __init__(self):
        key = os.getenv("ENCRYPTION_KEY")
        if not key:
            key = Fernet.generate_key().decode()
            print(f"Generated new encryption key: {key}")
            print("Add this to your .env file as ENCRYPTION_KEY")

        self.cipher = Fernet(key.encode() if isinstance(key, str) else key)

    def encrypt(self, text):
        if not text:
            return None
        return self.cipher.encrypt(text.encode()).decode()

    def decrypt(self, encrypted_text):
        if not encrypted_text:
            return None
        return self.cipher.decrypt(encrypted_text.encode()).decode()
```

#### Production Secrets Manager
```python
import os
import boto3
import hvac  # HashiCorp Vault
from typing import Optional, Dict, Any
import logging

class ProductionSecretsManager:
    def __init__(self, provider: str = "vault"):
        self.provider = provider
        self.logger = logging.getLogger(self.__class__.__name__)

        if provider == "vault":
            self.client = hvac.Client(
                url=os.getenv("VAULT_URL", "http://localhost:8200"),
                token=os.getenv("VAULT_TOKEN")
            )
        elif provider == "aws":
            self.client = boto3.client('secretsmanager')
        elif provider == "gcp":
            from google.cloud import secretmanager
            self.client = secretmanager.SecretManagerServiceClient()
        elif provider == "doppler":
            import doppler_sdk
            self.client = doppler_sdk.DopplerSDK()

    def get_secret(self, secret_name: str) -> Optional[str]:
        """Получаване на тайна от избрания provider"""
        try:
            if self.provider == "vault":
                response = self.client.secrets.kv.v2.read_secret_version(
                    path=secret_name
                )
                return response['data']['data']['value']

            elif self.provider == "aws":
                response = self.client.get_secret_value(SecretId=secret_name)
                return response['SecretString']

            elif self.provider == "gcp":
                name = f"projects/{os.getenv('GCP_PROJECT_ID')}/secrets/{secret_name}/versions/latest"
                response = self.client.access_secret_version(request={"name": name})
                return response.payload.data.decode("UTF-8")

            elif self.provider == "doppler":
                response = self.client.secrets.get(secret_name)
                return response['value']

        except Exception as e:
            self.logger.error(f"Failed to get secret {secret_name}: {e}")
            # Fallback към environment variable
            return os.getenv(secret_name)

    def set_secret(self, secret_name: str, secret_value: str) -> bool:
        """Задаване на тайна"""
        try:
            if self.provider == "vault":
                self.client.secrets.kv.v2.create_or_update_secret(
                    path=secret_name,
                    secret={'value': secret_value}
                )
                return True

            elif self.provider == "aws":
                self.client.create_secret(
                    Name=secret_name,
                    SecretString=secret_value
                )
                return True

        except Exception as e:
            self.logger.error(f"Failed to set secret {secret_name}: {e}")
            return False

# Конфигурация за различни среди
class EnvironmentConfig:
    def __init__(self, environment: str = "development"):
        self.environment = environment

        if environment == "production":
            self.secrets_manager = ProductionSecretsManager(
                provider=os.getenv("SECRETS_PROVIDER", "vault")
            )
        else:
            self.secrets_manager = CryptoManager()

    def get_database_url(self) -> str:
        if self.environment == "production":
            return self.secrets_manager.get_secret("database_url")
        else:
            return os.getenv("SUPABASE_URL")

    def get_openai_key(self) -> str:
        if self.environment == "production":
            return self.secrets_manager.get_secret("openai_api_key")
        else:
            return os.getenv("OPENAI_API_KEY")
```

### Задача 6.2: PII Detection и Data Governance

```python
import re
from typing import List, Dict, Any, Tuple
from presidio_analyzer import AnalyzerEngine
from presidio_anonymizer import AnonymizerEngine
import logging

class PIIDetector:
    def __init__(self):
        self.analyzer = AnalyzerEngine()
        self.anonymizer = AnonymizerEngine()
        self.logger = logging.getLogger(self.__class__.__name__)

        # Български специфични pattern-и
        self.bg_patterns = {
            'bg_egn': r'\b\d{10}\b',  # ЕГН
            'bg_phone': r'\b0[0-9]{8,9}\b',  # Български телефони
            'bg_iban': r'\bBG\d{2}[A-Z]{4}\d{14}\b'  # Български IBAN
        }

    def detect_pii(self, text: str, language: str = 'en') -> List[Dict[str, Any]]:
        """Откриване на лична информация в текст"""
        results = []

        # Стандартно откриване с Presidio
        analyzer_results = self.analyzer.analyze(
            text=text,
            language=language,
            entities=['PERSON', 'EMAIL_ADDRESS', 'PHONE_NUMBER', 'CREDIT_CARD', 'IBAN_CODE']
        )

        for result in analyzer_results:
            results.append({
                'entity_type': result.entity_type,
                'start': result.start,
                'end': result.end,
                'score': result.score,
                'text': text[result.start:result.end]
            })

        # Български специфични проверки
        if language == 'bg':
            for pattern_name, pattern in self.bg_patterns.items():
                matches = re.finditer(pattern, text)
                for match in matches:
                    results.append({
                        'entity_type': pattern_name.upper(),
                        'start': match.start(),
                        'end': match.end(),
                        'score': 0.9,
                        'text': match.group()
                    })

        return results

    def anonymize_text(self, text: str, language: str = 'en') -> Tuple[str, List[Dict]]:
        """Анонимизиране на текст"""
        pii_entities = self.detect_pii(text, language)

        if not pii_entities:
            return text, []

        # Конвертиране към Presidio формат
        analyzer_results = []
        for entity in pii_entities:
            from presidio_analyzer import RecognizerResult
            analyzer_results.append(
                RecognizerResult(
                    entity_type=entity['entity_type'],
                    start=entity['start'],
                    end=entity['end'],
                    score=entity['score']
                )
            )

        # Анонимизиране
        anonymized_result = self.anonymizer.anonymize(
            text=text,
            analyzer_results=analyzer_results
        )

        return anonymized_result.text, pii_entities

    def should_exclude_content(self, text: str, language: str = 'en') -> bool:
        """Проверка дали съдържанието трябва да бъде изключено поради PII"""
        pii_entities = self.detect_pii(text, language)

        # Изключване ако има повече от 3 PII entity или високо рискови типове
        high_risk_types = ['CREDIT_CARD', 'BG_EGN', 'IBAN_CODE']

        if len(pii_entities) > 3:
            return True

        for entity in pii_entities:
            if entity['entity_type'] in high_risk_types and entity['score'] > 0.8:
                return True

        return False

# Интеграция в ETL процеса
class PIIAwareProcessor(BaseProcessor):
    def __init__(self, config):
        super().__init__(config)
        self.pii_detector = PIIDetector()
        self.anonymize_pii = config.get('anonymize_pii', True)
        self.exclude_high_pii = config.get('exclude_high_pii', True)

    def process_content(self, content: str, language: str = 'en') -> Dict[str, Any]:
        """Обработка на съдържание с PII проверка"""

        # Проверка за изключване
        if self.exclude_high_pii and self.pii_detector.should_exclude_content(content, language):
            self.logger.warning("Content excluded due to high PII risk")
            return None

        # Анонимизиране ако е конфигурирано
        if self.anonymize_pii:
            anonymized_content, pii_entities = self.pii_detector.anonymize_text(content, language)

            return {
                'content': anonymized_content,
                'original_content': content,  # Запазване на оригинала за одит
                'pii_detected': len(pii_entities) > 0,
                'pii_entities': pii_entities,
                'anonymized': True
            }
        else:
            pii_entities = self.pii_detector.detect_pii(content, language)

            return {
                'content': content,
                'pii_detected': len(pii_entities) > 0,
                'pii_entities': pii_entities,
                'anonymized': False
            }
```

## 🔴 Критични проблеми и решения

### 1. Индексиране на pgvector
```sql
-- ❌ ПРОБЛЕМ: Остарял IVFFLAT
CREATE INDEX idx_document_sections_embedding ON document_sections
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- ✅ РЕШЕНИЕ: Оптимизиран HNSW индекс
CREATE INDEX idx_document_sections_embedding ON document_sections
USING hnsw (embedding vector_cosine_ops)
WITH (m = 32, ef_construction = 128, parallel_workers = 8);
```

### 2. Scrapy интеграция с asyncio
```python
# ❌ ПРОБЛЕМ: Твърде сложно смесване на Scrapy с asyncio
# Scrapy използва Twisted reactor, който конфликтира с asyncio event loop

# ✅ РЕШЕНИЕ 1: Използване на scrapy-asyncer за интеграция
import asyncio
from scrapy_asyncer import AsyncCrawlerRunner
from scrapy.utils.project import get_project_settings

class AsyncScrapyManager:
    def __init__(self):
        self.settings = get_project_settings()
        self.runner = AsyncCrawlerRunner(self.settings)

    async def run_spider(self, spider_class, **kwargs):
        """Асинхронно изпълнение на Scrapy spider"""
        try:
            await self.runner.crawl(spider_class, **kwargs)
            return {"status": "success", "spider": spider_class.name}
        except Exception as e:
            return {"status": "error", "error": str(e)}

# ✅ РЕШЕНИЕ 2: Изпълнение на Scrapy в отделен процес
import subprocess
import json
from concurrent.futures import ProcessPoolExecutor

class ProcessBasedScrapyManager:
    def __init__(self):
        self.executor = ProcessPoolExecutor(max_workers=4)

    async def run_spider_in_process(self, spider_name: str, **kwargs):
        """Изпълнение на Scrapy spider в отделен процес"""
        loop = asyncio.get_event_loop()

        def run_scrapy():
            cmd = [
                'scrapy', 'crawl', spider_name,
                '-s', 'JOBDIR=crawls/spider-1',  # За resume функционалност
                '-o', f'output/{spider_name}.json'
            ]

            # Добавяне на параметри
            for key, value in kwargs.items():
                cmd.extend(['-a', f'{key}={value}'])

            result = subprocess.run(cmd, capture_output=True, text=True)
            return {
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr
            }

        return await loop.run_in_executor(self.executor, run_scrapy)

# ✅ РЕШЕНИЕ 3: Хибриден подход с aiohttp за прости страници
import aiohttp
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse

class HybridCrawler:
    def __init__(self, config):
        self.config = config
        self.session = None
        self.scrapy_manager = AsyncScrapyManager()

    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={'User-Agent': self.get_random_user_agent()}
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def crawl_simple_page(self, url: str) -> dict:
        """Краулинг на прости HTML страници с aiohttp"""
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')

                    return {
                        "url": url,
                        "title": soup.title.string if soup.title else "",
                        "content": soup.get_text(strip=True),
                        "links": [urljoin(url, a.get('href')) for a in soup.find_all('a', href=True)],
                        "status": "success"
                    }
                else:
                    return {"url": url, "status": "error", "code": response.status}
        except Exception as e:
            return {"url": url, "status": "error", "error": str(e)}

    async def crawl_complex_site(self, spider_name: str, **kwargs):
        """Краулинг на сложни сайтове със Scrapy"""
        return await self.scrapy_manager.run_spider(spider_name, **kwargs)

    def get_random_user_agent(self):
        agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        ]
        import random
        return random.choice(agents)

# Използване на хибридния краулър
async def main():
    async with HybridCrawler(config) as crawler:
        # За прости страници
        simple_result = await crawler.crawl_simple_page("https://example.com")

        # За сложни сайтове
        complex_result = await crawler.crawl_complex_site("eufunds_spider", domain="eufunds.bg")
```

### 3. FastText модел за език
```python
# ❌ ПРОБЛЕМ: FastText модел не е включен в зависимостите
self.lang_model = fasttext.load_model('lid.176.ftz')  # Липсва модел

# ✅ РЕШЕНИЕ: Използване на langdetect или Azure/Google API
from langdetect import detect
def detect_language(self, text: str) -> str:
    try:
        return detect(text)
    except:
        return "unknown"
```

## 🟡 Структурни подобрения

### 1. Подобрена схема с партициониране
```sql
-- Добавяне на партициониране по дати
CREATE TABLE document_sections (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID REFERENCES documents(id),
    content TEXT NOT NULL,
    section_index INTEGER,
    section_type VARCHAR(20),
    embedding VECTOR(1536),
    embedding_model TEXT DEFAULT 'text-embedding-3-small',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
) PARTITION BY RANGE (created_at);

-- Създаване на месечни партиции
CREATE TABLE document_sections_2025_01 PARTITION OF document_sections
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');
```

### 2. Подобрена обработка на PDF файлове
```python
from unstructured.partition.pdf import partition_pdf
from pdfplumber import PDF
import pymupdf4llm

class AdvancedPDFProcessor:
    def process_pdf(self, file_path: str) -> List[Dict]:
        sections = []

        # За структурирани PDF-и
        try:
            elements = partition_pdf(file_path)
            for element in elements:
                if element.text.strip():
                    sections.append({
                        "content": element.text,
                        "type": element.category,
                        "metadata": element.metadata.to_dict()
                    })
        except:
            # Fallback към pymupdf4llm за по-добро извличане
            md_text = pymupdf4llm.to_markdown(file_path)
            sections.append({
                "content": md_text,
                "type": "markdown",
                "metadata": {"source": "pymupdf4llm"}
            })

        return sections
```

### 3. Chunking стратегия
```python
from langchain.text_splitter import RecursiveCharacterTextSplitter

class SmartChunker:
    def __init__(self):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            separators=["\n\n", "\n", ".", "!", "?", ",", " ", ""]
        )

    def chunk_document(self, content: str) -> List[str]:
        return self.text_splitter.split_text(content)
```

## 📋 Контролен списък за имплементация

### 🔴 Незабавни действия:
- [ ] Сменете IVFFLAT с HNSW индекс
- [ ] Добавете proper error handling във всички async функции
- [ ] Имплементирайте Redis кеширане
- [ ] Добавете rate limiting
- [ ] Тествайте с малко количество данни първо

### 🟡 Средносрочни подобрения:
- [ ] Добавете партициониране на таблици
- [ ] Имплементирайте health checks
- [ ] Добавете comprehensive logging
- [ ] Създайте Docker compose файл за development

### 🟢 Дългосрочни цели:
- [ ] Добавете A/B тестване на различни embedding модели
- [ ] Имплементирайте auto-scaling за краулъри
- [ ] Добавете data lineage tracking
- [ ] Създайте dashboard за мониторинг

## 🚀 Стъпки за стартиране

### 1. Подготовка на средата
```bash
# Създаване на виртуална среда
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# Инсталиране на зависимости
pip install -r requirements.txt

# Копиране на конфигурацията
cp .env.example .env
# Редактиране на .env с вашите стойности
```

### 2. Инициализация на базата данни
```bash
# Стартиране на Supabase инициализация
python database/init_db.py

# Проверка на връзката
python -c "from database.db_manager import DatabaseManager; dm = DatabaseManager(); print('Connection OK')"
```

### 3. Тестване на компонентите
```bash
# Тестване на краулърите
python -m pytest tests/test_crawlers.py

# Тестване на PDF процесора
python -m pytest tests/test_pdf_processor.py

# Тестване на MCP сървъра
python -m pytest tests/test_mcp_server.py
```

### 4. Стартиране на системата
```bash
# Стартиране на MCP сървъра
uvicorn mcp_server.server:app --host 0.0.0.0 --port 8000

# Стартиране на Prefect агент
prefect agent start

# Планиране на работни потоци
python workflows/schedule_flows.py
```

## 📊 Мониторинг и поддръжка

### Разширени Prometheus метрики
```python
from prometheus_client import Counter, Histogram, Gauge, Info

# Бизнес метрики
PAGES_CRAWLED = Counter('pages_crawled_total', 'Total pages crawled', ['domain', 'status'])
EMBEDDINGS_GENERATED = Counter('embeddings_generated_total', 'Total embeddings', ['model', 'status'])
SEARCH_REQUESTS = Counter('search_requests_total', 'Total search requests', ['endpoint'])
SEARCH_LATENCY = Histogram('search_latency_seconds', 'Search latency', ['query_type'])
EMBEDDING_CACHE_HITS = Counter('embedding_cache_hits_total', 'Embedding cache hits')
PDF_PROCESSING_TIME = Histogram('pdf_processing_seconds', 'PDF processing time', ['file_size_mb'])

# Системни метрики
SYSTEM_CPU = Gauge('system_cpu_usage_percent', 'System CPU usage')
SYSTEM_MEMORY = Gauge('system_memory_usage_percent', 'System memory usage')
SYSTEM_DISK = Gauge('system_disk_usage_percent', 'System disk usage')
DATABASE_CONNECTIONS = Gauge('database_connections_active', 'Active DB connections')

# API метрики
OPENAI_API_CALLS = Counter('openai_api_calls_total', 'OpenAI API calls', ['model', 'status'])
OPENAI_TOKENS_USED = Counter('openai_tokens_used_total', 'OpenAI tokens used', ['model'])
SUPABASE_API_CALLS = Counter('supabase_api_calls_total', 'Supabase API calls', ['operation'])

# Качество на данните
DUPLICATE_CONTENT_DETECTED = Counter('duplicate_content_detected_total', 'Duplicate content')
CONTENT_QUALITY_SCORE = Histogram('content_quality_score', 'Content quality score')

# Информация за системата
SYSTEM_INFO = Info('rag_system_info', 'RAG system information')
SYSTEM_INFO.info({
    'version': '1.0.0',
    'embedding_model': 'text-embedding-3-small',
    'vector_dimensions': '1536'
})
```

### Comprehensive Health Checks
```python
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Dict, Any
import asyncio
import time

class HealthStatus(BaseModel):
    status: str
    timestamp: str
    checks: Dict[str, Any]
    version: str

@app.get("/health", response_model=HealthStatus)
async def comprehensive_health_check():
    """Comprehensive health check endpoint"""
    checks = {}
    overall_status = "healthy"

    # Supabase connection check
    try:
        start_time = time.time()
        supabase.table('documents').select('id').limit(1).execute()
        checks['supabase'] = {
            'status': 'healthy',
            'response_time_ms': round((time.time() - start_time) * 1000, 2)
        }
    except Exception as e:
        checks['supabase'] = {'status': 'unhealthy', 'error': str(e)}
        overall_status = "unhealthy"

    # OpenAI API check
    try:
        start_time = time.time()
        await openai_client.embeddings.create(
            model="text-embedding-3-small",
            input=["health check"]
        )
        checks['openai'] = {
            'status': 'healthy',
            'response_time_ms': round((time.time() - start_time) * 1000, 2)
        }
    except Exception as e:
        checks['openai'] = {'status': 'unhealthy', 'error': str(e)}
        overall_status = "degraded"

    # Redis connection check
    try:
        start_time = time.time()
        redis_client.ping()
        checks['redis'] = {
            'status': 'healthy',
            'response_time_ms': round((time.time() - start_time) * 1000, 2)
        }
    except Exception as e:
        checks['redis'] = {'status': 'unhealthy', 'error': str(e)}
        overall_status = "degraded"

    # System resources check
    import psutil
    cpu_percent = psutil.cpu_percent()
    memory_percent = psutil.virtual_memory().percent
    disk_percent = psutil.disk_usage('/').percent

    checks['system_resources'] = {
        'cpu_percent': cpu_percent,
        'memory_percent': memory_percent,
        'disk_percent': disk_percent,
        'status': 'healthy' if all([
            cpu_percent < 80,
            memory_percent < 85,
            disk_percent < 90
        ]) else 'warning'
    }

    if checks['system_resources']['status'] == 'warning':
        overall_status = "degraded"

    return HealthStatus(
        status=overall_status,
        timestamp=datetime.now().isoformat(),
        checks=checks,
        version="1.0.0"
    )

@app.get("/metrics/summary")
async def metrics_summary():
    """Summary of key metrics"""
    return {
        "total_documents": get_metric_value('documents_total'),
        "total_embeddings": get_metric_value('embeddings_generated_total'),
        "search_requests_24h": get_metric_value('search_requests_total', '24h'),
        "cache_hit_rate": calculate_cache_hit_rate(),
        "avg_search_latency_ms": get_metric_value('search_latency_seconds') * 1000,
        "system_health": "healthy"  # Based on health check
    }
```

### Structured Logging
```python
import logging
import json
from datetime import datetime
from typing import Any, Dict

class StructuredLogger:
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)

        # JSON formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)

        # File handler with rotation
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            'logs/rag_system.log',
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)

    def log_structured(self, level: str, message: str, **kwargs):
        """Log structured data as JSON"""
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'message': message,
            'level': level,
            **kwargs
        }

        if level == 'info':
            self.logger.info(json.dumps(log_data))
        elif level == 'error':
            self.logger.error(json.dumps(log_data))
        elif level == 'warning':
            self.logger.warning(json.dumps(log_data))

    def log_crawl_event(self, url: str, status: str, duration: float, **metadata):
        """Log crawling events"""
        self.log_structured(
            'info',
            'Crawl completed',
            event_type='crawl',
            url=url,
            status=status,
            duration_seconds=duration,
            metadata=metadata
        )

    def log_search_event(self, query: str, results_count: int, latency: float):
        """Log search events"""
        self.log_structured(
            'info',
            'Search completed',
            event_type='search',
            query_hash=hash(query),
            results_count=results_count,
            latency_ms=latency * 1000
        )
```

### Performance Monitoring Dashboard
```python
# Grafana dashboard configuration (JSON)
GRAFANA_DASHBOARD = {
    "dashboard": {
        "title": "RAG System Monitoring",
        "panels": [
            {
                "title": "Search Requests per Minute",
                "type": "graph",
                "targets": [
                    {
                        "expr": "rate(search_requests_total[1m])",
                        "legendFormat": "Requests/min"
                    }
                ]
            },
            {
                "title": "Embedding Generation Rate",
                "type": "graph",
                "targets": [
                    {
                        "expr": "rate(embeddings_generated_total[5m])",
                        "legendFormat": "Embeddings/5min"
                    }
                ]
            },
            {
                "title": "Cache Hit Rate",
                "type": "stat",
                "targets": [
                    {
                        "expr": "rate(embedding_cache_hits_total[5m]) / rate(embeddings_generated_total[5m])",
                        "legendFormat": "Cache Hit Rate"
                    }
                ]
            },
            {
                "title": "System Resources",
                "type": "graph",
                "targets": [
                    {"expr": "system_cpu_usage_percent", "legendFormat": "CPU %"},
                    {"expr": "system_memory_usage_percent", "legendFormat": "Memory %"},
                    {"expr": "system_disk_usage_percent", "legendFormat": "Disk %"}
                ]
            }
        ]
    }
}
```

## 🎯 Метрики за успех и KPI

### Технически KPI
| Метрика | Целева стойност | Критична стойност |
|---------|-----------------|-------------------|
| Точност на търсенето (MRR) | >0.85 | <0.70 |
| Време за обработка на PDF | <100ms/страница | >500ms/страница |
| Време за отговор на MCP | <500ms | >2000ms |
| Успешни краулинг заявки | >99.5% | <95% |
| Cache hit rate за ембединги | >70% | <40% |
| Uptime на системата | >99.9% | <99% |

### Бизнес KPI
| Метрика | Целева стойност |
|---------|-----------------|
| Намаляване на халюцинации | >60% |
| Релевантност на отговорите | >90% |
| Време за отговор на потребителски заявки | <2s |
| Покритие на домейни | >95% от целевите сайтове |

## 🚨 Критични предупреждения и ограничения

### pgvector ограничения
- **Максимална размерност**: 2000 (използваме 1536 за text-embedding-3-small)
- **Индекс размер**: HNSW индексите заемат значителна памет
- **Concurrent операции**: Заключване на таблици при паралелна обработка

### OpenAI API ограничения
- **Rate limits**: 1M токена/минута за tier 1
- **Квоти**: Мониторинг на използваните токени
- **Fallback стратегия**: Локални модели при достигане на лимит

### Supabase ограничения
- **Безплатен план**: 500MB база данни, 2GB bandwidth
- **Concurrent connections**: Максимум 60 за безплатен план
- **Storage**: 1GB за безплатен план

## 🔄 Миграционна стратегия

### Фаза 1: MVP (2-3 седмици)
- [ ] Основна структура на проекта
- [ ] Базов Scrapy краулър
- [ ] Проста PDF обработка
- [ ] Основен MCP сървър
- [ ] Базови тестове

### Фаза 2: Оптимизация (3-4 седмици)
- [ ] Асинхронни краулъри
- [ ] HNSW индекси
- [ ] Кеширане на ембединги
- [ ] Rate limiting
- [ ] Prometheus метрики

### Фаза 3: Production Ready (2-3 седмици)
- [ ] Hybrid търсене
- [ ] Comprehensive мониторинг
- [ ] Автоматизирани тестове
- [ ] CI/CD pipeline
- [ ] Документация

## 📚 Допълнителни ресурси

### Документация
- [Supabase pgvector Guide](https://supabase.com/docs/guides/database/extensions/pgvector)
- [OpenAI Embeddings API](https://platform.openai.com/docs/guides/embeddings)
- [Prefect Documentation](https://docs.prefect.io/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)

### Полезни библиотеки (актуализирани версии)
```python
# requirements-extended.txt
unstructured[pdf]==0.11.8   # Подобрена PDF обработка
langchain==0.1.0            # Text splitting и chunking
sentence-transformers==2.2.2  # Локални embedding модели
faiss-cpu==1.7.4           # Алтернатива за векторно търсене
redis==5.0.1               # Кеширане
celery==5.3.4              # Алтернатива за task queue

# Облачни услуги за детекция на език (опционално)
google-cloud-translate==3.12.1  # Google Translate API
azure-ai-textanalytics==5.3.0   # Azure Text Analytics

# Scrapy интеграция
scrapy-asyncer==0.1.0       # Scrapy + asyncio интеграция
scrapy-playwright==0.0.25   # Scrapy + Playwright
```

## 🚀 Scalable Crawling Architecture

### Kubernetes-based Crawler Workers

#### `k8s/crawler-deployment.yml`
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rag-crawler-workers
  namespace: rag-system
spec:
  replicas: 3
  selector:
    matchLabels:
      app: rag-crawler
  template:
    metadata:
      labels:
        app: rag-crawler
    spec:
      containers:
      - name: crawler-worker
        image: ghcr.io/your-org/rag-crawler:latest
        env:
        - name: WORKER_TYPE
          value: "crawler"
        - name: REDIS_URL
          value: "redis://redis-service:6379"
        - name: SUPABASE_URL
          valueFrom:
            secretKeyRef:
              name: rag-secrets
              key: supabase-url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: crawler-service
  namespace: rag-system
spec:
  selector:
    app: rag-crawler
  ports:
  - port: 8080
    targetPort: 8080
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: crawler-hpa
  namespace: rag-system
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: rag-crawler-workers
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

#### Message Queue-based Crawler Architecture
```python
import asyncio
import aioredis
import json
from typing import Dict, Any, List
from dataclasses import dataclass, asdict
from enum import Enum
import logging

class CrawlJobStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"

@dataclass
class CrawlJob:
    id: str
    url: str
    crawler_type: str  # 'html', 'pdf', 'js'
    priority: int = 1  # 1=high, 2=medium, 3=low
    max_retries: int = 3
    retry_count: int = 0
    status: CrawlJobStatus = CrawlJobStatus.PENDING
    metadata: Dict[str, Any] = None
    created_at: str = None
    started_at: str = None
    completed_at: str = None

class CrawlJobQueue:
    def __init__(self, redis_url: str):
        self.redis_url = redis_url
        self.redis = None
        self.logger = logging.getLogger(self.__class__.__name__)

        # Queue names по приоритет
        self.queues = {
            1: "crawl_queue:high",
            2: "crawl_queue:medium",
            3: "crawl_queue:low"
        }

        self.processing_queue = "crawl_queue:processing"
        self.completed_queue = "crawl_queue:completed"
        self.failed_queue = "crawl_queue:failed"

    async def connect(self):
        """Свързване към Redis"""
        self.redis = await aioredis.from_url(self.redis_url)

    async def disconnect(self):
        """Затваряне на връзката"""
        if self.redis:
            await self.redis.close()

    async def enqueue_job(self, job: CrawlJob) -> bool:
        """Добавяне на job в опашката"""
        try:
            queue_name = self.queues[job.priority]
            job_data = json.dumps(asdict(job))

            await self.redis.lpush(queue_name, job_data)
            self.logger.info(f"Enqueued job {job.id} to {queue_name}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to enqueue job {job.id}: {e}")
            return False

    async def dequeue_job(self) -> CrawlJob:
        """Извличане на job от опашката (по приоритет)"""
        try:
            # Проверка на опашките по приоритет
            for priority in sorted(self.queues.keys()):
                queue_name = self.queues[priority]

                # Атомично преместване от queue към processing
                job_data = await self.redis.brpoplpush(
                    queue_name,
                    self.processing_queue,
                    timeout=1
                )

                if job_data:
                    job_dict = json.loads(job_data)
                    job = CrawlJob(**job_dict)
                    job.status = CrawlJobStatus.IN_PROGRESS

                    self.logger.info(f"Dequeued job {job.id} from {queue_name}")
                    return job

            return None

        except Exception as e:
            self.logger.error(f"Failed to dequeue job: {e}")
            return None

    async def complete_job(self, job: CrawlJob, success: bool = True):
        """Маркиране на job като завършен"""
        try:
            job_data = json.dumps(asdict(job))

            # Премахване от processing queue
            await self.redis.lrem(self.processing_queue, 1, job_data)

            # Добавяне към съответната queue
            if success:
                job.status = CrawlJobStatus.COMPLETED
                await self.redis.lpush(self.completed_queue, job_data)
            else:
                if job.retry_count < job.max_retries:
                    # Retry
                    job.retry_count += 1
                    job.status = CrawlJobStatus.RETRYING
                    await self.enqueue_job(job)
                else:
                    # Failed permanently
                    job.status = CrawlJobStatus.FAILED
                    await self.redis.lpush(self.failed_queue, job_data)

            self.logger.info(f"Completed job {job.id} with status {job.status}")

        except Exception as e:
            self.logger.error(f"Failed to complete job {job.id}: {e}")

class DistributedCrawlerWorker:
    def __init__(self, worker_id: str, redis_url: str):
        self.worker_id = worker_id
        self.job_queue = CrawlJobQueue(redis_url)
        self.logger = logging.getLogger(f"CrawlerWorker-{worker_id}")
        self.running = False

        # Crawler instances
        self.crawlers = {
            'html': HybridCrawler({}),
            'pdf': AsyncPDFDownloader({}),
            'js': PlaywrightCrawler({})
        }

    async def start(self):
        """Стартиране на worker"""
        await self.job_queue.connect()
        self.running = True
        self.logger.info(f"Worker {self.worker_id} started")

        while self.running:
            try:
                # Извличане на job
                job = await self.job_queue.dequeue_job()

                if job:
                    await self.process_job(job)
                else:
                    # Няма jobs, изчакване
                    await asyncio.sleep(1)

            except Exception as e:
                self.logger.error(f"Worker error: {e}")
                await asyncio.sleep(5)

    async def stop(self):
        """Спиране на worker"""
        self.running = False
        await self.job_queue.disconnect()
        self.logger.info(f"Worker {self.worker_id} stopped")

    async def process_job(self, job: CrawlJob):
        """Обработка на конкретен job"""
        self.logger.info(f"Processing job {job.id}: {job.url}")

        try:
            crawler = self.crawlers.get(job.crawler_type)
            if not crawler:
                raise ValueError(f"Unknown crawler type: {job.crawler_type}")

            # Изпълнение на краулинг
            if job.crawler_type == 'html':
                result = await crawler.crawl_simple_page(job.url)
            elif job.crawler_type == 'pdf':
                result = await crawler.download_pdf(None, job.url)
            elif job.crawler_type == 'js':
                result = await crawler.crawl_page(job.url)

            # Запазване на резултата
            if result and result.get('status') == 'success':
                await self.save_crawl_result(job, result)
                await self.job_queue.complete_job(job, success=True)
            else:
                await self.job_queue.complete_job(job, success=False)

        except Exception as e:
            self.logger.error(f"Failed to process job {job.id}: {e}")
            await self.job_queue.complete_job(job, success=False)

    async def save_crawl_result(self, job: CrawlJob, result: Dict[str, Any]):
        """Запазване на резултата от краулинг"""
        # Тук интегрирайте със Supabase или друга база данни
        self.logger.info(f"Saved result for job {job.id}")

# Crawler Job Scheduler
class CrawlerScheduler:
    def __init__(self, redis_url: str):
        self.job_queue = CrawlJobQueue(redis_url)
        self.logger = logging.getLogger(self.__class__.__name__)

    async def schedule_site_crawl(self, site_config: Dict[str, Any]):
        """Планиране на краулинг на цял сайт"""
        await self.job_queue.connect()

        try:
            # Създаване на jobs за всеки URL
            for url in site_config.get('start_urls', []):
                job = CrawlJob(
                    id=f"crawl_{hash(url)}_{int(time.time())}",
                    url=url,
                    crawler_type=site_config.get('crawler_type', 'html'),
                    priority=site_config.get('priority', 2),
                    metadata=site_config.get('metadata', {})
                )

                await self.job_queue.enqueue_job(job)

            self.logger.info(f"Scheduled {len(site_config['start_urls'])} crawl jobs")

        finally:
            await self.job_queue.disconnect()

# Docker Compose за development
DOCKER_COMPOSE_CRAWLERS = """
version: '3.8'
services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  crawler-worker-1:
    build: .
    environment:
      - WORKER_ID=worker-1
      - REDIS_URL=redis://redis:6379
      - WORKER_TYPE=crawler
    depends_on:
      - redis
    restart: unless-stopped

  crawler-worker-2:
    build: .
    environment:
      - WORKER_ID=worker-2
      - REDIS_URL=redis://redis:6379
      - WORKER_TYPE=crawler
    depends_on:
      - redis
    restart: unless-stopped

  crawler-scheduler:
    build: .
    environment:
      - REDIS_URL=redis://redis:6379
      - WORKER_TYPE=scheduler
    depends_on:
      - redis
    restart: unless-stopped

volumes:
  redis_data:
"""
```

## 🔧 Миграционни стъпки за съществуващи проекти

### Стъпка 1: Актуализиране на зависимости
```bash
# Деинсталиране на стари версии
pip uninstall openai pydantic fasttext

# Инсталиране на нови версии
pip install openai>=1.6.0 pydantic>=2.5.0 pydantic-settings>=2.1.0 langdetect scrapy-asyncer

# Проверка на версиите
pip list | grep -E "(openai|pydantic|langdetect)"
```

### Стъпка 2: Миграция на Pydantic модели
```python
# Стар синтаксис (Pydantic V1)
from pydantic import BaseSettings, validator

class OldConfig(BaseSettings):
    api_key: str

    @validator('api_key')
    def validate_key(cls, v):
        return v

    class Config:
        env_file = '.env'

# Нов синтаксис (Pydantic V2)
from pydantic import BaseModel, field_validator, ConfigDict
from pydantic_settings import BaseSettings

class NewConfig(BaseSettings):
    model_config = ConfigDict(env_file='.env')

    api_key: str

    @field_validator('api_key')
    @classmethod
    def validate_key(cls, v):
        return v
```

### Стъпка 3: Актуализиране на OpenAI код
```python
# Стар синтаксис (openai < 1.0)
import openai
openai.api_key = "your-key"
response = openai.Embedding.create(
    model="text-embedding-ada-002",
    input=["text"]
)

# Нов синтаксис (openai >= 1.0)
from openai import AsyncOpenAI
client = AsyncOpenAI(api_key="your-key")
response = await client.embeddings.create(
    model="text-embedding-3-small",
    input=["text"]
)
```

### Мониторинг стек
```yaml
# docker-compose.monitoring.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
```

---

**Този обединен план интегрира всички критични подобрения, оптимизации и best practices за създаване на production-ready RAG система с високо качество, надеждност и производителност.**
```

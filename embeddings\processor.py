"""
Embedding Processor with OpenAI API and Redis Caching
Implements batch processing and caching to reduce API costs
"""
import asyncio
import hashlib
import json
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import redis.asyncio as redis
from openai import Async<PERSON>penA<PERSON>
from config.settings import settings


class EmbeddingProcessor:
    """
    Processes text embeddings with OpenAI API
    Features: batch processing, Redis caching, rate limiting, error handling
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Initialize OpenAI client
        self.openai_client = AsyncOpenAI(
            api_key=settings.openai_api_key
        )
        
        # Initialize Redis for caching
        self.redis_client = None
        self.cache_enabled = True
        
        # Configuration
        self.model = settings.openai_config.model
        self.batch_size = settings.openai_config.batch_size
        self.cache_ttl = settings.redis_config.cache_ttl
        
        # Rate limiting
        self.rate_limit_semaphore = asyncio.Semaphore(10)  # Max 10 concurrent requests
        self.last_request_time = 0
        self.min_request_interval = 0.1  # 100ms between requests
        
        self.logger.info(f"EmbeddingProcessor initialized with model {self.model}")
    
    async def initialize_redis(self):
        """Initialize Redis connection"""
        try:
            self.redis_client = redis.from_url(
                settings.redis_url,
                encoding="utf-8",
                decode_responses=True
            )
            
            # Test connection
            await self.redis_client.ping()
            self.logger.info("Redis connection established for embedding cache")
            
        except Exception as e:
            self.logger.warning(f"Redis connection failed, caching disabled: {e}")
            self.cache_enabled = False
    
    async def close(self):
        """Close connections"""
        if self.redis_client:
            await self.redis_client.close()
    
    def _generate_cache_key(self, text: str) -> str:
        """Generate cache key from text content"""
        content_hash = hashlib.sha256(text.encode()).hexdigest()
        return f"embedding:{self.model}:{content_hash}"
    
    async def _get_cached_embedding(self, text: str) -> Optional[List[float]]:
        """Get embedding from cache"""
        if not self.cache_enabled or not self.redis_client:
            return None
        
        try:
            cache_key = self._generate_cache_key(text)
            cached_data = await self.redis_client.get(cache_key)
            
            if cached_data:
                embedding_data = json.loads(cached_data)
                self.logger.debug(f"Cache hit for text hash {cache_key[-8:]}")
                return embedding_data['embedding']
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Cache retrieval error: {e}")
            return None
    
    async def _cache_embedding(self, text: str, embedding: List[float]):
        """Cache embedding with TTL"""
        if not self.cache_enabled or not self.redis_client:
            return
        
        try:
            cache_key = self._generate_cache_key(text)
            cache_data = {
                'embedding': embedding,
                'model': self.model,
                'timestamp': datetime.now().isoformat(),
                'text_length': len(text)
            }
            
            await self.redis_client.setex(
                cache_key,
                self.cache_ttl,
                json.dumps(cache_data)
            )
            
            self.logger.debug(f"Cached embedding for text hash {cache_key[-8:]}")
            
        except Exception as e:
            self.logger.warning(f"Cache storage error: {e}")
    
    async def _rate_limited_request(self):
        """Implement rate limiting between requests"""
        async with self.rate_limit_semaphore:
            current_time = asyncio.get_event_loop().time()
            time_since_last = current_time - self.last_request_time
            
            if time_since_last < self.min_request_interval:
                sleep_time = self.min_request_interval - time_since_last
                await asyncio.sleep(sleep_time)
            
            self.last_request_time = asyncio.get_event_loop().time()
    
    async def generate_embedding(self, text: str) -> Optional[List[float]]:
        """
        Generate embedding for single text
        Checks cache first, then calls OpenAI API
        """
        
        # Input validation
        if not text or not text.strip():
            self.logger.warning("Empty text provided for embedding")
            return None
        
        # Truncate if too long (OpenAI limit is ~8192 tokens)
        if len(text) > 8000:
            text = text[:8000]
            self.logger.warning("Text truncated to 8000 characters")
        
        # Check cache first
        cached_embedding = await self._get_cached_embedding(text)
        if cached_embedding:
            return cached_embedding
        
        try:
            # Rate limiting
            await self._rate_limited_request()
            
            # Call OpenAI API
            self.logger.debug(f"Generating embedding for text ({len(text)} chars)")
            
            response = await self.openai_client.embeddings.create(
                model=self.model,
                input=text
            )
            
            embedding = response.data[0].embedding
            
            # Cache the result
            await self._cache_embedding(text, embedding)
            
            self.logger.debug(f"Generated embedding with {len(embedding)} dimensions")
            return embedding
            
        except Exception as e:
            self.logger.error(f"Failed to generate embedding: {e}")
            return None
    
    async def generate_embeddings_batch(self, texts: List[str]) -> List[Optional[List[float]]]:
        """
        Generate embeddings for multiple texts in batches
        More efficient than individual requests
        """
        
        if not texts:
            return []
        
        self.logger.info(f"Processing {len(texts)} texts in batches of {self.batch_size}")
        
        all_embeddings = []
        
        # Process in batches
        for i in range(0, len(texts), self.batch_size):
            batch = texts[i:i + self.batch_size]
            batch_embeddings = await self._process_batch(batch)
            all_embeddings.extend(batch_embeddings)
            
            # Small delay between batches
            if i + self.batch_size < len(texts):
                await asyncio.sleep(0.5)
        
        self.logger.info(f"Completed batch processing: {len(all_embeddings)} embeddings generated")
        return all_embeddings
    
    async def _process_batch(self, texts: List[str]) -> List[Optional[List[float]]]:
        """Process a single batch of texts"""
        
        # Check cache for all texts in batch
        cache_results = []
        uncached_texts = []
        uncached_indices = []
        
        for i, text in enumerate(texts):
            if not text or not text.strip():
                cache_results.append(None)
                continue
            
            # Truncate if needed
            if len(text) > 8000:
                text = text[:8000]
                texts[i] = text  # Update the original list
            
            cached = await self._get_cached_embedding(text)
            if cached:
                cache_results.append(cached)
            else:
                cache_results.append(None)
                uncached_texts.append(text)
                uncached_indices.append(i)
        
        # If all were cached, return cache results
        if not uncached_texts:
            self.logger.debug(f"All {len(texts)} texts found in cache")
            return cache_results
        
        self.logger.debug(f"Cache miss for {len(uncached_texts)}/{len(texts)} texts")
        
        try:
            # Rate limiting
            await self._rate_limited_request()
            
            # Call OpenAI API for uncached texts
            response = await self.openai_client.embeddings.create(
                model=self.model,
                input=uncached_texts
            )
            
            # Extract embeddings
            new_embeddings = [data.embedding for data in response.data]
            
            # Cache new embeddings
            for text, embedding in zip(uncached_texts, new_embeddings):
                await self._cache_embedding(text, embedding)
            
            # Merge cached and new results
            result = cache_results.copy()
            for i, embedding in zip(uncached_indices, new_embeddings):
                result[i] = embedding
            
            return result
            
        except Exception as e:
            self.logger.error(f"Batch embedding failed: {e}")
            
            # Return cached results and None for failed ones
            result = cache_results.copy()
            for i in uncached_indices:
                if result[i] is None:
                    result[i] = None
            
            return result
    
    async def process_document_sections(self, sections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process document sections and add embeddings
        Returns sections with embedding data added
        """
        
        if not sections:
            return []
        
        self.logger.info(f"Processing embeddings for {len(sections)} document sections")
        
        # Extract texts for embedding
        texts = [section.get('content', '') for section in sections]
        
        # Generate embeddings
        embeddings = await self.generate_embeddings_batch(texts)
        
        # Add embeddings to sections
        processed_sections = []
        for section, embedding in zip(sections, embeddings):
            processed_section = section.copy()
            
            if embedding:
                processed_section['embedding'] = embedding
                processed_section['embedding_model'] = self.model
                processed_section['embedding_status'] = 'completed'
                processed_section['is_embedded'] = True
            else:
                processed_section['embedding_status'] = 'failed'
                processed_section['is_embedded'] = False
                processed_section['embedding_error'] = 'Failed to generate embedding'
            
            processed_section['processed_at'] = datetime.now().isoformat()
            processed_sections.append(processed_section)
        
        successful = sum(1 for s in processed_sections if s.get('is_embedded', False))
        self.logger.info(f"Embedding processing completed: {successful}/{len(sections)} successful")
        
        return processed_sections
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        if not self.cache_enabled or not self.redis_client:
            return {'cache_enabled': False}
        
        try:
            # Get cache info
            info = await self.redis_client.info('memory')
            
            # Count embedding keys
            embedding_keys = await self.redis_client.keys('embedding:*')
            
            return {
                'cache_enabled': True,
                'total_keys': len(embedding_keys),
                'memory_used': info.get('used_memory_human', 'unknown'),
                'cache_ttl': self.cache_ttl,
                'model': self.model
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get cache stats: {e}")
            return {'cache_enabled': False, 'error': str(e)}

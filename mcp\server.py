from fastapi import FastAP<PERSON>, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import logging
import os
import json
import time
from dotenv import load_dotenv
from supabase import create_client
import redis.asyncio as redis
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from functools import lru_cache

# Модели за данни
class SearchQuery(BaseModel):
    query: str
    limit: int = Field(default=5, ge=1, le=20)
    threshold: float = Field(default=0.7, ge=0, le=1.0)

class SearchResult(BaseModel):
    content: str
    source: str
    similarity: float
    metadata: Dict[str, Any] = {}

class MCPRequest(BaseModel):
    tool: str
    params: Dict[str, Any]

class MCPResponse(BaseModel):
    result: Any
    error: Optional[str] = None

# Инициализация на FastAPI
app = FastAPI(title="MCP Server", description="Model Context Protocol Server for RAG")

# Rate limiting
limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Логгер
logger = logging.getLogger("mcp_server")

# Зареждане на конфигурация
load_dotenv()
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
redis_url = os.getenv("REDIS_URL", "redis://localhost:6379")
cache_ttl = int(os.getenv("CACHE_TTL", "300"))  # 5 минути по подразбиране

# Клиенти
supabase = create_client(supabase_url, supabase_key)
redis_client = redis.from_url(redis_url)

# Embedding клиент
from openai import OpenAI
openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# Функция за генериране на ембединг
async def generate_embedding(text: str) -> List[float]:
    response = openai_client.embeddings.create(
        model="text-embedding-3-small",
        input=text
    )
    return response.data[0].embedding

# Endpoint за търсене на документи
@app.post("/search", response_model=List[SearchResult])
@limiter.limit("30/minute")
async def search_documents(request: Request, query: SearchQuery):
    try:
        # Проверка за кеширан резултат
        cache_key = f"search:{hash(query.query)}:{query.limit}:{query.threshold}"
        cached_result = await redis_client.get(cache_key)
        
        if cached_result:
            logger.info(f"Cache hit for query: {query.query}")
            return json.loads(cached_result)
        
        # Генериране на ембединг за заявката
        query_embedding = await generate_embedding(query.query)
        
        # Векторно търсене в Supabase
        response = supabase.rpc(
            "match_documents",
            {
                "query_embedding": query_embedding,
                "match_threshold": query.threshold,
                "match_count": query.limit
            }
        ).execute()
        
        results = []
        for item in response.data:
            # Извличане на информация за документа
            doc_response = supabase.table("documents").select("*").eq("id", item["document_id"]).execute()
            doc_info = doc_response.data[0] if doc_response.data else {}
            
            result = SearchResult(
                content=item["content"],
                source=doc_info.get("source", "unknown"),
                similarity=item["similarity"],
                metadata={
                    "document_id": item["document_id"],
                    "section_type": item["section_type"],
                    "document_name": doc_info.get("name", "")
                }
            )
            results.append(result)
        
        # Кеширане на резултата
        await redis_client.setex(cache_key, cache_ttl, json.dumps([r.dict() for r in results]))
        
        return results
    
    except Exception as e:
        logger.error(f"Error in search_documents: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# MCP endpoint
@app.post("/mcp", response_model=MCPResponse)
@limiter.limit("50/minute")
async def mcp_endpoint(request: Request, mcp_request: MCPRequest):
    try:
        # Маршрутизиране на заявката към правилния инструмент
        if mcp_request.tool == "search_documents":
            query = SearchQuery(**mcp_request.params)
            result = await search_documents(request, query)
            return MCPResponse(result=result)
        
        elif mcp_request.tool == "list_programs":
            # Кеширане за list_programs
            cache_key = "list_programs"
            cached_result = await redis_client.get(cache_key)
            
            if cached_result:
                return MCPResponse(result=json.loads(cached_result))
            
            response = supabase.table("documents").select("name, source").execute()
            
            # Кеширане на резултата за 1 час
            await redis_client.setex(cache_key, 3600, json.dumps(response.data))
            
            return MCPResponse(result=response.data)
        
        else:
            return MCPResponse(result=None, error=f"Unknown tool: {mcp_request.tool}")
    
    except Exception as e:
        logger.error(f"Error in MCP endpoint: {str(e)}")
        return MCPResponse(result=None, error=str(e))

# Health check endpoint
@app.get("/health")
async def health_check():
    try:
        # Проверка на Supabase
        supabase_response = supabase.table("documents").select("count(*)", count="exact").execute()
        document_count = supabase_response.count
        
        # Проверка на Redis
        redis_ping = await redis_client.ping()
        
        return {
            "status": "healthy",
            "supabase": "connected",
            "redis": "connected" if redis_ping else "disconnected",
            "document_count": document_count
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "error": str(e)
        }

# Стартиране на сървъра
@app.on_event("startup")
async def startup_event():
    logger.info("Starting MCP server")

@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Shutting down MCP server")
    await redis_client.close()
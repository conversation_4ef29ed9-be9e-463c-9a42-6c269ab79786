#!/usr/bin/env python3
import os
from supabase import create_client, Client

def test_database_setup():
    """Тестване на database setup"""
    
    # Зареждане на credentials
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ Missing Supabase credentials")
        return False
    
    try:
        # Създаване на клиент
        supabase: Client = create_client(supabase_url, supabase_key)
        
        # Тест на връзката
        result = supabase.table('raw_pages').select('*').limit(1).execute()
        print("✅ Database connection successful")
        
        # Тест на разширенията
        extensions_result = supabase.rpc('sql', {
            'query': "SELECT extname FROM pg_extension WHERE extname IN ('vector', 'pg_cron');"
        }).execute()
        
        extensions = [row['extname'] for row in extensions_result.data]
        
        if 'vector' in extensions:
            print("✅ pgvector extension active")
        else:
            print("❌ pgvector extension missing")
            return False
            
        # Тест на таблиците
        tables_result = supabase.rpc('sql', {
            'query': """
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('raw_pages', 'document_sections', 'documents');
            """
        }).execute()
        
        tables = [row['table_name'] for row in tables_result.data]
        expected_tables = ['raw_pages', 'document_sections', 'documents']
        
        for table in expected_tables:
            if table in tables:
                print(f"✅ Table {table} exists")
            else:
                print(f"❌ Table {table} missing")
                return False
        
        print("✅ All database tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_database_setup()
    exit(0 if success else 1)

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your-service-key-here

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Server Configuration
SERVER_HOST=0.0.0.0
SERVER_PORT=8000
LOG_LEVEL=INFO

# Crawling Configuration
CRAWLER_CONCURRENT_REQUESTS=5
CRAWLER_DELAY_MIN=1.0
CRAWLER_DELAY_MAX=3.0

# Embedding Configuration
EMBEDDING_BATCH_SIZE=20
EMBEDDING_CACHE_TTL=3600

# Monitoring
PROMETHEUS_ENABLED=true
METRICS_PORT=9090
CRAWLER_DELAY=2
CRAWLER_USER_AGENTS=["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15"]
CRAWLER_RESPECT_ROBOTS_TXT=true

# MCP Server Configuration
MCP_SERVER_PORT=8000
MCP_SERVER_HOST=0.0.0.0
MCP_RATE_LIMIT=100
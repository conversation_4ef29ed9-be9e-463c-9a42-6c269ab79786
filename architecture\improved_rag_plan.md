# Подобрен архитектурен план за RAG система

## 1. Архитектурен преглед

### 1.1 Medallion архитектура
- **Бронзов слой**: Сурови данни от краулинг (URLs, метаданни, референции към файлове)
- **Сребърен слой**: Обработени и почистени данни (текст, структурирано съдържание)
- **Златен слой**: Готови за използване данни с ембединги за RAG

### 1.2 Компоненти
- **Краулинг система**: Асинхронна, с поддръжка на HTML и JavaScript страници
- **ETL процеси**: Обработка на текст, PDF и таблици
- **Векторна база данни**: Supabase с pgvector
- **MCP сървър**: FastAPI имплементация на Model Context Protocol

## 2. Подобрения в базата данни

### 2.1 Оптимизирана схема с Supabase Storage

```sql
-- Бронзов слой (сурови данни)
CREATE TABLE raw_pages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  url TEXT NOT NULL,
  html_content TEXT,
  fetch_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB,
  is_processed BOOLEAN DEFAULT FALSE
);

CREATE TABLE raw_documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  url TEXT NOT NULL,
  file_path TEXT,  -- Път към файла в Supabase Storage
  file_type TEXT,
  storage_bucket TEXT DEFAULT 'documents',
  fetch_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB,
  is_processed BOOLEAN DEFAULT FALSE
);

-- Сребърен слой (обработени данни)
CREATE TABLE clean_pages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  raw_page_id UUID REFERENCES raw_pages(id),
  content TEXT NOT NULL,
  language TEXT,
  content_hash TEXT UNIQUE,
  processed_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);

-- Златен слой (крайни данни)
CREATE TABLE documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT,
  source TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB
);

CREATE TABLE document_sections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID REFERENCES documents(id),
  content TEXT NOT NULL,
  section_index INTEGER,
  section_type VARCHAR(20), -- heading/paragraph/list/table
  embedding VECTOR(1536),
  embedding_model TEXT DEFAULT 'text-embedding-3-small',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Индекси
CREATE INDEX idx_raw_pages_url ON raw_pages(url);
CREATE INDEX idx_raw_pages_processed ON raw_pages(is_processed);
CREATE INDEX idx_raw_documents_url ON raw_documents(url);
CREATE INDEX idx_raw_documents_processed ON raw_documents(is_processed);
CREATE INDEX idx_clean_pages_content_hash ON clean_pages(content_hash);
CREATE INDEX idx_document_sections_document_id ON document_sections(document_id);

-- Векторен индекс за бързо търсене
CREATE INDEX idx_document_sections_embedding ON document_sections USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);
```

### 2.2 Инициализация на Supabase Storage

```python
def init_storage():
    # Създаване на bucket за документи
    try:
        supabase.storage.create_bucket("documents", {"public": False})
        print("Created 'documents' storage bucket")
    except Exception as e:
        if "already exists" in str(e):
            print("Bucket 'documents' already exists")
        else:
            raise e
```

## 3. Подобрена краулинг система

### 3.1 Асинхронен Scrapy интегратор

```python
from scrapy.crawler import CrawlerRunner
from twisted.internet import reactor, defer
from twisted.internet.defer import inlineCallbacks
from scrapy.utils.log import configure_logging
from scrapy.utils.project import get_project_settings
import logging

class CrawlerManager:
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        configure_logging()
        self.runner = CrawlerRunner(get_project_settings())
    
    @inlineCallbacks
    def run_html_crawlers(self):
        """Изпълнява всички HTML краулъри асинхронно"""
        self.logger.info("Starting HTML crawlers")
        
        for site_config in self.config.get('sites', []):
            self.logger.info(f"Starting crawler for {site_config.get('name', 'unnamed')}")
            yield self.runner.crawl(HtmlSpider, **site_config)
        
        self.logger.info("All HTML crawlers completed")
        reactor.stop()
    
    def start(self):
        """Стартира процеса на краулинг"""
        try:
            reactor.callWhenRunning(self.run_html_crawlers)
            reactor.run()
        except Exception as e:
            self.logger.error(f"Error in crawler manager: {str(e)}")
```

### 3.2 Асинхронен PDF даунлоудър

```python
import asyncio
import aiohttp
import os
from dotenv import load_dotenv
from supabase import create_client
import logging
from urllib.parse import urlparse
import tenacity
from prometheus_client import Counter, Histogram

# Метрики
PDF_DOWNLOADS = Counter('pdf_downloads_total', 'Total PDF downloads', ['status'])
PDF_DOWNLOAD_TIME = Histogram('pdf_download_seconds', 'Time to download PDF')

class AsyncPDFDownloader:
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Supabase клиент
        load_dotenv()
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        # Конфигурация
        self.pdf_urls = config.get('pdf_urls', [])
        self.concurrent_limit = config.get('concurrent_limit', 5)
        self.semaphore = asyncio.Semaphore(self.concurrent_limit)
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        ]
    
    def get_random_user_agent(self):
        """Връща случаен User-Agent"""
        return random.choice(self.user_agents)
    
    @tenacity.retry(
        stop=tenacity.stop_after_attempt(3),
        wait=tenacity.wait_exponential(multiplier=1, min=4, max=10),
        retry=tenacity.retry_if_exception_type(
            (aiohttp.ClientError, asyncio.TimeoutError)
        )
    )
    async def download_pdf(self, session, url):
        """Изтегля PDF файл с retry логика"""
        async with self.semaphore:
            try:
                self.logger.info(f"Downloading PDF from {url}")
                
                with PDF_DOWNLOAD_TIME.time():
                    headers = {'User-Agent': self.get_random_user_agent()}
                    async with session.get(url, headers=headers, timeout=30) as response:
                        if response.status == 200 and 'application/pdf' in response.headers.get('Content-Type', ''):
                            content = await response.read()
                            
                            # Генериране на уникално име на файла
                            filename = f"{uuid.uuid4()}_{os.path.basename(urlparse(url).path)}"
                            file_path = f"pdfs/{filename}"
                            
                            # Запазване в Supabase Storage
                            self.supabase.storage.from_('documents').upload(
                                file_path, content
                            )
                            
                            # Запазване на метаданни в базата данни
                            data = {
                                'url': url,
                                'file_path': file_path,
                                'file_type': 'pdf',
                                'storage_bucket': 'documents',
                                'metadata': {
                                    'filename': filename,
                                    'content_type': response.headers.get('Content-Type'),
                                    'content_length': len(content)
                                }
                            }
                            
                            self.supabase.table('raw_documents').insert(data).execute()
                            self.logger.info(f"Saved PDF from {url} to {file_path}")
                            PDF_DOWNLOADS.labels(status='success').inc()
                        else:
                            self.logger.warning(f"Failed to download PDF from {url}: status {response.status}")
                            PDF_DOWNLOADS.labels(status='error').inc()
            except Exception as e:
                self.logger.error(f"Error downloading PDF from {url}: {str(e)}")
                PDF_DOWNLOADS.labels(status='error').inc()
                raise
    
    async def download_all(self):
        """Изтегля всички PDF файлове асинхронно"""
        self.logger.info(f"Starting download of {len(self.pdf_urls)} PDF files")
        
        async with aiohttp.ClientSession() as session:
            tasks = [
                self.download_pdf(session, url) 
                for url in self.pdf_urls
            ]
            await asyncio.gather(*tasks, return_exceptions=True)
        
        self.logger.info("PDF download completed")
```

### 3.3 Playwright краулър за JavaScript страници

```python
import asyncio
from playwright.async_api import async_playwright
import os
from dotenv import load_dotenv
from supabase import create_client
import logging
import random
import time
import tenacity
from prometheus_client import Counter, Histogram

# Метрики
JS_PAGES_CRAWLED = Counter('js_pages_crawled_total', 'Total JavaScript pages crawled', ['status'])
JS_CRAWL_TIME = Histogram('js_crawl_seconds', 'Time to crawl JavaScript page')

class PlaywrightCrawler:
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Supabase клиент
        load_dotenv()
        supabase_url = os.getenv("SUPABASE_URL")
        supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
        self.supabase = create_client(supabase_url, supabase_key)
        
        # Конфигурация
        self.urls = config.get('urls', [])
        self.wait_time = config.get('wait_time', 5)
        self.concurrent_limit = config.get('concurrent_limit', 3)
        self.semaphore = asyncio.Semaphore(self.concurrent_limit)
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        ]
    
    def get_random_user_agent(self):
        """Връща случаен User-Agent"""
        return random.choice(self.user_agents)
    
    @tenacity.retry(
        stop=tenacity.stop_after_attempt(3),
        wait=tenacity.wait_exponential(multiplier=1, min=4, max=10),
        retry=tenacity.retry_if_exception_type(Exception)
    )
    async def crawl_page(self, url):
        """Краулинг на една страница с Playwright и retry логика"""
        async with self.semaphore:
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page(user_agent=self.get_random_user_agent())
                
                try:
                    self.logger.info(f"Crawling JS page: {url}")
                    
                    with JS_CRAWL_TIME.time():
                        await page.goto(url, wait_until='networkidle', timeout=60000)
                        await page.wait_for_timeout(self.wait_time * 1000)
                        
                        # Извличане на HTML след изпълнение на JavaScript
                        html_content = await page.content()
                        title = await page.title()
                        
                        # Проверка за дубликати
                        response = self.supabase.table('raw_pages').select('id').eq('url', url).execute()
                        
                        if not response.data:
                            # Запазване в базата данни
                            data = {
                                'url': url,
                                'html_content': html_content,
                                'metadata': {
                                    'title': title,
                                    'js_rendered': True
                                },
                                'is_processed': False
                            }
                            
                            self.supabase.table('raw_pages').insert(data).execute()
                            self.logger.info(f"Saved JS page: {url}")
                            JS_PAGES_CRAWLED.labels(status='success').inc()
                        else:
                            self.logger.info(f"Duplicate JS URL found: {url}")
                            JS_PAGES_CRAWLED.labels(status='duplicate').inc()
                except Exception as e:
                    self.logger.error(f"Error crawling JS page {url}: {str(e)}")
                    JS_PAGES_CRAWLED.labels(status='error').inc()
                    raise
                finally:
                    await browser.close()
    
    async def crawl_all(self):
        """Краулинг на всички URL-и асинхронно"""
        self.logger.info(f"Starting crawling of {len(self.urls)} JavaScript pages")
        
        tasks = [self.crawl_page(url) for url in self.urls]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        self.logger.info("JavaScript crawling completed")
```

## 4. Подобрена ETL система

### 4.1 Валидация на конфигурацията с Pydantic

```python
from pydantic import BaseSettings, validator, Field
from typing import List, Optional, Dict, Any

class CrawlerConfig(BaseSettings):
    start_urls: List[str]
    allowed_domains: List[str]
    download_delay: float = 2.0
    concurrent_requests: int = 8
    max_pages: Optional[int] = None
    respect_robots_txt: bool = True
    
    @validator('concurrent_requests')
    def validate_concurrent_requests(cls, v):
        if v < 1 or v > 32:
            raise ValueError('concurrent_requests must be between 1 and 32')
        return v
    
    class Config:
        env_file = '.env'
        env_prefix = 'CRAWLER_'

class PDFProcessorConfig(BaseSettings):
    batch_size: int = Field(default=10, ge=1, le=100)
    extract_tables: bool = True
    extract_images: bool = False
    min_text_length: int = Field(default=50, ge=10)
    section_splitter: str = Field(default="blocks", regex="^(blocks|simple|layout)$")
    
    class Config:
        env_file = '.env'
        env_prefix = 'PDF_'

class EmbeddingConfig(BaseSettings):
    model: str = "text-embedding-3-small"
    batch_size: int = Field(default=20, ge=1, le=100)
    dimensions: int = 1536
    api_key: str
    
    class Config:
        env_file = '.env'
        env_prefix = 'EMBEDDING_'
```

### 4.2 Подобрен PDF процесор

```python
import fitz  # PyMuPDF
import os
import logging
import hashlib
import fasttext
import tenacity
from typing import List, Dict, Any, Optional
from prometheus_client import Counter, Histogram

# Метрики
PDF_PROCESSED = Counter('pdf_processed_total', 'Total PDFs processed', ['status'])
PDF_PROCESSING_TIME = Histogram('pdf_processing_seconds', 'Time to process PDF')

class PDFProcessor:
    def __init__(self, config: PDFProcessorConfig):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Зареждане на модел за детекция на език
        self.lang_model = fasttext.load_model('lid.176.ftz')
    
    def detect_language(self, text: str) -> str:
        """Бърза детекция на език с FastText"""
        if not text or len(text) < 20:
            return "unknown"
        
        prediction = self.lang_model.predict(text[:1000])[0][0]
        return prediction.replace('__label__', '')
    
    @tenacity.retry(
        stop=tenacity.stop_after_attempt(3),
        wait=tenacity.wait_exponential(multiplier=1, min=2, max=8),
        retry=tenacity.retry_if_exception_type(Exception)
    )
    def process_pdf(self, file_path: str) -> List[Dict[str, Any]]:
        """Обработва PDF файл и връща списък от секции"""
        self.logger.info(f"Processing PDF: {file_path}")
        
        with PDF_PROCESSING_TIME.time():
            try:
                doc = fitz.open(file_path)
                sections = []
                
                for page_idx, page in enumerate(doc):
                    # Извличане на текст по блокове за по-добра структура
                    if self.config.section_splitter == "blocks":
                        blocks = page.get_text("blocks")
                        for block_idx, block in enumerate(blocks):
                            text = block[4]  # Текстово съдържание на блока
                            
                            if len(text.strip()) >= self.config.min_text_length:
                                section = {
                                    "content": text.strip(),
                                    "section_index": page_idx * 1000 + block_idx,
                                    "section_type": "paragraph",
                                    "page": page_idx + 1,
                                    "language": self.detect_language(text)
                                }
                                sections.append(section)
                    
                    # Извличане на таблици, ако е конфигурирано
                    if self.config.extract_tables:
                        tables = page.find_tables()
                        for table_idx, table in enumerate(tables):
                            table_html = table.html
                            
                            section = {
                                "content": table_html,
                                "section_index": page_idx * 1000 + 500 + table_idx,
                                "section_type": "table",
                                "page": page_idx + 1,
                                "language": "html"  # Таблиците са в HTML формат
                            }
                            sections.append(section)
                
                self.logger.info(f"Extracted {len(sections)} sections from {file_path}")
                PDF_PROCESSED.labels(status='success').inc()
                return sections
            
            except Exception as e:
                self.logger.error(f"Error processing PDF {file_path}: {str(e)}")
                PDF_PROCESSED.labels(status='error').inc()
                raise
    
    def compute_content_hash(self, content: str) -> str:
        """Изчислява хеш на съдържанието за дедупликация"""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
```

### 4.3 Batch Embedding Processor

```python
import os
import logging
import asyncio
import tenacity
from typing import List, Dict, Any
from openai import AsyncOpenAI
from prometheus_client import Counter, Histogram, Gauge

# Метрики
EMBEDDINGS_GENERATED = Counter('embeddings_generated_total', 'Total embeddings generated', ['status'])
EMBEDDING_GENERATION_TIME = Histogram('embedding_generation_seconds', 'Time to generate embeddings')
EMBEDDING_QUEUE_SIZE = Gauge('embedding_queue_size', 'Size of embedding queue')

class EmbeddingProcessor:
    def __init__(self, config: EmbeddingConfig):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.client = AsyncOpenAI(api_key=config.api_key)
        self.batch_size = config.batch_size
        self.model = config.model
    
    @tenacity.retry(
        stop=tenacity.stop_after_attempt(3),
        wait=tenacity.wait_exponential(multiplier=1, min=4, max=10),
        retry=tenacity.retry_if_exception_type(Exception)
    )
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Генерира ембединги за списък от текстове"""
        if not texts:
            return []
        
        self.logger.info(f"Generating embeddings for {len(texts)} texts")
        
        with EMBEDDING_GENERATION_TIME.time():
            try:
                response = await self.client.embeddings.create(
                    model=self.model,
                    input=texts
                )
                
                embeddings = [item.embedding for item in response.data]
                EMBEDDINGS_GENERATED.labels(status='success').inc(len(texts))
                return embeddings
            
            except Exception as e:
                self.logger.error(f"Error generating embeddings: {str(e)}")
                EMBEDDINGS_GENERATED.labels(status='error').inc(len(texts))
                raise
    
    async def process_batch(self, sections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Обработва batch от секции и добавя ембединги"""
        texts = [section['content'] for section in sections]
        
        try:
            embeddings = await self.generate_embeddings(texts)
            
            # Добавяне на ембединги към секциите
            for i, section in enumerate(sections):
                section['embedding'] = embeddings[i]
                section['embedding_model'] = self.model
            
            return sections
        
        except Exception as e:
            self.logger.error(f"Error processing batch: {str(e)}")
            return []
    
    async def process_all(self, sections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Обработва всички секции на batches"""
        EMBEDDING_QUEUE_SIZE.set(len(sections))
        self.logger.info(f"Processing {len(sections)} sections in batches of {self.batch_size}")
        
        results = []
        
        for i in range(0, len(sections), self.batch_size):
            batch = sections[i:i + self.batch_size]
            processed_batch = await self.process_batch(batch)
            results.extend(processed_batch)
            
            # Актуализиране на метриката за размера на опашката
            EMBEDDING_QUEUE_SIZE.set(len(sections) - i - len(batch))
            
            # Малко забавяне между batch-овете за избягване на rate limiting
            await asyncio.sleep(0.5)
        
        self.logger.info(f"Completed processing {len(results)} sections with embeddings")
        return results
```

## 5. MCP сървър с FastAPI

### 5.1 MCP сървър имплементация

```python
from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import logging
import os
from dotenv import load_dotenv
from supabase import create_client
import numpy as np
from prometheus_client import Counter, Histogram
import time

# Метрики
MCP_REQUESTS = Counter('mcp_requests_total', 'Total MCP requests', ['endpoint'])
MCP_REQUEST_TIME = Histogram('mcp_request_seconds', 'MCP request processing time', ['endpoint'])

# Модели за данни
class SearchQuery(BaseModel):
    query: str
    limit: int = Field(default=5, ge=1, le=20)
    threshold: float = Field(default=0.7, ge=0, le=1.0)

class SearchResult(BaseModel):
    content: str
    source: str
    similarity: float
    metadata: Dict[str, Any] = {}

class MCPRequest(BaseModel):
    tool: str
    params: Dict[str, Any]

class MCPResponse(BaseModel):
    result: Any
    error: Optional[str] = None

# Инициализация на FastAPI
app = FastAPI(title="MCP Server", description="Model Context Protocol Server for RAG")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Supabase клиент
load_dotenv()
supabase_url = os.getenv("SUPABASE_URL")
supabase_key = os.getenv("SUPABASE_SERVICE_KEY")
supabase = create_client(supabase_url, supabase_key)

# Embedding клиент
from openai import OpenAI
openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# Логгер
logger = logging.getLogger("mcp_server")

# Middleware за измерване на време
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    endpoint = request.url.path
    MCP_REQUEST_TIME.labels(endpoint=endpoint).observe(process_time)
    return response

# Функция за генериране на ембединг
def generate_embedding(text: str) -> List[float]:
    response = openai_client.embeddings.create(
        model="text-embedding-3-small",
        input=text
    )
    return response.data[0].embedding

# Endpoint за търсене на документи
@app.post("/search", response_model=List[SearchResult])
async def search_documents(query: SearchQuery):
    MCP_REQUESTS.labels(endpoint="/search").inc()
    
    try:
        # Генериране на ембединг за заявката
        query_embedding = generate_embedding(query.query)
        
        # Векторно търсене в Supabase
        response = supabase.rpc(
            "match_documents",
            {
                "query_embedding": query_embedding,
                "match_threshold": query.threshold,
                "match_count": query.limit
            }
        ).execute()
        
        results = []
        for item in response.data:
            # Извличане на информация за документа
            doc_response = supabase.table("documents").select("*").eq("id", item["document_id"]).execute()
            doc_info = doc_response.data[0] if doc_response.data else {}
            
            result = SearchResult(
                content=item["content"],
                source=doc_info.get("source", "unknown"),
                similarity=item["similarity"],
                metadata={
                    "document_id": item["document_id"],
                    "section_type": item["section_type"],
                    "document_name": doc_info.get("name", "")
                }
            )
            results.append(result)
        
        return results
    
    except Exception as e:
        logger.error(f"Error in search_documents: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# MCP endpoint
@app.post("/mcp", response_model=MCPResponse)
async def mcp_endpoint(request: MCPRequest):
    MCP_REQUESTS.labels(endpoint="/mcp").inc()
    
    try:
        # Маршрутизиране на заявката към правилния инструмент
        if request.tool == "search_documents":
            query = SearchQuery(**request.params)
            result = await search_documents(query)
            return MCPResponse(result=result)
        
        elif request.tool == "list_programs":
            # Пример за друг инструмент
            response = supabase.table("documents").select("name, source").execute()
            return MCPResponse(result=response.data)
        
        else:
            return MCPResponse(result=None, error=f"Unknown tool: {request.tool}")
    
    except Exception as e:
        logger.error(f"Error in MCP endpoint: {str(e)}")
        return MCPResponse(result=None, error=str(e))

# Функция за инициализация на базата данни
def init_database():
    # Създаване на stored procedure за векторно търсене
    supabase.table("document_sections").select("*").limit(1).execute()
    
    # Забележка: В реална ситуация бихте използвали SQL редактора на Supabase
    # за създаване на stored procedure
    logger.info("Database initialized")

# Стартиране на сървъра
@app.on_event("startup")
async def startup_event():
    logger.info("Starting MCP server")
    init_database()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

## 6. Оркестрация и мониторинг

### 6.1 Prefect работен поток

```python
from prefect import flow, task
import asyncio
import logging
from datetime import datetime
from typing import List, Dict, Any
import os
from dotenv import load_dotenv
from supabase import create_client

# Импортиране на компоненти
from crawlers.crawler_manager import CrawlerManager
from crawlers.js_crawler import PlaywrightCrawler
from crawlers.pdf_downloader import AsyncPDFDownloader
from processors.pdf_processor import PDFProcessor
from processors.html_processor import HTMLProcessor
from embeddings
</augment_code_snippet>